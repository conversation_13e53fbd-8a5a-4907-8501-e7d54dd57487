<?php
/**
 * Created by PhpStorm.
 * User: neverland
 * Date: 2020/9/22
 * Time: 9:04 下午
 */
return [
    "shiyanjia_url" => env("CONSTANTS_SHIYANJIA_URL"),
    "notify_url" => env("CONSTANTS_NOTIFY_URL"),
    'report_service_url' => env('CONSTANTS_REPORT_SERVICE_URL'),
    "crm_url" => env("CONSTANTS_CRM_URL"),
    "activity_url" => env("CONSTANTS_ACTIVITY_URL"),
    //小程序的环境参数 正式版为 release，体验版为 trial，开发版为 develop
    "env_version" => env("CONSTANTS_ENV_VERSION"),
    //团体券包id
    "group_couponid" => env("CONSTANTS_GROUP_COUPONID"), //线上1248 测试792
    "ai_service_url" => env("CONSTANTS_AI_SERVICE_URL"),
    //阿里云市场调用参数
    "AppCode" => env("CONSTANTS_APPCODE"),
    "AppSecret" => env("CONSTANTS_APPSECRET"),
    "AppKey" => env("CONSTANTS_APPKEY"),
    "data_service_api" => env("CONSTANTS_DATA_SERVICE_API"), // 数据服务接口
    //下单补充认证成功赠送优惠券
    "order_supplementary_auth_give_couponid" => env("ORDER_SUPPLEMENTARY_AUTH_GIVE_COUPONID"),
    //认证审核通知角色id
    "auth_examine_notice_role_id"=>env("AUTH_EXAMINE_NOTICE_ROLE_ID"),
    //认证审核通知法务角色id
    "auth_examine_notice_Legal_role_id"=>env("AUTH_EXAMINE_NOTICE_LEGAL_ROLE_ID"),
    "crmapi_url" => env("CONSTANTS_CRMAPI_URL"),
    "crm_v2_url" => env("CONSTANTS_CRM_V2_URL"),
    "next_crm_url" => env("CONSTANTS_NEXT_CRM_URL"),
    'admin_url' => env('CONSTANTS_ADMIN_URL'),
    //乐橙直播appid
    "lc_appid" => env("LE_CHENG_APPID"),
    //乐橙直播appSecret
    "lc_app_secret" => env("LE_CHENG_APP_SECRET"),
    //乐橙请求url
    "lc_url" => env("LE_CHENG_URL"),
    //乐橙直播hls链接筛选关键字
    "lc_hls_keywords" => env("LE_HLS_KEYWORD"),
    //供应商url
    "supplier_url" => env("CONSTANTS_SUPPLIER_URL"),

    // 智能外呼配置
    "smart_call_url" => env("SMART_CALL_URL"),
    "smart_call_app_key" => env("SMART_CALL_APP_KEY"),
    "smart_call_app_secret" => env("SMART_CALL_APP_SECRET"),
    "smart_call_app_type" => env("SMART_CALL_APP_TYPE"),
];
