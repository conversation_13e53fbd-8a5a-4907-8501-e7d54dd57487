# 网易七鱼智能外呼系统使用说明

## 概述

本项目已集成网易七鱼智能外呼系统功能，采用分层架构设计：
- **通用组件**：放在 `yanqu-phplib` 中，包括认证、加密、HTTP客户端等
- **业务逻辑**：放在 `openapi` 项目中，包括环境校验、权限控制、业务处理等

## 架构设计

### yanqu-phplib 中的通用组件
```
src/SmartCall/
├── Auth/
│   └── SmartCallAuth.php          # 认证和签名工具
├── Crypto/
│   └── SmartCallAESUtil.php       # AES加密工具
└── Client/
    └── SmartCallClient.php        # 基础HTTP客户端
```

### openapi 项目中的业务组件
```
app/Services/SmartCall/
└── NetEaseSmartCallService.php    # 网易七鱼业务服务

app/Http/Controllers/
└── NetEaseSmartCallController.php # 网易七鱼控制器
```

## 环境和权限控制

### 环境校验逻辑
1. **生产环境**：直接通过，无需额外校验
2. **测试环境**：需要校验用户是否在白名单中
3. **其他环境**：不允许访问

### IP校验
- 所有请求都需要通过IP白名单校验
- 使用 `CheckIpUtil::checkIp()` 进行验证

### 用户白名单校验（测试环境）
- 使用 `CommonWhiteListUtil` 进行用户白名单校验
- 白名单类型：`type=1`

## 配置

### 1. 环境配置

在 `.env` 文件中添加以下配置：

```env
# 网易七鱼智能外呼系统配置
SMART_CALL_URL=https://b.163.com/open/api
SMART_CALL_APP_KEY=your_app_key_here
SMART_CALL_APP_SECRET=your_app_secret_here
SMART_CALL_APP_TYPE=your_channel_tag
```

### 2. yanqu-phplib 配置

确保 `yanqu-phplib` 中的智能外呼组件已正确配置。

## API接口

### 基础URL
- 生产环境：`https://your-domain.com/api/netease-smart-call`
- 测试环境：`https://test-domain.com/api/netease-smart-call`

### 客户管理

#### 导入客户信息
```http
POST /api/netease-smart-call/customers/import
Content-Type: application/json

{
    "customers": [
        {
            "name": "张三",
            "phone": "***********",
            "task_id": "task_123"
        }
    ],
    "account_id": 12345,           // 测试环境必填
    "encrypt_key": "****************"  // 可选，16位加密密钥
}
```

### 任务管理

#### 创建/更新外呼任务
```http
POST /api/netease-smart-call/task/save
Content-Type: application/json

{
    "taskName": "测试外呼任务",
    "botId": 123,
    "didNumbers": ["************"],
    "robotSeat": 1,
    "recall": 0,
    "account_id": 12345,           // 测试环境必填
    "startType": 0,
    "blacklistGroups": [{"id": 123}],
    "hangupSms": 0
}
```

#### 查询任务状态
```http
GET /api/netease-smart-call/task/status?task_id=123&account_id=12345
```

### 基础数据查询

#### 获取话术列表
```http
GET /api/netease-smart-call/bot/list?account_id=12345
```

#### 获取可用线路列表
```http
GET /api/netease-smart-call/did/list?account_id=12345
```

#### 获取意向标签组详情
```http
POST /api/netease-smart-call/intent/groups
Content-Type: application/json

{
    "group_ids": [123, 456],
    "account_id": 12345
}
```

#### 获取已审核短信模版列表
```http
GET /api/netease-smart-call/sms/templates?account_id=12345
```

#### 获取拦截规则列表
```http
GET /api/netease-smart-call/filter/rules?account_id=12345
```

### 工具接口

#### 获取环境信息
```http
GET /api/netease-smart-call/environment/info
```

#### 测试加密功能
```http
POST /api/netease-smart-call/test/encryption
Content-Type: application/json

{
    "data": "***********",
    "encrypt_key": "****************",
    "account_id": 12345
}
```

## 使用示例

### PHP代码示例

```php
use App\Services\SmartCall\NetEaseSmartCallService;

$service = new NetEaseSmartCallService();

// 导入客户（测试环境需要提供account_id）
$customers = [
    [
        'name' => '张三',
        'phone' => '***********',
        'task_id' => 'task_123'
    ]
];

try {
    $result = $service->importCustomers($customers, 12345);
    echo "导入成功\n";
} catch (\Exception $e) {
    echo "导入失败: " . $e->getMessage() . "\n";
}

// 创建任务
$taskData = [
    'taskName' => '测试任务',
    'botId' => 123,
    'didNumbers' => ['************'],
    'robotSeat' => 1,
    'recall' => 0
];

try {
    $task = $service->saveTask($taskData, 12345);
    echo "任务创建成功，ID: " . $task['taskId'] . "\n";
} catch (\Exception $e) {
    echo "任务创建失败: " . $e->getMessage() . "\n";
}
```

### JavaScript示例

```javascript
// 导入客户
const importCustomers = async () => {
    const response = await fetch('/api/netease-smart-call/customers/import', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            customers: [
                {
                    name: '张三',
                    phone: '***********',
                    task_id: 'task_123'
                }
            ],
            account_id: 12345
        })
    });
    
    const result = await response.json();
    console.log(result);
};

// 创建任务
const createTask = async () => {
    const response = await fetch('/api/netease-smart-call/task/save', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            taskName: '测试外呼任务',
            botId: 123,
            didNumbers: ['************'],
            robotSeat: 1,
            recall: 0,
            account_id: 12345
        })
    });
    
    const result = await response.json();
    console.log(result);
};
```

## 错误处理

### 常见错误

1. **环境权限错误**
   ```json
   {
       "status_code": 1001,
       "status_msg": "IP地址不在白名单中",
       "data": []
   }
   ```

2. **用户白名单错误**（测试环境）
   ```json
   {
       "status_code": 1001,
       "status_msg": "用户不在测试环境白名单中",
       "data": []
   }
   ```

3. **参数验证错误**
   ```json
   {
       "status_code": 1001,
       "status_msg": "taskName字段是必填的",
       "data": []
   }
   ```

### 错误处理建议

```php
try {
    $result = $service->saveTask($taskData, $accountId);
} catch (\Exception $e) {
    // 记录错误日志
    \Yanqu\YanquPhplib\YqLog\YqLog::logger('smart_call:error')
        ->error('操作失败', [
            'error' => $e->getMessage(),
            'account_id' => $accountId
        ]);
    
    // 返回错误响应
    return response()->json([
        'status_code' => 1001,
        'status_msg' => $e->getMessage(),
        'data' => []
    ], 500);
}
```

## 日志记录

系统自动记录以下日志：

- `smart_call:import_customers` - 客户导入日志
- `smart_call:save_task` - 任务保存日志
- `smart_call:test_env` - 测试环境校验日志
- `smart_call:batch_import` - 批量导入日志
- `smart_call:get_*` - 各种查询操作日志

## 部署注意事项

### 1. 环境配置
- 确保生产环境和测试环境的配置正确
- 验证IP白名单设置
- 确认用户白名单配置（测试环境）

### 2. yanqu-phplib 依赖
- 确保 `yanqu-phplib` 版本包含智能外呼组件
- 验证通用组件的配置和功能

### 3. 网络访问
- 确保服务器可以访问网易七鱼API
- 配置正确的防火墙规则

### 4. 监控和告警
- 监控API调用频率和成功率
- 设置错误告警机制
- 定期检查日志文件

## 测试验证

### 1. 环境信息测试
```bash
curl -X GET "https://your-domain.com/api/netease-smart-call/environment/info"
```

### 2. 加密功能测试
```bash
curl -X POST "https://your-domain.com/api/netease-smart-call/test/encryption" \
  -H "Content-Type: application/json" \
  -d '{
    "data": "***********",
    "encrypt_key": "****************",
    "account_id": 12345
  }'
```

### 3. 基础数据查询测试
```bash
curl -X GET "https://your-domain.com/api/netease-smart-call/bot/list?account_id=12345"
```

## 技术支持

如有问题，请：
1. 检查环境配置和权限设置
2. 查看系统日志文件
3. 验证网络连接和API访问
4. 联系技术支持团队

## 版本信息

- 版本：v2.0.0
- 更新日期：2025-07-10
- 架构：分层设计（yanqu-phplib + openapi）
- 兼容性：Laravel 5.x+, PHP 7.4+
