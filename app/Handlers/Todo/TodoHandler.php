<?php

namespace App\Handlers\Todo;

use App\Entities\Dts\DtsEvent;
use App\Entities\Todo\TodoDealDTO;
use App\Handlers\TodoHandlerInterface;
use App\Models\WechatMessage;
use Yanqu\YanquPhplib\Openapi\Todo\TodoParam;

abstract class TodoHandler implements TodoHandlerInterface {

    public function canTriggerCreateByDtsEvent (DtsEvent $dtsEvent): bool {
        return false;
    }

    public function getCreateTodoParam ($id): ?TodoParam {
        return null;
    }

    public function canTriggerDealByDtsEvent (DtsEvent $dtsEvent): bool {
        return false;
    }

    /**
     * 检查是否已存在相同的待办消息
     */
    protected function checkMessageExists ($otherId, $categoryName): bool {
        return !empty(WechatMessage::query()
            ->where("otherid", $otherId)
            ->where("categoryname", $categoryName)
            ->value("messageid"));
    }

    /**
     * 默认允许手动处理
     */
    public function checkCanDealManual (TodoDealDTO $dealDTO): bool {
        return true;
    }

    /**
     * 默认不允许自动处理
     */
    public function checkCanDealAuto (TodoDealDTO $dealDTO): bool {
        return false;
    }

    public function getOtherInfo (WechatMessage $message): array {
        return [];
    }

    public function afterDeal (TodoDealDTO $dto) {
        // 可选操作：将同一个任务的其他待办，标记为已完成
        WechatMessage::query()
            ->where("uuid", $dto->getMessage()->uuid)
            ->where("messageid", '!=', $dto->getMessage()->messageid)
            ->where("isdeal", 0)
            ->update(['isdeal' => 1, 'dealtime' => time()]);
    }

    /**
     * 转交待办以后处理
     * @return void
     */
    public function afterHandOver (WechatMessage $message) {
        //Do nothing
    }
} 