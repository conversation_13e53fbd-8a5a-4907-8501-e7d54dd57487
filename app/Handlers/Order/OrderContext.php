<?php

namespace App\Handlers\Order;

use App\Entities\PickupEntity;
use stdClass;

class OrderContext
{
    /**
     * @var stdClass $account
     */
    public $account;

    /**
     * @var int $oid
     */
    public $oid;

    /**
     * @var int $categoryId
     */
    public $categoryId;

    /**
     * @var int $providerproductid
     */
    public $providerproductid;

    /**
     * @var PickupEntity $pickupEntity
     */
    public $pickupEntity;

    /**
     * @var int $addressid
     */
    public $addressid;

    /**
     * 是否获取二维码，0否，1是
     * @var int $isGetQrCode
     */
    public $isGetQrCode;
}
