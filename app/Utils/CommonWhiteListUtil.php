<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2022/5/9
 */

namespace App\Utils;

class CommonWhiteListUtil
{
    public $api = '/dev/common/white_list';
    public $apiName = 'getData';
    public $group = 'aliyun';

    /**
     * @param $type
     * @param $id
     * @return array
     * @description 判断是不是白名单用户
     */
    public function getWhiteList($type = 1, $id = '')
    {
        $keyVal = [
            'type' => $type
        ];

        if ($id > 0) {
            $keyVal['value'] = $id;
        }
        $param = [
            'method' => 'GET',
            'api' => $this->api,
            'params' => $keyVal
        ];
        $result = DataService::getData($this->group, $this->apiName, 'POST', $param, true);
        if (isset($result['data'])) {
            file_put_contents("/data/logs/getWhiteList.log", json_encode(['time' => date('Y-m-d H:i:s'), 'accountId' => $id, 'data' => $result['data']]) . "\n", FILE_APPEND);
            $data = $result['data'];
            return isset($data['errCode']) && $data['errCode'] == 0 ? (isset($data['data'][0]['value']) ? $data['data'][0]['value'] : []) : [];
        } else {
            return [];
        }
    }

    /**
     * 检查用户是否在白名单中
     * @param int $type 白名单类型
     * @param string|int $id 用户ID或手机号
     * @return bool
     */
    public function isInWhiteList($type = 1, $id = '')
    {
        $whiteList = $this->getWhiteList($type, $id);
        return !empty($whiteList);
    }

    /**
     * 过滤客户列表，只保留白名单中的手机号
     * @param array $customers 客户列表
     * @param int $type 白名单类型，默认为1
     * @return array 过滤后的客户列表
     */
    public function filterCustomersByPhoneWhiteList(array $customers, $type = 1)
    {
        if (empty($customers)) {
            return [];
        }

        $filteredCustomers = [];
        foreach ($customers as $customer) {
            if (isset($customer['phone']) && $this->isInWhiteList($type, $customer['phone'])) {
                $filteredCustomers[] = $customer;
            }
        }

        return $filteredCustomers;
    }
}
