<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2023/9/28
 */

namespace App\Utils\OSS;

use OSS\OssClient;
use Exception;

class AliyunOSSUtil {

    private static $ossClient = null;

    public static function getConnection () {
        if (self::$ossClient == null) {
            self::$ossClient = new OssClient(
                config('oss.access_key_id'),
                config('oss.access_key_secret'),
                config('oss.end_point')
            );
        }

        return self::$ossClient;
    }

    /**
     * @param $url
     * @param $internal
     * @return array
     */
    public static function getURLBucketAndObject ($url, $internal = 0) {
        if ($internal == 1) {
            $ep = config('oss.intranet_end_point');
        } else {
            $ep = config('oss.end_point');
        }

        $urlList = explode($ep, $url);
        return ['bucket' => rtrim(explode('//', $urlList[0])[1], '.'), 'object' => ltrim($urlList[1], '/')];
    }

    /**
     * @param $fromBucket //源bucket
     * @param $toBucket //目标bucket
     * @param $fromObj //源文件
     * @param $toObj //目标文件
     * @return true
     * @throws \OSS\Core\OssException
     */
    public static function moveFile ($fromBucket, $fromObj, $toBucket, $toObj) {
        if (!self::getConnection()->doesObjectExist($fromBucket, $fromObj)) {
            throw new Exception('原文件不存在');
        }
        self::getConnection()->copyObject($fromBucket,$fromObj, $toBucket, $toObj);
        self::getConnection()->deleteObject($fromBucket, $fromObj);
        return self::getConnection()->doesObjectExist($toBucket, $toObj);
    }
}