<?php

namespace App\Utils;

use Illuminate\Support\Facades\App;
use Yanqu\YanquPhplib\Configuration;

class EnvironmentHelper
{
    /**
     * 检查是否为生产环境
     * 优先从.config.ini读取env参数，如果没有则使用Laravel的APP_ENV
     */
    public static function isProduction()
    {
        $configEnv = self::getConfigEnv();
        if ($configEnv !== null) {
            return $configEnv === 'prod';
        }
        return App::environment('production');
    }

    /**
     * 检查是否为测试环境
     */
    public static function isTest()
    {
        $configEnv = self::getConfigEnv();
        if ($configEnv !== null) {
            return $configEnv === 'test';
        }
        return App::environment('test');
    }

    /**
     * 检查是否为开发环境
     */
    public static function isDev()
    {
        $configEnv = self::getConfigEnv();
        if ($configEnv !== null) {
            return $configEnv === 'dev';
        }
        return App::environment('local');
    }

    public static function isLocal()
    {
        return self::isDev();
    }

    /**
     * 从.config.ini文件读取环境配置
     * @return string|null
     */
    private static function getConfigEnv()
    {
        try {
            $config = Configuration::getInstance();
            return $config->get('env');
        } catch (\Exception $e) {
            // 如果读取失败，返回null，使用默认的Laravel环境判断
            return null;
        }
    }

    /**
     * 获取当前环境名称
     * @return string
     */
    public static function getCurrentEnvironment()
    {
        $configEnv = self::getConfigEnv();
        if ($configEnv !== null) {
            return $configEnv;
        }
        return App::environment();
    }
}
