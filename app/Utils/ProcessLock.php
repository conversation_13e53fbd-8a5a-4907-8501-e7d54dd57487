<?php

namespace App\Utils;

use Illuminate\Support\Facades\Storage;

class ProcessLock
{
    protected static $fileHandler = false; // 文件句柄

    public static function doLock($cmdName)
    {
        $lockPath = storage_path() . "/command_locks/";
        if (!is_dir($lockPath)) {
            mkdir($lockPath);
        }
        
        $argv = array_slice($_SERVER['argv'], 2);
        $lockFile = $lockPath . $cmdName . implode(".", $argv);
        self::$fileHandler = fopen($lockFile, 'w'); // 执行文件锁
        if (false === self::$fileHandler) {
            exit("Can not create lock file {$lockFile}" . PHP_EOL);
        }
        if (!flock(self::$fileHandler, LOCK_EX + LOCK_NB)) {
            exit(date('Y-m-d H:i:s') . 'Process already exists.' . PHP_EOL);
        }
    }
}