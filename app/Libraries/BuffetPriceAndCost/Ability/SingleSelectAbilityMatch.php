<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2021/8/18
 */

namespace App\Libraries\BuffetPriceAndCost\Ability;

class SingleSelectAbilityMatch implements AbilityMatchInterface
{


    /**
     * @param $ask
     * @param $ability
     * @return int
     * @description 单选题的能力匹配
     */
    static public function match($ask, $ability)
    {
        // TODO: Implement match() method.
        $option = current($ask['items']);
        if(isset($ability[$option['optionuuid']]) && !empty($ability[$option['optionuuid']])){
            return 1;
        }
        return 0;
    }
}