<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2024/9/10
 */

namespace App\Validate\TypicalCase;

use App\Validate\BaseValidator;

class AbnormalReasonSaveValidator extends BaseValidator
{
    protected $rule = [
        'product_id' => 'required|integer',
        'parent_name' => 'required|string',
        'sub_name' => 'required|string',
    ];
    protected $message = [
        'product_id.required' => '测试项目不能为空',
        'parent_name.required' => '测试项目不能为空',
        'sub_name.required' => '测试项目不能为空',
    ];
}