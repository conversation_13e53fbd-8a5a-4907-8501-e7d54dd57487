<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2024/9/10
 */

namespace App\Validate\TypicalCase;

use App\Validate\BaseValidator;

class CaseSaveValidator extends BaseValidator
{
    protected $rule = [
        'belong' => 'required|integer',

        'case_info.oid' => 'required|integer',
        'case_info.buffet_id' => 'required|integer',
        'case_info.relation_products' => 'required|array',
        'case_info.relation_products.*.oid' => 'required|integer',
        'case_info.relation_products.*.product_id' => 'required|integer',
        'case_info.relation_products.*.sample_group' => 'required|string',
        'case_info.relation_products.*.test_condition' => 'required|array',
        'case_info.pattern_id' => 'required|string',
        'case_info.sample_group' => 'required|string',
        'case_info.standard_sample' => 'required|integer',
        'case_info.sample_ingredient' => 'required|string',
        'case_info.sample_category_id' => 'required|integer',
        'case_info.difficulty' => 'required|integer',

        'test_process.pretreated' => 'required|string',
        'test_process.step_or_params' => 'required|string',
        'test_process.pretreated_template_code' => 'required|array',
        'test_process.step_or_params_template_code' => 'required|array',

        'test_result.result_type' => 'required|integer',

        'refer_process_id',
    ];
    protected $message = [
        'belong.required' => '所属单位不能为空',

        'case_info.oid.required' => '订单ID不能为空',
        'case_info.buffet_id' => '下单商品不能为空',
        'case_info.relation_products' => '测试项目不能为空',
        'case_info.relation_products.*.oid.required' => '测试项目关联的订单不能为空',
        'case_info.relation_products.*.product_id.required' => '测试项目关联的项目不能为空',
        'case_info.relation_products.*.sample_group.required' => '测试项目关联的样品组不能为空',
        'case_info.relation_products.*.test_condition.required' => '测试项目关联的测试条件不能为空',
        'case_info.pattern_id.required' => '仪器型号不能为空',
        'case_info.sample_group.required' => '样品组不能为空',
        'case_info.standard_sample.required' => '是否为实验室标样不能为空',
        'case_info.sample_ingredient.required' => '样品成分不能为空',
        'case_info.sample_category_id.required' => '样品分类不能为空',
        'case_info.difficulty.required' => '测试难度不能为空',

        'test_process.pretreated.required' => '测试过程预处理信息不能为空',
        'test_process.step_or_params.required' => '测试过程步骤参数不能为空',
        'test_process.pretreated_template_code' => '制样/前处理方法 引用模板编号不能为空',
        'test_process.step_or_params_template_code' => '测试步骤/参数 引用模板编号不能为空',

        'test_result.result_type.required' => '测试结果是否异常不能为空',

        'refer_process_id' => '引用实验过程ID不能为空',
    ];
    protected $scene = [
        'LAB' => [
            'belong',

            'case_info.oid',
            'case_info.buffet_id',
            'case_info.relation_products',
            'case_info.relation_products.*.oid',
            'case_info.relation_products.*.product_id',
            'case_info.relation_products.*.sample_group',
            'case_info.pattern_id',
            'case_info.sample_group',

            'test_result.result_type',
        ],
        'CRM' => [
            'belong',

            'case_info.buffet_id',
            'case_info.relation_products',
            'case_info.relation_products.*.product_id',
            'case_info.relation_products.*.test_condition',
            'case_info.pattern_id',
            'case_info.standard_sample',
            'case_info.sample_ingredient',
            'case_info.sample_category_id',
            'case_info.difficulty',

            'test_process.pretreated',
            'test_process.step_or_params',
            'test_process.pretreated_template_code',
            'test_process.step_or_params_template_code',

            'test_result.result_type',
        ],
        'SUPPLIER' => [
            'belong',

            'case_info.oid',
            'case_info.buffet_id',
            'case_info.relation_products',
            'case_info.relation_products.*.oid',
            'case_info.relation_products.*.product_id',
            'case_info.relation_products.*.sample_group',
            'case_info.relation_products.*.test_condition',
            'case_info.sample_group',
            'case_info.pattern_id',
            'case_info.standard_sample',
            'case_info.sample_ingredient',
            'case_info.sample_category_id',
            'case_info.difficulty',

            'test_process.pretreated',
            'test_process.step_or_params',
            'test_process.pretreated_template_code',
            'test_process.step_or_params_template_code',

            'test_result.result_type',
        ],
        'DATA_CHECK' => [ //同CRM
            'belong',

            'case_info.buffet_id',
            'case_info.relation_products',
            'case_info.relation_products.*.product_id',
            'case_info.relation_products.*.test_condition',
            'case_info.pattern_id',
            'case_info.standard_sample',
            'case_info.sample_ingredient',
            'case_info.sample_category_id',
            'case_info.difficulty',

            'test_process.pretreated',
            'test_process.step_or_params',
            'test_process.pretreated_template_code',
            'test_process.step_or_params_template_code',

            'test_result.result_type',
        ],
        'LAB_REFER_PROCESS' => [
            'belong',

            'refer_process_id',
        ],
    ];

    public function saveForSubmit($isSubmit)
    {
        if (!$isSubmit) {
            $exceptKeys = [
                'case_info.standard_sample',
                'case_info.difficulty',
                'test_process.pretreated_template_code',
                'test_process.step_or_params_template_code',
                'test_result.result_type',
                'case_info.sample_ingredient',
                'case_info.sample_category_id',
            ];
            $this->only = array_values(array_diff($this->only, $exceptKeys));
        }
        return $this;
    }
}