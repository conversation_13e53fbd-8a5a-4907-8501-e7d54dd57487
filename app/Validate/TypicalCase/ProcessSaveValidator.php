<?php

namespace App\Validate\TypicalCase;

use App\Validate\BaseValidator;

class ProcessSaveValidator extends BaseValidator
{
    protected $rule = [
        // 默认信息
        'merchant_id' => 'required|integer',
        'origin' => 'required|integer',
        'operator' => 'required|integer',

        'id' => 'required|integer',
        'refer_case_id' => 'required|integer',
        'oid' => 'required|integer',
        'sample_group' => 'required|integer',
        'is_retest' => 'nullable|integer',
        'remark' => 'required|string',
        'result_data' => 'nullable|string',
    ];
    protected $message = [
        // 默认信息
        'merchant_id' => '商户ID不能为空',
        'origin' => '来源不能为空',
        'operator' => '操作人不能为空',

        'id' => 'ID不能为空',
        'refer_case_id' => '参考案例不能为空',
        'oid' => '订单ID不能为空',
        'sample_group' => '样品组不能为空',
        'is_retest' => '是否复测不能为空',
        'remark' => '备注不能为空',
        'result_data' => '测试结果数据不能为空',
    ];

    protected $scene = [
        'ADD' => [
            // 默认信息
            'merchant_id',
            'origin',
            'operator',

            'oid',
            'refer_case_id',
            'sample_group',
            'is_retest',
            'remark',
            'result_data',
        ],
        'EDIT' => [
            // 默认信息
            'merchant_id',
            'origin',
            'operator',

            'id',
            'is_retest',
            'remark',
            'result_data',
        ],
    ];
}