<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Yanqu\YanquPhplib\CheckIp\CheckIpUtil;

class CheckIp {
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle (Request $request, Closure $next) {
        if (php_sapi_name() == 'cli') {
            // CLI 环境
            $isLegalIp = CheckIpUtil::checkIpInSwoole($request);
        } else {
            // 非 CLI 环境（fpm环境）
            $isLegalIp = CheckIpUtil::checkIp();
        }

        if ($isLegalIp) {
            return $next($request);
        } else {
            $result = ["status_code" => 1001];
            return response($result);
        }
    }
}
