<?php

namespace App\Http\Controllers;

use App\Entities\StandardReport\StandardReportDTO;
use App\Http\Requests\StandardReport\CopyObjectRequest;
use App\Http\Requests\StandardReport\GenerateReportRequest;
use App\Http\Requests\StandardReport\GenerateResultRequest;
use App\Http\Requests\StandardReport\GetOrderResultImageRequest;
use App\Http\Requests\StandardReport\GetWebOfficeTokenRequest;
use App\Services\StandardReport\StandardReportService;
use App\Utils\StatusCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Yanqu\YanquPhplib\YqLog\YqLog;

class StandardReportController extends Controller {

    /**
     * 搜索获取报告分页列表
     *
     * @param Request $request
     * @return array
     */
    public function getReportList (Request $request): array {
        return $this->createResponse(StatusCode::OK,
            app(StandardReportService::class)->getReportList(
                (int)$request->input('page', 1),
                (int)$request->input('page_size', 10),
                $request->input('condition', [])
            )
        );
    }

    /**
     * 异步生成报告
     *
     * @param GenerateReportRequest $request
     * @return array
     */
    public function generateAsync (GenerateReportRequest $request): array {
        $dto = (new StandardReportDTO())
            ->setReportType($request->report_type)
            ->setGenerateParam($request->generate_param)
            ->setTemplateId($request->template_id)
            ->setOrderIdList($request->order_id_list)
            ->setAccountId($request->account_id ?? 0)
            ->setUpdater($request->updater ?? 0)
            ->setScene($request->scene ?? '')
            ->setStandardReportId($request->standard_report_id ?? 0);

        return $this->createResponse(StatusCode::OK,
            app(StandardReportService::class)->generateAsync($dto)
        );
    }

    /**
     * 同步生成报告
     *
     * @param GenerateReportRequest $request
     * @return array
     */
    public function generateSync (GenerateReportRequest $request): array {
        $dto = (new StandardReportDTO())
            ->setReportType($request->report_type)
            ->setGenerateParam($request->generate_param)
            ->setTemplateId($request->template_id)
            ->setOrderIdList($request->order_id_list)
            ->setAccountId($request->account_id ?? 0)
            ->setUpdater($request->updater ?? 0)
            ->setScene($request->scene ?? '');

        return $this->createResponse(StatusCode::OK,
            app(StandardReportService::class)->generateSync($dto)
        );
    }

    /**
     * 获取状态id列表
     *
     * @param Request $request
     * @return array
     */
    public function getStatusByIdList (Request $request): array {
        $validate = Validator::make($request->all(), [
            'report_id_list' => 'required|array',
        ]);
        if ($validate->fails()) {
            return $this->createResponse(StatusCode::ERROR, $validate->errors()->first());
        }
        return $this->createResponse(StatusCode::OK,
            app(StandardReportService::class)->getReportStatusByReportIdList(
                $request->input('report_id_list', [])
            )
        );
    }

    public function getDownloadUrl (Request $request) {
        $validate = Validator::make($request->all(), [
            'report_id' => 'required|integer',
        ]);
        if ($validate->fails()) {
            return $this->createResponse(StatusCode::ERROR, $validate->errors()->first());
        }
        return $this->createResponse(StatusCode::OK,
            app(StandardReportService::class)->getReportDownloadUrl($request->input('report_id'))
        );
    }

    /**
     * 获取完整的报告(目前是根据订单id获取)
     *
     * @param Request $request
     * @return array
     */
    public function getCompleteReport (Request $request): array {
        $validate = Validator::make($request->all(), [
            'oid'     => 'required|integer',
            'purpose' => 'string',
        ]);
        if ($validate->fails()) {
            return $this->createResponse(StatusCode::ERROR, $validate->errors()->first());
        }
        return $this->createResponse(StatusCode::OK,
            app(StandardReportService::class)->getCompleteReportByOid(
                $request->input('oid'),
                $request->input('purpose')
            )
        );
    }

    /**
     * 转换pdf
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\BusinessException
     */
    public function convertToPDF (Request $request): array {
        $validate = Validator::make($request->all(), [
            'report_id' => 'required|integer',
        ]);
        if ($validate->fails()) {
            return $this->createResponse(StatusCode::ERROR, $validate->errors()->first());
        }

        return $this->createResponse(StatusCode::OK,
            app(StandardReportService::class)->convertToPDF($request->input('report_id'))
        );
    }

    /**
     * 标准化报告生成结果通知
     * @param Request $request
     * @return array
     */
    public function generateResult(GenerateResultRequest $request) : array {
        return $this->createResponse(StatusCode::OK,
            app(StandardReportService::class)->generateResult($request->validatedParam())
        );
    }

    /**
     * 复制oss文件到公有bucket
     * @param CopyObjectRequest $request
     * @return array
     */
    public function copyObject(CopyObjectRequest $request)
    {
        return $this->createResponse(StatusCode::OK,
            app(StandardReportService::class)->copyObject($request->validatedParam())
        );
    }

    /**
     * 获取weboffice的token
     * @param GetWebOfficeTokenRequest $request
     * @return array
     */
    public function getWebOfficeToken(GetWebOfficeTokenRequest $request)
    {
        return $this->createResponse(StatusCode::OK,
            app(StandardReportService::class)->getWebOfficeToken($request->validatedParam())
        );
    }

    /**
     * 获取订单结果图片
     * @param GetOrderResultImageRequest $request
     * @return array
     */
    public function getOrderResultImage(GetOrderResultImageRequest $request)
    {
        return $this->createResponse(StatusCode::OK,
            app(StandardReportService::class)->getOrderResultImage($request->validatedParam())
        );
    }
}