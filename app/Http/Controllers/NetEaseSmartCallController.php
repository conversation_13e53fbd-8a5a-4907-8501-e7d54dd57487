<?php

namespace App\Http\Controllers;

use App\Services\SmartCall\NetEaseSmartCallService;
use App\Utils\StatusCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * 网易七鱼智能外呼控制器
 * 处理公网访问的智能外呼业务请求
 */
class NetEaseSmartCallController extends Controller
{
    /**
     * 网易七鱼智能外呼服务
     */
    private NetEaseSmartCallService $smartCallService;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->smartCallService = new NetEaseSmartCallService();
    }
    
    /**
     * 导入客户信息
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function importCustomers(Request $request)
    {
        try {
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'customers' => 'required|array',
                'customers.*.name' => 'required|string|max:100',
                'customers.*.phone' => 'required|string|max:20',
                'customers.*.task_id' => 'required|string',
                'account_id' => 'nullable|integer',
                'encrypt_key' => 'nullable|string|size:16'
            ]);
            
            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }
            
            $customers = $request->input('customers');
            $accountId = $request->input('account_id');
            $encryptKey = $request->input('encrypt_key');
            
            // 检查是否为批量导入
            if (count($customers) > 100) {
                $result = $this->smartCallService->batchImportCustomers($customers, $accountId, $encryptKey);
            } else {
                $result = $this->smartCallService->importCustomers($customers, $accountId, $encryptKey);
            }
            
            return $this->createResponse(StatusCode::OK, $result, '客户导入成功');
            
        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '客户导入失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 创建/更新外呼任务
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveTask(Request $request)
    {
        try {
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'taskName' => 'required|string|max:64',
                'botId' => 'required|integer',
                'didNumbers' => 'required|array',
                'robotSeat' => 'required|integer|min:1',
                'recall' => 'required|integer|in:0,1',
                'account_id' => 'nullable|integer',
                'taskId' => 'nullable|integer', // 更新任务时必填
                'folderId' => 'nullable|integer',
                'lineType' => 'nullable|integer|in:0,1,3',
                'linePolicyId' => 'nullable|integer',
                'startType' => 'nullable|integer|in:0,1',
                'executeBeginTime' => 'nullable|integer',
                'executeEndTime' => 'nullable|integer',
                'cycle' => 'nullable|array',
                'executeTimeInterval' => 'nullable|array',
                'blacklistGroups' => 'nullable|array',
                'hangupSms' => 'nullable|integer|in:0,1',
                'hangupSmsMod' => 'nullable|integer|in:0,1',
                'axb' => 'nullable|integer|in:0,1',
                'flashSms' => 'nullable|integer|in:0,1'
            ]);
            
            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }
            
            $accountId = $request->input('account_id');
            
            // 获取任务数据，排除account_id
            $taskData = $request->except(['account_id']);
            
            // 验证特定条件
            if (isset($taskData['lineType']) && $taskData['lineType'] == 1 && empty($taskData['linePolicyId'])) {
                return $this->createResponse(StatusCode::ERROR, [], '智能外显策略ID不能为空');
            }
            
            if (isset($taskData['startType']) && $taskData['startType'] == 1) {
                $requiredFields = ['executeBeginTime', 'executeEndTime', 'cycle', 'executeTimeInterval'];
                foreach ($requiredFields as $field) {
                    if (empty($taskData[$field])) {
                        return $this->createResponse(StatusCode::ERROR, [], "定时任务必须填写{$field}");
                    }
                }
            }
            
            if (isset($taskData['flashSms']) && $taskData['flashSms'] == 1 && empty($taskData['flashSmsConf'])) {
                return $this->createResponse(StatusCode::ERROR, [], '闪信配置不能为空');
            }
            
            $result = $this->smartCallService->saveTask($taskData, $accountId);
            
            return $this->createResponse(StatusCode::OK, $result, '外呼任务保存成功');
            
        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '外呼任务保存失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取话术列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBotList(Request $request)
    {
        try {
            $accountId = $request->input('account_id');
            $result = $this->smartCallService->getBotList($accountId);
            
            return $this->createResponse(StatusCode::OK, $result, '获取话术列表成功');
            
        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '获取话术列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取可用线路列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDidList(Request $request)
    {
        try {
            $accountId = $request->input('account_id');
            $result = $this->smartCallService->getDidList($accountId);
            
            return $this->createResponse(StatusCode::OK, $result, '获取线路列表成功');
            
        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '获取线路列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取意向标签组详情
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getIntentGroupsByIds(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'group_ids' => 'required|array',
                'group_ids.*' => 'integer',
                'account_id' => 'nullable|integer'
            ]);
            
            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }
            
            $groupIds = $request->input('group_ids');
            $accountId = $request->input('account_id');
            
            $result = $this->smartCallService->getIntentGroupsByIds($groupIds, $accountId);
            
            return $this->createResponse(StatusCode::OK, $result, '获取意向标签组成功');
            
        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '获取意向标签组失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取已审核短信模版列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getValidSmsTemplateList(Request $request)
    {
        try {
            $accountId = $request->input('account_id');
            $result = $this->smartCallService->getValidSmsTemplateList($accountId);
            
            return $this->createResponse(StatusCode::OK, $result, '获取短信模版列表成功');
            
        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '获取短信模版列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取拦截规则列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFilterRuleList(Request $request)
    {
        try {
            $accountId = $request->input('account_id');
            $result = $this->smartCallService->getFilterRuleList($accountId);
            
            return $this->createResponse(StatusCode::OK, $result, '获取拦截规则列表成功');
            
        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '获取拦截规则列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 查询任务状态
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTaskStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'task_id' => 'required|integer',
                'account_id' => 'nullable|integer'
            ]);
            
            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }
            
            $taskId = $request->input('task_id');
            $accountId = $request->input('account_id');
            
            $result = $this->smartCallService->getTaskStatus($taskId, $accountId);
            
            return $this->createResponse(StatusCode::OK, $result, '查询任务状态成功');
            
        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '查询任务状态失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取环境信息（用于调试）
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEnvironmentInfo(Request $request)
    {
        try {
            $result = $this->smartCallService->getEnvironmentInfo();
            return $this->createResponse(StatusCode::OK, $result, '获取环境信息成功');
            
        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '获取环境信息失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试加密功能
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testEncryption(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'data' => 'required|string',
                'encrypt_key' => 'required|string|size:16',
                'account_id' => 'nullable|integer'
            ]);
            
            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }
            
            $data = $request->input('data');
            $encryptKey = $request->input('encrypt_key');
            $accountId = $request->input('account_id');
            
            // 环境校验
            $this->smartCallService->getEnvironmentInfo(); // 这会触发环境校验
            
            // 创建客户端实例用于加密操作
            $client = new \Yanqu\YanquPhplib\SmartCall\SmartCallClient(
                config('constants.smart_call_url'),
                config('constants.smart_call_app_key'),
                config('constants.smart_call_app_secret'),
                config('constants.smart_call_app_type', '')
            );
            
            // 加密
            $encrypted = $client->encryptSensitiveField($data, $encryptKey);
            
            // 解密验证
            $decrypted = $client->decryptSensitiveField($encrypted, $encryptKey);
            
            $result = [
                'original' => $data,
                'encrypted' => $encrypted,
                'decrypted' => $decrypted,
                'md5_hash' => $client->getMd5Hash($data),
                'is_valid' => $data === $decrypted,
                'environment' => app()->environment()
            ];
            
            return $this->createResponse(StatusCode::OK, $result, '加密测试完成');
            
        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '加密测试失败: ' . $e->getMessage());
        }
    }
}
