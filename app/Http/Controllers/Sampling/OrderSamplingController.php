<?php

namespace App\Http\Controllers\Sampling;

use App\Entities\OrderSampling\UpdateSamplingInfoDTO;
use App\Entities\OrderSampling\UpdateSamplingTimeDTO;
use App\Exceptions\BusinessException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Order\OrderSampling\UpdateSamplingInfoRequest;
use App\Http\Requests\Order\OrderSampling\UpdateSamplingTimeRequest;
use App\Services\Order\OrderSampling\SampleService;
use Exception;
use Illuminate\Http\Request;
use Yanqu\YanquPhplib\Queue\MNSCallbackHandler;
use Yanqu\YanquPhplib\Queue\MNSException;

class OrderSamplingController extends Controller {

    public function updateSamplingInfo (UpdateSamplingInfoRequest $request) {
        return $this->success(
            app(SampleService::class)->updateSamplingInfo(
                (new UpdateSamplingInfoDTO())
                    ->setOid($request->oid)
                    ->setSamplingId($request->sampling_id)
                    ->setDeliverDay($request->deliver_day)
                    ->setDeliverConsignee($request->deliver_consignee)
                    ->setDeliverMobile($request->deliver_mobile)
                    ->setRemindDay($request->remind_day ?? '')
                    ->setUniversityCode($request->university_code)
                    ->setCampusId($request->campus_id)
                    ->setBuildingId($request->building_id ?? 0)
                    ->setDeliverId($request->deliver_id ?? 0)
                    ->setProvince($request->province)
                    ->setCity($request->city)
                    ->setLouceng($request->louceng ?? 0)
                    ->setLouzhuang($request->louzhuang ?? 0)
                    ->setLoushi($request->loushi)
                    ->setAccountId($request->account_id ?? 0)
                    ->setOperateFrom($request->operate_from)
                    ->setOperatorId($request->operator_id ?? 0)
                    ->setLinkId($request->link_id ?? 0)
                    ->setNumber($request->number ?? 0)
                    ->setReason($request->reason ?? '')
            )
        );
    }

    /**
     * @throws BusinessException
     */
    public function updateSamplingTime (UpdateSamplingTimeRequest $request) {
        return $this->success(
            app(SampleService::class)->updateSamplingTime(
                (new UpdateSamplingTimeDTO())
                    ->setOid($request->oid)
                    ->setAccountId($request->account_id ?? 0)
                    ->setDeliverDay($request->deliver_day)
                    ->setRemindDay($request->remind_day ?? '')
                    ->setDeliverId($request->deliver_id ?? 0)
                    ->setOperateFrom($request->operate_from)
                    ->setOperatorId($request->operator_id ?? 0)
                    ->setSamplingId($request->sampling_id ?? 0)
            )
        );
    }

    /**
     * @throws MNSException
     * @throws Exception
     */
    public function setSamplingTimeRemind (Request $request) {
        $data = MNSCallbackHandler::getInstance()->parse($request->input())->getJsonMessage();
        if (empty($data)) {
            return $this->createResponse(0, MNSCallbackHandler::getInstance()->success());
        }
        app(SampleService::class)->setSamplingTimeRemind(
            $data['oid'] ?? 0,
            $data['sampling_id'] ?? 0,
            $data['remind_day'] ?? '',
            $data['operate_from'] ?? 1,
            $data['operator_id'] ?? 0
        );
        return $this->createResponse(0, MNSCallbackHandler::getInstance()->success());
    }
}