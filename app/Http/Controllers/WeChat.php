<?php

namespace App\Http\Controllers;

use App\Models\AccountBind;
use App\Utils\LogHelper;
use App\Utils\StatusCode;
use App\Utils\YuntongxunSms\SDK\YuntongxunSDK;
use EasyWeChat\Factory;
use Illuminate\Http\Request;

class WeChat extends Controller
{
    public function sendTemplateMessage(Request $request)
    {
        return $this->createResponse(StatusCode::WECHAT_OK);
        
        $accountid = $request->accountid;
        $accountBind = AccountBind::where("accountid", $accountid)->first();
        if (empty($accountBind) || !$accountBind->openid) {
            return $this->createResponse(StatusCode::WECHAT_NO_BIND);
        }
        $to = $accountBind->openid;

        $config = [
            'app_id' => config("wechat.app_id"),
            'secret' => config("wechat.secret"),
            // 指定 API 调用返回结果的类型：array(default)/collection/object/raw/自定义类名
            'response_type' => 'array',

            //...
        ];

        $templateId = trim($request->template_id);

        $app = Factory::officialAccount($config);
        $data = json_decode($request->data, true);
        $url = $request->url;
        $miniprogram = json_decode($request->miniprogram, true);
        if (count($miniprogram) >= 2) {
            $sendData = [
                'touser' => $to,
                'template_id' => $templateId,
                'miniprogram' => $miniprogram,
                'data' => $data
            ];
        } elseif (!empty($url)) {
            $sendData = [
                'touser' => $to,
                'template_id' => $templateId,
                'url' => $url,
                'data' => $data
            ];
        } else {
            $sendData = [
                'touser' => $to,
                'template_id' => $templateId,
                'data' => $data
            ];
        }
        $result = $app->template_message->send($sendData);

        $logInfo = [];
        $logInfo["request"] = $request->toArray();
        $logInfo["wechat_send_data"] = $sendData;
        $logInfo["result"] = $result;
        LogHelper::doLog("wechat", json_encode($logInfo, JSON_UNESCAPED_UNICODE));

        if ($result["errcode"] == "0") {
            return $this->createResponse(StatusCode::WECHAT_OK);
        } else {
            return $this->createResponse(StatusCode::WECHAT_ERROR, [], $result["errmsg"]);
        }
    }
}
