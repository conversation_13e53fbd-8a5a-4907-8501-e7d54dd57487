<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2025/5/6
 */

namespace App\Http\Controllers\Supplier;

use App\Http\Controllers\Controller;
use App\Http\Requests\DtsEventTrait;
use App\Http\Requests\Supplier\EventNotification\EventNotificationReceiveRequest;
use App\Services\Supplier\SupplierEventNotificationService;
use Illuminate\Http\Request;

class EventNotificationReceiveController extends Controller
{
        use DtsEventTrait;
    public function receive(EventNotificationReceiveRequest $request)
    {
        app(SupplierEventNotificationService::class)->receive($request->type,json_decode($request->extra_params,true));
        return $this->success();
    }

    public function eventFromDts(Request $request)
    {
        $dtsEvent = $this->getDtsEventByRequest($request);
        if (empty($dtsEvent->getId())) {
            return $this->success();
        }
        app(SupplierEventNotificationService::class)->eventFromDts($dtsEvent);
        return $this->success();
    }
}