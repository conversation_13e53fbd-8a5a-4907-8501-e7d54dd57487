<?php

namespace App\Http\Controllers\VideoTest;

use App\Exceptions\BusinessException;
use App\Http\Controllers\Controller;
use App\Services\VideoTest\VideoTestOperateService;
use App\Utils\StatusCode;
use Illuminate\Http\Request;

class VideoTestOperateController extends Controller {


    public function startTimer (Request $request) {
        $request->validate([
            'oid' => 'required',
        ]);
        return $this->createResponse(StatusCode::OK,
            app(VideoTestOperateService::class)->startTimer(
                $request->input('oid')
            )
        );
    }

    /**
     * @throws BusinessException
     */
    public function stopTimer (Request $request) {
        $request->validate([
            'oid' => 'required',
        ]);
        return $this->createResponse(StatusCode::OK,
            app(VideoTestOperateService::class)->stopTimer(
                $request->input('oid'),
                $request->input('operator_type',0),
                $request->input('operator_id',0)
            )
        );
    }

    public function preStopTimer (Request $request) {
        $request->validate([
            'oid' => 'required',
        ]);
        return $this->createResponse(StatusCode::OK,
            app(VideoTestOperateService::class)->preStopTimer(
                $request->input('oid')
            )
        );
    }

    public function getRecordListByOid (Request $request) {
        $request->validate([
            'oid' => 'required',
        ]);
        return $this->createResponse(StatusCode::OK,
            [
                'items' => app(VideoTestOperateService::class)->videoRecordListByOid(
                    $request->input('oid')
                )
            ]
        );
    }

    public function getTimerDetail (Request $request) {
        $request->validate([
            'oid' => 'required',
        ]);
        return $this->createResponse(StatusCode::OK,
            app(VideoTestOperateService::class)->getTimeDetail(
                $request->input('oid')
            )
        );
    }
}