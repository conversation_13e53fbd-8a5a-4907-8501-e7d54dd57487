<?php

namespace App\Http\Controllers\AfterSale;

use App\Entities\ProblemTypeListDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\AfterSale\GetProblemHandlerRequest;
use App\Http\Requests\AfterSale\ProblemTypeListRequest;
use App\Services\AfterSale\ProblemTypeService;
use App\Utils\StatusCode;

class ProblemTypeController extends Controller {

    /**
     * 获取问题处理人
     *
     * @param GetProblemHandlerRequest $request
     * @return array
     * @throws \App\Exceptions\BusinessException
     * @throws \Yanqu\YanquPhplib\Exception\CurlException
     */
    public function getProblemHandler (GetProblemHandlerRequest $request) {
        return $this->createResponse(
            StatusCode::OK,
            app(ProblemTypeService::class)->getProblemHandler(
                $request->problem_type_id,
                $request->oid,
                $request->with_qrcode ?? false
            )
        );
    }
    public function getProblemTypeList(ProblemTypeListRequest $request) {
        return $this->createResponse(
            StatusCode::OK,
            app(ProblemTypeService::class)->getList(
                (new ProblemTypeListDTO())
                    ->setTypeName($request->type_name ?? null)
                    ->setOid($request->oid ?? null)
                    ->setRetestOn($request->retest_on ?? null)
                    ->setState($request->state ?? null)
                    ->setFeedbackOn($request->feedback_on ?? null)
                    ->setSupplierOn($request->supplier_on ?? null)
                    ->setWithBank($request->with_bank ?? false)
                    ->setAll((bool)$request->all ?? false)
            )
        );
    }
}