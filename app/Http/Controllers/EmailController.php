<?php

namespace App\Http\Controllers;


use App\Constants\Constant;
use App\Entities\EmailEntity;
use App\Models\CrmAccount;
use App\Services\EmailService;
use App\Utils\LogHelper;
use App\Utils\StatusCode;
use Illuminate\Http\Request;

class EmailController extends Controller
{
    public function sendEmail(Request $request, EmailService $service): array
    {
        try {
            [$toAddress, $subject, $body, $attachments] = $this->getEmailParam($request, true);
            $entity = (new EmailEntity())->setToAddress($toAddress)->setSubject($subject)->setBody($body)->setAttachments($attachments);
            try {
                $res = $service->sendEmail($entity);
                if ($res) {
                    LogHelper::doLog("openapi_email", json_encode(['request' => $request->all(), 'msg' => "发送邮件成功", 'code' => StatusCode::SEND_MAIL_OK], JSON_UNESCAPED_UNICODE));
                    return $this->createResponse(StatusCode::SEND_MAIL_OK, [], "发送邮件成功");
                }
            } catch (\Exception $e) {
                LogHelper::doLog("openapi_email", json_encode(['request' => $request->all(), 'msg' => $e->getMessage(), 'code' => StatusCode::SEND_MAIL_ERROR], JSON_UNESCAPED_UNICODE));
                return $this->createResponse(StatusCode::SEND_MAIL_ERROR, [], $e->getMessage());
            }
        } catch (\RuntimeException $paramException) {
            LogHelper::doLog("openapi_email", json_encode(['request' => $request->all(), 'msg' => $paramException->getMessage(), 'code' => $paramException->getCode()], JSON_UNESCAPED_UNICODE));
            return $this->createResponse($paramException->getCode(), [], $paramException->getMessage());
        }

        LogHelper::doLog("openapi_email", json_encode(['request' => $request->all(), 'msg' => "发送邮件失败", 'code' => StatusCode::SEND_MAIL_ERROR], JSON_UNESCAPED_UNICODE));
        return $this->createResponse(StatusCode::SEND_MAIL_ERROR, [], "发送邮件失败");
    }

    public function sendEmailAsync(Request $request, EmailService $service): array
    {
        try {
            [$toAddress, $subject, $body, $attachments] = $this->getEmailParam($request);
            $entity = (new EmailEntity())->setToAddress($toAddress)->setSubject($subject)->setBody($body)->setAttachments($attachments);
            try {
                $res = $service->sendEmailAsync($entity);
                if ($res) {
                    return $this->createResponse(StatusCode::SEND_MAIL_OK, [], "发送异步邮件成功");
                }
            } catch (\Exception $e) {
                return $this->createResponse(StatusCode::SEND_MAIL_ERROR, [], $e->getMessage());
            }
        } catch (\RuntimeException $paramException) {
            return $this->createResponse($paramException->getCode(), [], $paramException->getMessage());
        }
    }

    private function getEmailParam(Request $request, $verifyToken = false): array
    {
        $toAddress = $request->post("toaddress", "");
        $subject = $request->post("subject", "");
        $body = $request->post("body", "");
        $time = $request->post("time", "");
        $token = $request->post("token", "");
        $attachments = $request->post("attachments", []);
        $toAddressAry = explode(",",$toAddress);
        $toAddressAryValid = [];

        $shiyanjiaEmails = array_filter($toAddressAry, function($address) {
            return strpos($address, 'shiyanjia.com') !== false;
        });

        $validAddress = CrmAccount::query()->whereIn('loginame', $shiyanjiaEmails)
            ->where('isvoid', Constant::NOT_VOID)
            ->where('status',  CrmAccount::STATUS_NORMAL)
            ->where('isnormal', 1)
            ->pluck('loginame')
            ->flip()
            ->all();

        foreach ($toAddressAry as $address) {
            if(strpos($address, 'shiyanjia.com') !== false) {
                if (array_key_exists($address, $validAddress)) {
                    $toAddressAryValid[] = $address;
                }
            } else {
                $toAddressAryValid[] = $address;
            }
        }
        $toAddressValid = implode(",",$toAddressAryValid);
        if (empty($toAddressValid)) {
            throw new \RuntimeException("收件人不可以为空，原发件人{$toAddress},标题{$subject}", StatusCode::SEND_MAIL_NO_ADDRESS);
        }
        if(strpos($toAddressValid,"@") === false) {
            throw new \RuntimeException("收件人格式非法", StatusCode::SEND_MAIL_NO_ADDRESS);
        }
        if (empty($subject)) {
            throw new \RuntimeException("标题不可以为空", StatusCode::SEND_MAIL_NO_SUBJECT);
        }
        if (empty($body)) {
            throw new \RuntimeException("正文不可以为空", StatusCode::SEND_MAIL_NO_BODY);
        }
        if(!is_array($attachments)){
            $attachments = explode(",",$attachments);
        }
        if ($verifyToken && md5($time . "yqEmail") !== $token) {
            throw new \RuntimeException("非法请求来源", StatusCode::SEND_MAIL_NO_BODY);
        }
        return [
            $toAddressValid, $subject, $body, $attachments
        ];
    }

}
