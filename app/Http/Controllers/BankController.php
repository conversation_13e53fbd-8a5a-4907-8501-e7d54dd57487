<?php
/**
 * Created by PhpStorm.
 * User: 86583
 * Date: 2023/4/6
 * Time: 13:21
 * 银行卡号相关接口
 */

namespace App\Http\Controllers;


use App\Services\BankService;
use App\Services\CardService;
use App\Utils\StatusCode;
use Illuminate\Http\Request;

class BankController extends Controller
{
    /**
     * 通过图片获取银行卡号
     */
    public function BankCardOcr(Request $request)
    {
        $bankImg = $request->post("bankImg", "");
        $accountid = $request->post("accountid", 0);
        if ($bankImg == "") {
            return $this->createResponse(StatusCode::BANK_UNDEFINED, [], "参数错误");
        }
        $BankService = new BankService();
        $response = $BankService->OcrBankCard($bankImg, $accountid);
        if (empty($response)) {
            return $this->createResponse(StatusCode::BANK_ERROR, [], "请求失败");
        } else {
            if ($response['status'] == "OK") {
                return $this->createResponse(StatusCode::BANK_OK, [
                    "card_number" => $response['result']['card_number'],
                    "card_type" => $response['result']['card_type']
                ], "success");
            } else {
                return $this->createResponse(StatusCode::BANK_ERROR, [], "未识别成功");
            }
        }
    }

    /**
     * 银行卡校验(卡号+姓名+身份证号)
     * //避免身份证号明文传参，请根据accountid查询
     */
    public function BankCardCheck(Request $request)
    {
        $bankcode = $request->post("bankcode", ""); //银行卡号
        $username = $request->post("username", ""); //用户姓名
        $accountid = $request->post("accountid", 0); //用户id
        $checkType = $request->post("checkType", 0); // 3三要素 2二要素
        if ($bankcode == "" || $accountid == 0 || $checkType == 0) {
            return $this->createResponse(StatusCode::BANK_UNDEFINED, [], "参数错误");
        }
        $BankService = new BankService();
        if ($checkType == 3) {
            $response = $BankService->BankCard3c($bankcode, $username, $accountid);
        } else {
            $response = $BankService->BankCard2c($bankcode, $username, $accountid);
        }
        if ($response['status'] != 100) {
            return $this->createResponse(StatusCode::BANK_ERROR, [], $response['msg']);
        } else {
            $data = $response['data'];
            if ($data['code'] == 200) {
                if ($data['data']['result'] == 0) {
                    return $this->createResponse(StatusCode::BANK_OK, [], "success");
                } else {
                    return $this->createResponse(StatusCode::BANK_ERROR, [], $data['data']['desc']);
                }
            } else {
                return $this->createResponse(StatusCode::BANK_ERROR, [], "识别失败：" . $data['msg']);
            }
        }
    }
}