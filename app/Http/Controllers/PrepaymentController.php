<?php

namespace App\Http\Controllers;

use App\Models\AccountCoupon;
use App\Services\Prepayment\PrepaymentSplitAmountService;
use App\Utils\StatusCode;
use Illuminate\Http\Request;

class PrepaymentController extends Controller
{
    /**
     * 获取账号预存金额拆分
     * @return array[]
     */
    public function getSplitPrepaymentAmount(Request $request): array
    {
        $accountId = $request->post('accountId');
        $groupId = $request->post('groupId');
        return (new PrepaymentSplitAmountService())->getSplitPrepaymentAmount($accountId, $groupId);
    }

    public function getCouponPrepaymentAmount(Request $request): array
    {
        $accountIds = $request->post('accountIds');
        if (!is_array($accountIds)) {
            $accountIds = explode(',', $accountIds);
        }
        $couponAmount = (new AccountCoupon())->getCouponPrepaymentAmount($accountIds);
        $data = [];
        // 以$accountIds 作为key，vaule默认为0
        $data['couponAmount'] = array_fill_keys($accountIds, 0);
        $data['couponAmount'] = $couponAmount + $data['couponAmount'];
        $data['couponAmount'] = json_encode($data['couponAmount']);

        return $this->createResponse(StatusCode::OK, $data);
    }

    /**
     * 查询研溯预存金额
     * @param Request $request
     * @return array
     */
    public function getYanSuPrepaymentAmount(Request $request): array
    {
        $accountId = $request->post('accountId');
        $groupId = $request->post('groupId');
        return (new PrepaymentSplitAmountService())->getYanSuPrepaymentAmount($accountId, $groupId);
    }

    public function isHasUnSplitPrepayment(Request $request): array
    {
        $accountId = $request->post('accountId');
        $groupId = $request->post('groupId');
        $data = (new PrepaymentSplitAmountService())->isHasUnSplitPrepayment($accountId, $groupId);
        return $this->createResponse(StatusCode::OK, $data);

    }

    public function getPrepaymentRechargeType(Request $request) {
        $autoIds = $request->post('auto_ids');
        $dataType = $request->post('data_type');
        $data = (new PrepaymentSplitAmountService())->getPrepaymentRechargeType($autoIds, $dataType);
        return $this->createResponse(StatusCode::OK, $data);
    }

}
