<?php

namespace App\Http\Controllers\CrmAccount;

use App\Http\Controllers\Controller;
use App\Services\CrmAccount\CrmAccountService;
use App\Utils\StatusCode;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Yanqu\YanquPhplib\Exception\CurlException;

class CrmAccountController extends Controller
{
    /**
     * 获取crm账户列表
     *
     * @param Request $request
     * @return JsonResponse
     * @throws CurlException
     */
    public function getCrmAccounts(Request $request)
    {
        $crmAccountService = new CrmAccountService();
        return response()->json([
            'status_code' => StatusCode::OK,
            'status_msg' => 'success',
            'data' => $crmAccountService->getCrmAccounts($request->all()),
        ]);
    }
}