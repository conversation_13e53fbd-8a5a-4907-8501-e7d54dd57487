<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2024 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/4/1 14:34
 */
namespace App\Http\Controllers\Order;

use App\Http\Controllers\Controller;
use App\Services\Order\OrderAssuredFulfillService;
use Illuminate\Http\Request;
use Yanqu\YanquPhplib\Queue\MNSCallbackHandler;

class OrderAssuredFulfillController extends Controller
{
    public function setResultTime(Request $request)
    {
        $mnsCallbackData = MNSCallbackHandler::getInstance()
            ->parse($request->input())
            ->getJsonMessage();

        app(OrderAssuredFulfillService::class)->setResultTime(
            $mnsCallbackData['oid'],
            $mnsCallbackData['arrive_time'],
            $mnsCallbackData['is_sample_shipping'] ?? 0
        );

        return $this->success();
    }
}