<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2025 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/2/28 15:33
 */
namespace App\Http\Controllers\Order;

use App\Http\Controllers\Controller;
use App\Http\Requests\DtsEventTrait;
use App\Services\Order\OrderReportService;
use App\Utils\StatusCode;
use Illuminate\Http\Request;

class OrderReportController extends Controller
{
    use DtsEventTrait;

    public function getReportVisibility(Request $request)
    {
        $oid = $request->input('oid');
        if (empty($oid)) {
            return $this->createResponse(StatusCode::ERROR, [], '参数错误');
        }

        $reportVisibility = app(OrderReportService::class)->getReportVisibility($oid);

        return $this->createResponse(
            StatusCode::OK,
            ['visibility' => $reportVisibility], 'success'
        );
    }

    public function getOrdersReportVisibility(Request $request)
    {
        $oids = $request->input('oids', []);
        if (empty($oids)) {
            return $this->createResponse(StatusCode::ERROR, [], '参数错误');
        }

        $ordersReportVisibility = app(OrderReportService::class)->getOrdersReportVisibility($oids);

        return $this->createResponse(StatusCode::OK, $ordersReportVisibility, 'success');
    }

    public function reportUploadedCallback(Request $request)
    {
        $dtsEvent = $this->getDtsEventByRequest($request);
        if (empty($dtsEvent->getId())) {
            return $this->success();
        }

        app(OrderReportService::class)->handleReportUploaded($dtsEvent);

        return $this->success();
    }
}