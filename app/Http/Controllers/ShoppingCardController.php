<?php
/**
 * Created by PhpStorm.
 * User: fangyan
 * Date: 2021/04/19
 * Time: 上午11:00
 */

namespace App\Http\Controllers;

use App\Models\ShopOrder;
use App\Utils\StatusCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;


class ShoppingCardController extends Controller
{
    /**
     * 自动发放购物卡
     * $orderid sci_shop_order.orderid  购物卡兑换订单号
     * $ids 锁定的购物卡数据
     */
    public function SendCard(Request $request)
    {
        DB::beginTransaction();
        try{
            $orderid    =   trim($request->orderid);
            $ids    =   json_decode($request->ids,true);
            if(empty($ids)){
                $ids =   ShopOrder::checkStock($orderid,0,0);
            }
            $actionGrant    =   ShopOrder::actionGrant($orderid,$ids);
            if($actionGrant === false){
                DB::rollBack();
                return $this->createResponse(StatusCode::CARD_ERROR, [], "发放失败,请重试");
            }
            DB::commit();
            return $this->createResponse(StatusCode::CARD_OK, [], "success");
        }catch (\Exception $e){
            DB::rollBack();
            if(!empty($ids)){
                DB::table("shop_ecard")->whereIn("id",$ids)->update(["status"=>"1"]);
            }
            return $this->createResponse(StatusCode::CARD_ERROR, [], "发放失败,请重试");
        }
    }

    /**
     * 校验发放数据
     * @param Request $request
     * @param $accountid 用户id
     * @param $goodid 兑换购物卡id
     * @param $puynums 兑换数量
     * @return array
     */
    public function checkSendCard(Request $request)
    {
        $accountid   =   $request->accountid;
        $goodid   =   $request->goodid;
        $puynums   =   $request->puynums;
        if(empty($accountid) || empty($goodid) || empty($puynums)){
            return $this->createResponse(StatusCode::CARD_ERROR, [], "参数错误");
        }
        //校验重复提交
        $orderinfo  =   ShopOrder::repeatSubmit($accountid,$goodid,$puynums);
        if($orderinfo === false){
            DB::rollBack();
            return $this->createResponse(StatusCode::CARD_ERROR, [], "重复提交");
        }
        //自动发放电子卡预警机制
        $checkUserCanAutoSendEcard  =  ShopOrder::checkUserCanAutoSendEcard($accountid);
        if($checkUserCanAutoSendEcard === false){
            DB::rollBack();
            return $this->createResponse(StatusCode::CARD_WARNING, [], "不自动发放");
        }
        //校验库存并锁定数据
        $ids =   ShopOrder::checkStock(0,$goodid,$puynums);
        /*if($ids === false){
            return $this->createResponse(StatusCode::CARD_ERROR, [], "库存不足");
        }*/
        if($ids === false){
            $ids    =   "";
        }
        return  $this->createResponse(StatusCode::CARD_OK, $ids, "success");
    }

    /**
     * 解除锁定
     * @param Request $request
     * @param ids 需要解除锁的购物卡数据 sci_shop_ecard.id
     */
    public function cancelLock(Request $request){
        ShopOrder::cancelLock(json_decode($request->ids,true));
    }
    /**
     * 发送短信
     * @param $orderid 兑换订单orderid
     */
    public function sedShopCardSms(Request $request){
        $sendSms    =   ShopOrder::sendSms($request->orderid);
        if($sendSms === false){
            return $this->createResponse(StatusCode::CARD_ERROR, [], "发送失败");
        }else{
            return $this->createResponse(StatusCode::CARD_OK, [], "发送成功");
        }
    }

}