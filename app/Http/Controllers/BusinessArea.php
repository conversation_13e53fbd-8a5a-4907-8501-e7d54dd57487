<?php


namespace App\Http\Controllers;


use App\Constants\BusinessAreaConstants;
use App\Models\AccountInfo;
use App\Services\BusinessAreaService;
use App\Services\ResponsibleUserService;
use App\Services\UserService;
use Yanqu\YanquPhplib\YqLog\YqLog;
use App\Utils\LogHelper;
use App\Utils\SensorsDataUtil;
use App\Utils\StatusCode;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;

/**
 * 区域业务(发布需求、获取负责人)
 * Class BusinessArea
 * @package App\Http\Controllers
 */
class BusinessArea extends Controller
{
    /**
     * @var BusinessAreaService
     */
    protected $businessService;
    protected $responseUserService;
    protected $userService;
    public function __construct() {
        $this->businessService = new BusinessAreaService();
        $this->responseUserService = new ResponsibleUserService();
        $this->userService = new UserService();
    }

    /**
     * 获取业务区域负责人信息
     * @param Request $request
     * @return array
     */
    public function getAreaUserInfo(Request $request):array
    {
        $params = Input::all();
        $accountId = $params['accountid'] ?? 0;
        $userIp = $params['user_ip'] ?? ''; //转发地址可能多个ip拼接，取首个 方法内处理
        $categoryId = $params['categoryid'] ?? 0;
        $buffetId = $params['buffetid'] ?? 0;
        try {
            //未登录且获取无ip，默认给兜底
            if(empty($accountId) && empty($userIp)) {
                $userInfo = $this->responseUserService->getDemandDefaultUserInfo(); //兜底用户
                return $this->createResponse(StatusCode::GET_QRCODE_OK, $userInfo,"success");
            }
            //获取对接人
            $accountInfo = $this->userService->getAccountInfo($accountId,['city']);
            $addressId = $this->userService->getBranchAddressId($accountInfo,$userIp);
            $finalAddressId = $this->responseUserService->transformBranchId($addressId); //有些办事处无人员配置，转换一下
            //材料成长走一样的逻辑，从配置表取项目经理
            $userList = $this->responseUserService->getBusinessAreaUserData($categoryId,$buffetId,$finalAddressId);
            if(empty($userList)) {
                //取区域副经理
                $userList = $this->responseUserService->getAreaUserByCompanyAddressBook($finalAddressId,
                    BusinessAreaConstants::REGION_VICE_MANAGER_ARR);
                //没有则取区域经理
                if(empty($userList)) {
                    $userList = $this->responseUserService->getAreaUserByCompanyAddressBook($finalAddressId,
                        BusinessAreaConstants::REGION_MANAGER_ARR);
                }
            }
            //返回用户
            if(!empty($userList)) {
                $userInfo = $userList[array_rand($userList)]; //随机取经理
            }else {
                $userInfo = $this->responseUserService->getDemandDefaultUserInfo(); //兜底用户
            }
            return $this->createResponse(StatusCode::GET_QRCODE_OK, $userInfo,"success");
        } catch (\Exception $e) {
            YqLog::logger('pickup:getAreaUserInfo')->error($e);
            return $this->createResponse(StatusCode::GET_QRCODE_ERROR, $e->getMessage(),"error");
        }
    }

    /**
     * 新增发布需求接口
     * @param Request $request
     * @return array
     */
    public function createDemandData(Request $request)
    {
        $input = Input::all(); //弃用$request，部分字段冲突导致取不到值，例如file、files
        LogHelper::doLog("createDemand", json_encode(['input' => $input], JSON_UNESCAPED_UNICODE));
        $categoryId = $input['categoryid'] ?? 0; //业务类别
        $contacter = $input['contacter'] ?? ""; //联系人
        $contactel = $input['contactel'] ?? "";//联系电话
        $contactemail = $input['contactemail'] ?? ""; //联系邮箱
        $patterncnt = isset($input['patterncnt']) && !empty($input['patterncnt']) ? $input['patterncnt'] : ''; //样品数量
        $budget = isset($input['budget']) && !empty($input['budget']) ? $input['budget'] : ''; //预算范围
        $period = isset($input['period']) && !empty($input['period']) ? $input['period'] : ''; //完成周期
        $detail = $input['detail'] ?? ""; //需求描述
        $saccountid = isset($input['saccountid']) && !empty($input['saccountid'])
            ? $input['saccountid'] : BusinessAreaConstants::DEMAND_DEFAULT_CRM_ACCOUNT_ID; //负责人id
        $ctype = $input['ctype'] ?? 1; //默认pc
        $files = $input['files'] ?? null; //附件
        $accountId = $input['accountid'] ?? 0;
        $ip = $input['user_ip'] ?? "";
        if(empty($categoryId) || ($ctype != 7 && empty($contacter)) || empty($contactel)
            || ($ctype != 7 && empty($contactemail)) || empty($detail) || empty($saccountid)) {
            return $this->createResponse(StatusCode::GET_QRCODE_ERROR, [],"请填写必填项相关信息");
        }
        DB::beginTransaction();
        try {
            $taskNo = $this->businessService->getTaskNO(); //任务no
            $title = BusinessAreaConstants::CATEGORY_LIST[$categoryId]; //标题
            //accountid为空查询用户id绑定
            if(empty($accountId)) {
                $accountId = DB::table('account')->where('loginame',$contactel)->value('accountid');
            }
            $accountId = !empty($accountId) ? $accountId : 0;
            //获取城市
            if (!empty($accountId)) {
                $accountObj = AccountInfo::where("accountid", $accountId)->first();
                $cityId = !empty($accountObj->city) ? $accountObj->city : $this->userService->getCityIdByIp($ip);
            } else {
                $cityId = $this->userService->getCityIdByIp($ip);
            }
            //获取企微id
            $external_userid = $this->businessService->getExternalUseridByAccountId($accountId);
            //判断是否首单
            $is_first_order = $this->businessService->checkIsFirstOrder($accountId);
            //重组数据
            $taskId = $this->businessService->addTaskData($categoryId,$accountId,$cityId,$taskNo,$title,$contacter,
                $contactel,$contactemail,$patterncnt,$budget,$period,$detail,$saccountid,$ip,$ctype,$external_userid,
                $is_first_order);
            if($taskId > 0) {
                //新增一条type为24的售前沟通消息
                $presaleId = $this->businessService->addPresaleCommunicationData($accountId,$external_userid,$saccountid,
                    $categoryId,$period,$patterncnt,$budget,$detail);
                //把taskid更新上去
                if ($presaleId !== null) {
                    DB::table("presale_communication")->where("id", $presaleId)->update(["taskid" => $taskId]);
                    //绑定发布人及沟通记录id
                    DB::table('task')->where('taskid',$taskId)->update([
                        'last_presale_communication_id' => $presaleId,
                        'create_user' => !empty($saccountid) ? $saccountid : 0
                    ]);
                }
                //更新附件
                $this->businessService->updateFileData($files,$taskId);
                //发送邮件信息
                $this->businessService->addJobEmailData($contactemail,$contactel,$patterncnt,$budget,$period,$detail,$taskId,$files);
                //神策埋点
                $this->sensorsRecord($taskNo,BusinessAreaConstants::CATEGORY_LIST[$categoryId],$contacter,
                    BusinessAreaConstants::DEMAND_SOURCE[$ctype],$ip,$accountId);

                DB::commit();
                return $this->createResponse(StatusCode::GET_QRCODE_OK,['taskid' => $taskId],'success');
            }else {
                DB::rollBack();
                return $this->createResponse(StatusCode::GET_QRCODE_ERROR,[],'需求发布失败，请联系客服咨询');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->createResponse(StatusCode::GET_QRCODE_ERROR, [],$e->getMessage());
        }
    }

    /**
     * 获取成长业务用户
     * @param $categoryId
     * @param $addressId
     */
    private function getGrowthBusinessUserData($categoryId,$addressId)
    {
        $userInfo = DB::table("business_area_config as a")
            ->leftJoin('crm_account as b','a.accountid','=','b.accountid')
            ->leftJoin('job as d','b.jobid','=','d.id')
            ->where('a.categoryid',$categoryId)
            ->where('b.is_leave',0)
            ->where('b.dismissdate','=','')
            ->whereRaw("FIND_IN_SET('".$addressId."',sci_a.addressids)")
            ->where('a.isvoid',0)
            ->orderByDesc('a.id')
            ->select('a.accountid as saccountid','b.realname as name','b.mobile','b.wxqrcode as qrcode','d.jobname')
            ->first();
        //默认取第一个用户
        if(empty($userInfo)) {
            $userInfo = DB::table("business_area_config as a")
                ->leftJoin('crm_account as b','a.accountid','=','b.accountid')
                ->leftJoin('job as d','b.jobid','=','d.id')
                ->where('a.categoryid',$categoryId)
                ->where('b.is_leave',0)
                ->where('b.dismissdate','=','')
                ->where('a.isvoid',0)
                ->orderByDesc('a.id')
                ->select('a.accountid as saccountid','b.realname as name','b.mobile','b.wxqrcode as qrcode','d.jobname')
                ->first();
        }
        return $userInfo;
    }

    /**
     * 新增发布需求神策埋点，crmapi项目调用
     * @param Request $request
     * @return array
     */
    public function createSensorsRecord(Request $request)
    {
        $input = Input::all(); //弃用$request，部分字段冲突导致取不到值，例如file、files
        $demandNo = isset($input['demand_no']) ? $input['demand_no'] : null;
        $categoryId = isset($input['categoryid']) ? $input['categoryid'] : null; //业务类别
        $contacter = isset($input['contacter']) ? $input['contacter'] : null; //联系人
        $ctype = isset($input['ctype']) ? $input['ctype'] : 1; //默认pc
        $accountId = isset($input['accountid']) && !empty($input['accountid']) ? $input['accountid'] : 0;
        $ip = isset($input['user_ip']) && !empty($input['user_ip']) ? $input['user_ip'] : '';
        if(empty($ip) || empty($demandNo) || empty($categoryId) || empty($ctype)) {
            return $this->createResponse(StatusCode::ERROR,[],"参数缺失");
        }
        $res = $this->sensorsRecord($demandNo,
            (!empty(BusinessAreaConstants::CATEGORY_LIST[$categoryId])
            ? BusinessAreaConstants::CATEGORY_LIST[$categoryId] : ""),
            $contacter,
            !empty(BusinessAreaConstants::DEMAND_SOURCE[$ctype])
                ? BusinessAreaConstants::DEMAND_SOURCE[$ctype] : "",
            $ip,
            $accountId);
        return $this->createResponse(StatusCode::OK,['taskno' => $demandNo,'event_status' => $res],'success');
    }

    /**
     * 神策埋点
     * @param $demandNo
     * @param $businessName
     * @param $contacter
     * @param $demandSource
     * @param $userIp
     * @param $accountId
     */
    private function sensorsRecord($demandNo,$businessName,$contacter,$demandSource,$userIp,$accountId)
    {
        $sensorAna = SensorsDataUtil::getInstance();
        $sensorProperties = [];
        $sensorProperties['$time'] = (int)(microtime(true) * 1000);
        $sensorProperties['$ip'] = $userIp;
        $sensorProperties["is_success"] = true;
        $sensorProperties["fail_reason"] = '';
        $sensorProperties["demand_no"] = $demandNo;
        $sensorProperties["business_name"] = $businessName;
        $sensorProperties["contacter"] = $contacter;
        $sensorProperties["demand_source"] = $demandSource;
        $isLoginId = !empty($accountId) ? true : false;
        LogHelper::doLog("NewDemandSensorsRecord", json_encode(['sensorProperties' => $sensorProperties], JSON_UNESCAPED_UNICODE));
        $res = $sensorAna->track($accountId, $isLoginId, "new_demand", $sensorProperties);
        return $res;
    }

}
