<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2024/8/28
 */

namespace App\Http\Controllers;

use App\Exceptions\BusinessException;
use App\Http\Requests\TypicalCaseLibrary\CaseDeleteRequest;
use App\Http\Requests\TypicalCaseLibrary\GetAbnormalRequest;
use Yanqu\YanquPhplib\Openapi\TypicalCase\Constants\CaseOriginConstants as CaseOriginConstant;
use App\Services\TypicalCaseLibrary\Helper\TypicalCaseTrait;
use App\Services\TypicalCaseLibrary\TypicalCaseService;
use App\Utils\StatusCode;
use App\Validate\TypicalCase\CaseSaveValidator;
use Illuminate\Http\Request;
use Yanqu\YanquPhplib\YqLog\YqLog;

class TypicalCaseController extends Controller
{
    use TypicalCaseTrait;

    public function index(Request $request)
    {
        $result = app(TypicalCaseService::class)->getList($this->formatSearchParams($request), $request->all(['origin', 'merchant_id', 'operator']));
        return $this->createResponse(StatusCode::OK, $result, "success");
    }

    public function unique(Request $request)
    {
        $result = app(TypicalCaseService::class)->unique($request->all());
        return $this->createResponse(StatusCode::OK, $result, "success");
    }



    public function save(Request $request)
    {
        $caseSaveValidator = new CaseSaveValidator();
        try {
            $origin = $request->get('origin', CaseOriginConstant::SUPPLIER);
            $caseSaveValidator->scene(CaseOriginConstant::ORIGIN[$origin])->saveForSubmit($request->input('is_submit'))->check($request->all());
            if ($caseSaveValidator->getError()) {
                return $this->createResponse(StatusCode::ERROR, [], $caseSaveValidator->getError());
            }
            $data = $request->all();
            $id = app(TypicalCaseService::class)->save($data);
            return $this->createResponse(StatusCode::OK, ['case_id' => $id], "success");
        } catch (BusinessException $businessException) {
            YqLog::logger('typical_case_library:case')->error($businessException->getMessage());
            return $this->createResponse(StatusCode::ERROR, [], $businessException->getMessage());
        } catch (\Exception $e) {
            YqLog::logger('typical_case_library:case')->error($e);
            return $this->createResponse(StatusCode::ERROR, [], '系统错误');
        }
    }

    public function createFromProcess(Request $request)
    {
        $caseSaveValidator = new CaseSaveValidator();
        try {
            $origin = $request->get('origin', CaseOriginConstant::LAB_REFER_PROCESS);
            $caseSaveValidator->scene(CaseOriginConstant::ORIGIN[$origin])->check($request->all());
            if ($caseSaveValidator->getError()) {
                return $this->createResponse(StatusCode::ERROR, [], $caseSaveValidator->getError());
            }
            $data = $request->all();
            $id = app(TypicalCaseService::class)->createFromProcess($data);
            return $this->createResponse(StatusCode::OK, ['case_id' => $id], "success");
        } catch (BusinessException $businessException) {
            YqLog::logger('typical_case_library:case')->error($businessException->getMessage());
            return $this->createResponse(StatusCode::ERROR, [], $businessException->getMessage());
        } catch (\Exception $e) {
            YqLog::logger('typical_case_library:case')->error($e);
            return $this->createResponse(StatusCode::ERROR, [], '系统错误');
        }
    }



    public function delete(CaseDeleteRequest $request)
    {
        try {
            $data = $request->all();
            app(TypicalCaseService::class)->delete($data);
            return $this->createResponse(StatusCode::OK, [], "success");
        } catch (BusinessException $businessException) {
            YqLog::logger('typical_case_library:case')->error($businessException->getMessage());
            return $this->createResponse(StatusCode::ERROR, [], $businessException->getMessage());
        } catch (\Exception $e) {
            YqLog::logger('typical_case_library:case')->error($e);
            return $this->createResponse(StatusCode::ERROR, [], '系统错误');
        }
    }


    public function show(Request $request)
    {
        try {
            $id = $request->get('id', 0);
            $currentOid = $request->get('current_oid', 0);
            if (!$id) {
                return $this->createResponse(StatusCode::ERROR, [], "参数缺失");
            }
            $result = app(TypicalCaseService::class)->getDetail($id, $currentOid, $request->all(['origin', 'merchant_id', 'operator']));
            return $this->createResponse(StatusCode::OK, $result, "success");
        } catch (BusinessException $businessException) {
            YqLog::logger('typical_case_library:case')->error($businessException->getMessage());
            return $this->createResponse(StatusCode::ERROR, [], $businessException->getMessage());
        } catch (\Exception $e) {
            YqLog::logger('typical_case_library:case')->error($e);
            return $this->createResponse(StatusCode::ERROR, [], '系统错误');
        }
    }

    public function setExcellent(Request $request)
    {
        try {
            $id = $request->post('id', 0);
            $isExcellent = $request->post('is_excellent', 0);
            $operator = $request->post('operator', 0);
            if (!$id) {
                return $this->createResponse(StatusCode::ERROR, [], "参数缺失");
            }
            app(TypicalCaseService::class)->setExcellent($id,$isExcellent,$operator);
            return $this->createResponse(StatusCode::OK, [], "success");
        } catch (BusinessException $businessException) {
            YqLog::logger('typical_case_library:case')->error($businessException->getMessage());
            return $this->createResponse(StatusCode::ERROR, [], $businessException->getMessage());
        } catch (\Exception $e) {
            YqLog::logger('typical_case_library:case')->error($e);
            return $this->createResponse(StatusCode::ERROR, [], '系统错误');
        }
    }

    public function getSampleCategory(Request $request)
    {
        $buffetId = $request->get('buffet_id', 0);
        $result = app(TypicalCaseService::class)->getSampleCategory($buffetId);
        return $this->success($result);
    }

    public function getOrderBuffet(Request $request)
    {
        $keyword = trim($request->get('keyword', ''));
        $result = app(TypicalCaseService::class)->getOrderBuffet($keyword);
        return $this->success($result);
    }

    public function getOrderInfo(Request $request)
    {
        $osn = $request->get('osn', '');
        $providerMerchantId = $request->get('provider_merchant_id', 0);
        $result = app(TypicalCaseService::class)->getOrderInfo($osn, $providerMerchantId);
        return $this->success($result);
    }

    public function getOrderSampleIngredient(Request $request)
    {
        $oid = $request->get('oid', 0);
        $sampleGroup = $request->get('sample_group', 0);
        $result = app(TypicalCaseService::class)->getOrderSampleIngredient($oid, $sampleGroup);
        return $this->success($result);
    }

    public function guessSampleCategory(Request $request)
    {
        $sampleIngredient = trim($request->get('sample_ingredient', ''));
        $buffetId = (int)$request->get('buffet_id', 0);
        $result = app(TypicalCaseService::class)->guessSampleCategory($sampleIngredient, $buffetId);
        return $this->success($result);
    }
    
    public function inviteCoWorker(Request $request)
    {
        app(TypicalCaseService::class)->inviteCoWorker($request->all());
        return $this->success();
    }

    public function getAbnormal(GetAbnormalRequest $request)
    {
        $buffetId = $request->get('buffet_id');
        $result = app(TypicalCaseService::class)->getAbnormal($buffetId);
        return $this->response($result);
    }
}