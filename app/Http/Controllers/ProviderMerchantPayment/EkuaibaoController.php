<?php

namespace App\Http\Controllers\ProviderMerchantPayment;

use App\Exceptions\BusinessException;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Http\Controllers\Controller;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoService;
use Illuminate\Http\Request;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\Queue\MNSCallbackHandler;
use Yanqu\YanquPhplib\Queue\MNSException;

class EkuaibaoController extends Controller
{
    /**
     * @param Request $request
     * @return array
     * @throws BusinessException
     * @throws BusinessWithoutErrorReportException
     * @throws MNSException
     * @throws CurlException
     * /
     */
    public function createEkuaibaoFlowByMnsCallback(Request $request)
    {
        $data = MNSCallbackHandler::getInstance()->parse($request->input())->getJsonMessage();
        if (empty($data)) {
            return $this->success(MNSCallbackHandler::getInstance()->success());
        }
        app(EkuaibaoService::class)->createEkuaibaoFlow($data);
        return $this->success(MNSCallbackHandler::getInstance()->success());
    }
}