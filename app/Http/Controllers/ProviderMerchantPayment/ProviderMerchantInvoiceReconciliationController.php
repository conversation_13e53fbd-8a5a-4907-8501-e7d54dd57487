<?php

namespace App\Http\Controllers\ProviderMerchantPayment;

use App\Exceptions\BusinessException;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Http\Controllers\Controller;
use App\Http\Requests\ProviderMerchantPayment\ConfirmReconcileRequest;
use App\Http\Requests\ProviderMerchantPayment\GetReconciliationApplicationsRequest;
use App\Http\Requests\ProviderMerchantPayment\Reconciliation\AuditReconcileRequest;
use App\Http\Requests\ProviderMerchantPayment\RejectReconciliationApplicationRequest;
use App\Services\ProviderMerchantPayment\Reconciliation\ProviderMerchantInvoiceReconciliationService;
use App\Utils\StatusCode;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Yanqu\YanquPhplib\AliyunHttp\AliyunException;
use Yanqu\YanquPhplib\Exception\CurlException;

class ProviderMerchantInvoiceReconciliationController extends Controller
{
    private $providerMerchantInvoiceReconciliationService;

    public function __construct()
    {
        $this->providerMerchantInvoiceReconciliationService = new ProviderMerchantInvoiceReconciliationService();
    }

    public function getReconcileStatusEnums(Request $request): JsonResponse
    {
        $list = $this->providerMerchantInvoiceReconciliationService->getReconcileStatusEnums($request->all());
        return response()->json([
            'status_code' => StatusCode::OK,
            'status_msg' => 'success',
            'data' => $list,
        ]);
    }

    public function getReconcileTypeEnums(): JsonResponse
    {
        $list = $this->providerMerchantInvoiceReconciliationService->getReconcileTypeEnums();
        return response()->json([
            'status_code' => StatusCode::OK,
            'status_msg' => 'success',
            'data' => $list,
        ]);
    }

    public function getReconciliationApplications(GetReconciliationApplicationsRequest $request): JsonResponse
    {
        $list = $this->providerMerchantInvoiceReconciliationService->getReconciliationApplications($request->all());
        return response()->json([
            'status_code' => StatusCode::OK,
            'status_msg' => 'success',
            'data' => $list,
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws BusinessException
     */
    public function getReconciliationApplicationDetail(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reconciliation_application_id' => 'required|integer',
        ], [
            'reconciliation_application_id.required' => '核销申请id不能为空',
            'reconciliation_application_id.integer' => '核销申请id必须是整数',
        ]);
        if ($validator->fails()) {
            throw new BusinessException($validator->errors()->first());
        }
        $detail = $this->providerMerchantInvoiceReconciliationService
            ->getReconciliationApplicationDetail($request->all());
        return response()->json([
            'status_code' => StatusCode::OK,
            'status_msg' => 'success',
            'data' => $detail,
        ]);
    }

    /**
     * @param RejectReconciliationApplicationRequest $request
     * @return JsonResponse
     * @throws BusinessException
     */
    public function rejectReconciliationApplication(RejectReconciliationApplicationRequest $request): JsonResponse
    {
        $this->providerMerchantInvoiceReconciliationService->rejectReconciliationApplication($request->all());
        return response()->json([
            'status_code' => StatusCode::OK,
            'status_msg' => 'success',
        ]);
    }

    /**
     * @param ConfirmReconcileRequest $request
     * @return JsonResponse
     * @throws BusinessException
     */
    public function confirmReconcile(ConfirmReconcileRequest $request): JsonResponse
    {
        $this->providerMerchantInvoiceReconciliationService->confirmReconcile($request->all());
        return response()->json([
            'status_code' => StatusCode::OK,
            'status_msg' => 'success',
        ]);
    }

    /**
     * @throws BusinessException
     * @throws BusinessWithoutErrorReportException
     * @throws AliyunException
     * @throws BusinessWithoutErrorReportException
     * @throws CurlException
     * @throws BusinessException
     * /
     */
    public function auditReconciliationBySpecialist(AuditReconcileRequest $request)
    {
        $this->providerMerchantInvoiceReconciliationService->auditReconciliationBySpecialist($request->all());
        return $this->success();
    }
}
