<?php

namespace App\Http\Controllers\ProviderMerchantPayment;

use App\Exceptions\BusinessWithoutErrorReportException;
use App\Http\Controllers\Controller;
use App\Http\Requests\ProviderMerchantPayment\OrderSettle\CheckInvoicesBeforeAddSettleRequest;
use App\Http\Requests\ProviderMerchantPayment\OrderSettle\GetOrderSettlesRequest;
use App\Http\Requests\ProviderMerchantPayment\OrderSettle\SaveOrderSettleInvoicesRequest;
use App\Services\ProviderMerchantPayment\OrderSettle\OrderSettleService;
use App\Services\ProviderMerchantPayment\OrderSettle\SaveOrderSettleInvoicesService;

class OrderSettleController extends Controller
{
    public function getOrderSettlesForUploadInvoice(GetOrderSettlesRequest $request)
    {
        return $this->success(
            app(OrderSettleService::class)->getOrderSettlesForUploadInvoice($request->all())
        );
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    public function saveOrderSettleInvoices(SaveOrderSettleInvoicesRequest $request)
    {
        app(SaveOrderSettleInvoicesService::class)->saveOrderSettleInvoices($request->all());
        return $this->success();
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    public function checkInvoicesBeforeSettle(CheckInvoicesBeforeAddSettleRequest $request)
    {
        app(SaveOrderSettleInvoicesService::class)->checkInvoicesBeforeAddSettle($request->all());
        return $this->success();
    }

}