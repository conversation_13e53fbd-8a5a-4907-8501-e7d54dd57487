<?php


namespace App\Http\Controllers\Constants\EnterpriseContract;


class EnterpriseContractConstant
{
    //正常
    const NOT_VOID = 0;
    //作废
    const VOID = 1;

    //合同类型-企业授权书
    const CONTRACT_TYPE_AUTHORIZATION = 1;
    //合同类型-企业框架协议（团体负责人变更）
    const CONTRACT_TYPE_AUTH_GROUP_CHANGE = 8;
    //合同类型-预付款合同
    const CONTRACT_TYPE_ADVANCE_PAYMENT = 2;
    //合同类型-中标项目合同
    const CONTRACT_TYPE_PROJECT = 3;
    //合同类型-订单合同
    const CONTRACT_TYPE_ORDER = 4;
    //合同类型-企业授权书
    const CONTRACT_TYPE_AUTH_LETTER = 6;

    /**
     * 合同类型：高校/科研院所/医院协议
     */
    const CONTRACT_TYPE_UNIVS_HOSPITAL = 7;

    //合同类型-无
    const CONTRACT_TYPE_NULL = 0;

    //合同状态-待审核
    const STATUS_WAIT_EXAMINE = 1;
    //合同状态-驳回
    const STATUS_REJECT = 2;
    //合同状态-待归档
    const STATUS_WAIT_ARCHIVE = 3;
    //合同状态-已归档
    const STATUS_ALREADY_PLACE_ON_FILE = 4;
    //合同状态-已作废
    const STATUS_CANCEL = 5;
    //合同状态-无
    const STATUS_NULL = 0;

    /**
     * 有优惠条款
     */
    const HAS_DISCOUNT_TERMS = 1;

}
