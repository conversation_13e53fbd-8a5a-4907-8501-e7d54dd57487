<?php

namespace App\Http\Controllers;


use App\Services\AutoInvoice\SearchSuspectIdentityService;
use App\Utils\StatusCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AutoInvoiceController extends Controller
{
    /**
     * 根据流水号和附言查询疑似身份
     * @param Request $request
     * @return array
     */
    public function searchSuspectedIdentityByPostscript(Request $request)
    {
        try {
            $params = $request->all();
            $validate = Validator::make($params, [
                'serialno' => 'required',
                'postscript' => 'required',
            ]);
            if ($validate->fails()) {
                return $this->createResponse(StatusCode::ERROR, $validate->errors()->first());
            }
            $searchSuspectIdentityService = new SearchSuspectIdentityService();
            $data = $searchSuspectIdentityService->searchSuspectedIdentityByPostscript($params);
            return $this->createResponse(StatusCode::OK, $data, '查询成功');
        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, $e->getMessage());
        }
    }
}