<?php


namespace App\Http\Controllers;


use App\Constants\Invoice\InvoiceCurrencyCodeConstants;
use App\Constants\InvoiceConstants;
use App\Constants\NuoNuoConstants;
use App\Entities\ApplyInvoiceDTO;
use App\Exceptions\InvoiceException;
use App\Http\Requests\Invoice\InvoiceApplyRequest;
use App\Models\AccessToken;
use App\Models\Account;
use App\Models\AccountCouponApply;
use App\Models\AccountCouponTicket;
use App\Models\AccountCouponTicketItem;
use App\Models\FinanceProvider;
use App\Models\FinanceTicketApply;
use App\Models\FinanceTicketApplyItem;
use App\Models\FinanceTicketInfo;
use App\Models\OnlineInvoice;
use App\Repositories\Invoice\FinanceSellerBankRepository;
use App\Services\InvoiceService;
use App\Services\NnfpService;
use App\Services\OnlineInvoiceService;
use App\Services\PreStoreService;
use App\Utils\Invoice\InvoiceControllerResponseUtil;
use App\Utils\LogHelper;
use App\Utils\StatusCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yanqu\YanquPhplib\YqLog\YqLog;

class InvoiceController extends Controller
{
    use InvoiceControllerResponseUtil;
    const NANJINYANKE_TAX_RATE_CHANGE = "2023-04-01";
    const NNFP_APPLICATION_TOKEN_TYPE = [
        [
            'value'=>6,
            'ticketType'=>[0,1,2,4]
        ],
        [
            'value'=>9,
            'ticketType'=>[5,6]
        ]
    ];

    /**
     * 订单申请开具发票。正票/红票
     **/
    public function orderOpenTicket(Request $request){
        try {
            $applyid = $request->post('applyid');
            $isRep = $request->post('isRep');
            $redReason = $request->post('redReason');
            $adminId = $request->post('adminId');
            if(empty($applyid) || $isRep == null || $isRep == ''){
                return $this->createResponse(StatusCode::INVOICE_ORDER_OPEN_TICKET_ERROR,[],'参数错误');
            }
            $nnfpService = new NnfpService();
            $ticketApply = FinanceTicketApply::query()->where('applyid',$applyid)->select('applyid','ticketinfoid','applyaccount','providerid','invoicedm','invoiceno','ticketremark','dzpjson','applyamount','invoicetime')->first()->toArray();
            $ticketInfo = FinanceTicketInfo::query()->where('ticketinfoid',$ticketApply['ticketinfoid'])
                ->select('mobile','ticketype','title','registrationo','registaddress','registphone',
                    'depositbank','banksn', 'seller_bank_id')->first()->toArray();
            $providerInfo = FinanceProvider::query()->where('providerid',$ticketApply['providerid'])->select('phone','companyaccount','address','taxnumber','bankaccountnumber','accept_ticket_type','bank')->first()->toArray();
            $financeSellerBankRepository = new  FinanceSellerBankRepository();
            $sellerBank = $financeSellerBankRepository->getSellerBankBySellerBankId($ticketInfo['seller_bank_id']);
            if (empty($sellerBank)) {
                return $this->createResponse(StatusCode::INVOICE_ORDER_OPEN_TICKET_ERROR, [],
                    '未查询到开票银行信息');
            }
            $isNami = $this->ticketIsNami($applyid);
            if ($isRep) {
                $ticketInfo['blueInvoiceLine'] = $this->getNnfpInvoiceType($ticketInfo['ticketype']);
                $ticketInfo['ticketype'] = InvoiceConstants::REP_INVOICE_TYPE[$ticketInfo['ticketype']] ??
                    $ticketInfo['ticketype'];

                $repType = $this->getRepType($ticketInfo['ticketype']);
            } else {
                $invoiceService = new InvoiceService();
                $invoiceService->validateDuplicatedInvoice($applyid, InvoiceConstants::TICKET_APPLY_TYPE_ORDER);
            }
            if($isNami){
                $ticketInfo['clerk'] = "卢明明";
            }else{
                $ticketInfo['clerk'] = $this->getClerk('order',$ticketInfo['ticketype']); //根据发票类型判断
            }
            if(empty($ticketInfo['clerk'])){
                return $this->createResponse(StatusCode::INVOICE_ORDER_OPEN_TICKET_ERROR,[],'获取开票员失败');
            }
            $tokenType = $this->getTokenTypeByTicketType($ticketInfo['ticketype']);
            $token = $this->getProviderAccessToken($tokenType,$providerInfo['taxnumber']);
            if(empty($providerInfo['taxnumber']) || empty($token)){
                return $this->createResponse(StatusCode::INVOICE_ORDER_OPEN_TICKET_ERROR,[],"获取税号信息失败");
            }

            $onlineInvoice = OnlineInvoice::where('type',1)->where('applyid',$applyid)->where('is_void',0)->where('status',1)->where('invoice_type',1)->orderBy("id", "desc")->first();
            if($isRep && !empty($onlineInvoice)){ //红冲时取开票时的信息
                $onlineInvoice = $onlineInvoice->toArray();
                $onlineInvoiceService = new OnlineInvoiceService();
                $getOriginOnlineInvoice = $onlineInvoiceService->getOriginOnlineInvoice($onlineInvoice['id'], $onlineInvoiceService::TRANS_TYPE_PUBLIC);
                if(empty($getOriginOnlineInvoice) || $getOriginOnlineInvoice['code'] != StatusCode::GET_ONLINE_INVOICE_SUCCESS){
                    return $this->createResponse(StatusCode::INVOICE_ORDER_OPEN_TICKET_ERROR,[],$getOriginOnlineInvoice['msg']);
                }
                $billingInfo = $getOriginOnlineInvoice['data']['originOnlineInvoice'];
                $billingInfo['invoiceDate'] = date('Y-m-d H:i:s',time());
                $billingInfo['invoiceType'] = 2;
                $billingInfo['clerk'] = $ticketInfo['clerk'];
                $billingInfo['payee'] = '潘予';
                $billingInfo['checker'] = '李燕南';
                $billingInfo['orderNo'] = $nnfpService->createTicketOrderNo($applyid, 1);
            }else{
                if(empty($ticketInfo['mobile'])){
                    $ticketInfo['mobile'] = Account::query()->where("accountid",$ticketApply["applyaccount"])->value('loginame');
                }
                if($isRep){
                    $dzpjson = $ticketApply['dzpjson'];
                    $dzpjson = json_decode($dzpjson,true);
                    $invoiceCode = !empty($dzpjson['invoiceCode']) ? $dzpjson['invoiceCode'] : '';
                    if (empty($invoiceCode)) {
                        $invoiceCode = !empty($dzpjson['c_fpdm']) ? $dzpjson['c_fpdm'] : '';
                    }
                    $invoiceNo = !empty($dzpjson['invoiceNo']) ? $dzpjson['invoiceNo'] : '';
                    if (empty($invoiceNo)) {
                        $invoiceNo = !empty($dzpjson['c_fphm']) ? $dzpjson['c_fphm'] : '';
                    }
                    if(empty($invoiceCode) || empty($invoiceNo)){
                        return $this->createResponse(StatusCode::INVOICE_ORDER_OPEN_TICKET_ERROR,[],'原开票信息不全');
                    }
                    $ticketInfo['invoiceCode'] = $invoiceCode;
                    $ticketInfo['invoiceNum'] = $invoiceNo;
                    $ticketInfo['invoiceType'] = 2;
                }else{
                    $ticketInfo['invoiceCode'] = '';
                    $ticketInfo['invoiceNum'] = '';
                    $ticketInfo['invoiceType'] = 1;
                }
                $ticketInfo['ticketremark'] = $ticketApply['ticketremark'];
                $ticketInfo['invoiceDetail'] = $this->getOrderDetail($applyid,$isRep,$ticketApply['providerid']);
                $ticketInfo['invoiceLine'] = $this->getNnfpInvoiceType($ticketInfo['ticketype']);
                $originInfo = array_merge($ticketInfo,$providerInfo);
                $originInfo['ticketOrderNo'] = $nnfpService->createTicketOrderNo($applyid, 1);
                $originInfo['seller_opening_bank_name'] = $sellerBank->opening_bank_name;
                $originInfo['seller_account_number'] = $sellerBank->account_number;
                $billingInfo = $this->transBillingInfo($originInfo);
            }
            $billingInfo['token'] = $token;
            $billingInfo['applyid'] = $applyid;
            $billingInfo['blueInvoiceLine'] = $isRep ? $ticketInfo['blueInvoiceLine'] : '';
            $invoiceService = new InvoiceService();
            $billingInfo['invoiceLine'] = $invoiceService->tranRepInvoiceLine($isRep, $billingInfo['invoiceLine']);
            $nnfpService->setTokenType($tokenType);
            if ($isRep && $repType == InvoiceConstants::REP_CONFIRM) {
                $blueTiketInfo = [
                    'amount' => $ticketApply['applyamount'],
                    'invoicetime' => $ticketApply['invoicetime'],
                    'providerid' => $ticketApply['providerid'],
                    'title' => $ticketInfo['title'],
                ];
                $result = $nnfpService->requestRedConfirm($billingInfo,$redReason,$adminId,$blueTiketInfo);
            } else {
                $result = $nnfpService->requestBilling($billingInfo);
            }

            $result = json_decode($result,true);
            return $this->dealOrderOpenTicketResponse($result,$applyid);
        } catch (InvoiceException $e) {
            return $this->createResponse(StatusCode::INVOICE_ORDER_OPEN_TICKET_ERROR,[],$e->getMessage());
        }catch (\Exception $e){
            $context = [
                "billingInfo" => !empty($billingInfo) ? $billingInfo : [],
                "errorMessage" => $e->getTraceAsString()
            ];
            YqLog::logger("finance:invoice:openapi_orderOpenTicket")
                ->error("订单开票错误 applyid: {$applyid}", $context);
            LogHelper::doLog('openapi_orderOpenTicket',json_encode(['request'=>$request->all(),'code'=>StatusCode::INVOICE_ORDER_OPEN_TICKET_ERROR,'responseData'=>[],'msg'=>$e->getMessage()],JSON_UNESCAPED_UNICODE));
            return $this->createResponse(StatusCode::INVOICE_ORDER_OPEN_TICKET_ERROR,[],$e->getMessage());
        }
    }
    /**
     * 预存申请开具发票。正票/红票
    **/
    public function couponOpenTicket(Request $request){
        try {
            $cticketid = $request->post('cticketid');
            $isRep = $request->post('isRep');
            $redReason = $request->post('redReason');
            $adminId = $request->post('adminId');
            if(empty($cticketid) || $isRep === null || $isRep === ''){
                return $this->createResponse(StatusCode::INVOICE_COUPON_OPEN_TICKET_ERROR,[],'参数错误');
            }
            $nnfpService = new NnfpService();
            $couponTicket = AccountCouponTicket::query()->where('cticketid',$cticketid)->first()->toArray();
            $ticketInfo = json_decode($couponTicket['ticketjson'],true)[0];
            $couponApply = AccountCouponApply::query()->where('applyid',$couponTicket['couponapplyid'])->first()->toArray();
            $providerInfo = FinanceProvider::query()->where('providerid',$couponTicket['providerid'])->select('phone','companyaccount','address','taxnumber','bankaccountnumber','accept_ticket_type','bank')->first()->toArray();
            $financeSellerBankRepository = new  FinanceSellerBankRepository();
            $sellerBank = $financeSellerBankRepository->getSellerBankBySellerBankId($couponTicket['seller_bank_id']);
            if (empty($sellerBank)) {
                return $this->createResponse(StatusCode::INVOICE_COUPON_OPEN_TICKET_ERROR, [],
                    '未查询到开票银行信息');
            }
            if ($isRep) {
                $ticketInfo['blueInvoiceLine'] = $this->getNnfpInvoiceType($couponTicket['ticketype']);
                $couponTicket['ticketype'] = InvoiceConstants::REP_INVOICE_TYPE[$couponTicket['ticketype']] ??
                    $couponTicket['ticketype'];
            }
            $isNami = $couponTicket['coupon_type'];   //  0测试费/1材料费
            if($isNami){
                $ticketInfo['clerk'] = "卢明明";
            }else{
                $ticketInfo['clerk'] = $this->getClerk('coupon',$couponTicket['ticketype']); //根据发票类型判断
            }
            if(empty($ticketInfo['clerk'])){
                return $this->createResponse(StatusCode::INVOICE_COUPON_OPEN_TICKET_ERROR,[],'获取开票员失败');
            }
            $tokenType = $this->getTokenTypeByTicketType($couponTicket['ticketype']);
            $token = $this->getProviderAccessToken($tokenType,$providerInfo['taxnumber']);
            if(empty($providerInfo['taxnumber']) || empty($token)){
                return $this->createResponse(StatusCode::INVOICE_COUPON_OPEN_TICKET_ERROR,[],"获取税号信息失败");
            }
            if ($isRep) {
                $repType = $this->getRepType($couponTicket['ticketype']);
            } else {
                $invoiceService = new InvoiceService();
                $invoiceService->validateDuplicatedInvoice($cticketid, InvoiceConstants::TICKET_APPLY_TYPE_COUPON);
            }
            $onlineInvoice = OnlineInvoice::where('type',2)->where('cticketid',$cticketid)->where('is_void',0)->where('status',1)->where('invoice_type',1)->orderBy("id", "desc")->first();
            if($isRep && !empty($onlineInvoice)){ //红冲时取开票时的信息
                $onlineInvoice = $onlineInvoice->toArray();
                $onlineInvoiceService = new OnlineInvoiceService();
                $getOriginOnlineInvoice = $onlineInvoiceService->getOriginOnlineInvoice($onlineInvoice['id'], $onlineInvoiceService::TRANS_TYPE_PUBLIC);
                if(empty($getOriginOnlineInvoice) || $getOriginOnlineInvoice['code'] != StatusCode::GET_ONLINE_INVOICE_SUCCESS){
                    return $this->createResponse(StatusCode::INVOICE_ORDER_OPEN_TICKET_ERROR,[],$getOriginOnlineInvoice['msg']);
                }
                $billingInfo = $getOriginOnlineInvoice['data']['originOnlineInvoice'];
                $billingInfo['invoiceDate'] = date('Y-m-d H:i:s',time());
                $billingInfo['invoiceType'] = 2;
                $billingInfo['clerk'] = $ticketInfo['clerk'];
                $billingInfo['payee'] = '潘予';
                $billingInfo['checker'] = '李燕南';
                $billingInfo['orderNo'] = $nnfpService->createTicketOrderNo($cticketid, 2);
            }else{
                $ticketInfo['ticketOrderNo'] = $nnfpService->createTicketOrderNo($cticketid, 2);
                if($isRep){
                    $dzpjson = $couponTicket['dzpjson'];
                    $dzpjson = json_decode($dzpjson,true);
                    $invoiceCode = !empty($dzpjson['invoiceCode']) ? $dzpjson['invoiceCode'] : '';
                    if (empty($invoiceCode)) {
                        $invoiceCode = !empty($dzpjson['c_fpdm']) ? $dzpjson['c_fpdm'] : '';
                    }
                    $invoiceNo = !empty($dzpjson['invoiceNo']) ? $dzpjson['invoiceNo'] : '';
                    if (empty($invoiceNo)) {
                        $invoiceNo = !empty($dzpjson['c_fphm']) ? $dzpjson['c_fphm'] : '';
                    }
                    if(empty($invoiceCode) || empty($invoiceNo)){
                        return $this->createResponse(StatusCode::INVOICE_ORDER_OPEN_TICKET_ERROR,[],'原开票信息不全');
                    }
                    $ticketInfo['invoiceCode'] = $invoiceCode;
                    $ticketInfo['invoiceNum'] = $invoiceNo;
                    $ticketInfo['invoiceType'] = 2;
                }else{
                    $ticketInfo['invoiceCode'] = '';
                    $ticketInfo['invoiceNum'] = '';
                    $ticketInfo['invoiceType'] = 1;
                }
                $ticketInfo['ticketremark'] = $couponTicket['ticketremark'];
                $ticketInfo['invoiceDetail'] = app(PreStoreService::class)
                    ->getCouponDetail($cticketid, $couponApply, $couponTicket, $isRep);
                $ticketInfo['invoiceLine'] = $this->getNnfpInvoiceType($couponTicket['ticketype']);
                $originInfo = array_merge($ticketInfo,$providerInfo);
                $originInfo['seller_opening_bank_name'] = $sellerBank->opening_bank_name;
                $originInfo['seller_account_number'] = $sellerBank->account_number;
                $billingInfo = $this->transBillingInfo($originInfo);
            }

            $billingInfo['token'] = $token;
            $billingInfo['cticketid'] = $cticketid;
            $billingInfo['blueInvoiceLine'] = $isRep ? $ticketInfo['blueInvoiceLine'] : '';
            $invoiceService = new InvoiceService();
            $billingInfo['invoiceLine'] = $invoiceService->tranRepInvoiceLine($isRep, $billingInfo['invoiceLine']);
            $nnfpService->setTokenType($tokenType);

            if ($isRep && $repType == InvoiceConstants::REP_CONFIRM) {
                $blueTicketInfo = [
                    'amount' => $couponTicket['amount'],
                    'invoicetime' => $couponTicket['invoicetime'],
                    'providerid' => $couponTicket['providerid'],
                    'title' => $ticketInfo['title'],
                ];
                $result = $nnfpService->requestRedConfirm($billingInfo,$redReason,$adminId,$blueTicketInfo);
            } else {
                $result = $nnfpService->requestBilling($billingInfo);
            }
            $result = json_decode($result,true);
            return $this->dealCouponOpenTicketResponse($result, $cticketid);
        } catch (InvoiceException $e) {
            return $this->createResponse(StatusCode::INVOICE_COUPON_OPEN_TICKET_ERROR,[],$e->getMessage());
        } catch (\Exception $e){
            $context = [
                "billingInfo" => !empty($billingInfo) ? $billingInfo : [],
                "errorMessage" => $e->getTraceAsString()
            ];
            YqLog::logger("finance:invoice:openapi_couponOpenTicket")
                ->error("预存开票错误 cticketid: {$cticketid}", $context);
            LogHelper::doLog('openapi_couponOpenTicket',json_encode(['request'=>$request->all(),'code'=>StatusCode::INVOICE_COUPON_OPEN_TICKET_ERROR,'responseData'=>[],'msg'=>$e->getMessage()],JSON_UNESCAPED_UNICODE));
            return $this->createResponse(StatusCode::INVOICE_COUPON_OPEN_TICKET_ERROR,[],$e->getMessage());
        }
    }
    public function getTicketResult(Request $request){
        try {
            $serialNos = $request->post('queryid');
            $providerid = $request->post('providerid');
            $nnfpTokenType = $request->post('nnfpTokenType');
            if(empty($serialNos)){
                return $this->createResponse(StatusCode::INVOICE_GET_TICKET_STATUS_ERROR,[],'发票流水号为空');
            }
            if(empty($providerid)){
                return $this->createResponse(StatusCode::INVOICE_GET_TICKET_STATUS_ERROR,[],'未查询到开票主体');
            }
            $providerInfo = FinanceProvider::query()->where('providerid',$providerid)->select('taxnumber')->first();
            $token = $this->getProviderAccessToken($nnfpTokenType,$providerInfo['taxnumber']);
            if(empty($providerInfo['taxnumber']) || empty($token)){
                return $this->createResponse(StatusCode::INVOICE_GET_TICKET_STATUS_ERROR,[],"获取税号信息失败");
            }
            $nnfpService = new NnfpService();
            $nnfpService->setTokenType($nnfpTokenType);
            $statusResult = $nnfpService->queryInvoiceResult($serialNos,$providerInfo['taxnumber'],$token);
            LogHelper::doLog('openapi_getTicketResultOrigin',json_encode(['request'=>$request->all(),'code'=>0,'responseData'=>$statusResult,'msg'=>''],JSON_UNESCAPED_UNICODE));
            $statusResult = json_decode($statusResult,true);
            if($statusResult['code'] != 'E0000'){
                LogHelper::doLog('openapi_getTicketResult',json_encode(['request'=>$request->all(),'code'=>StatusCode::INVOICE_GET_TICKET_STATUS_ERROR,'responseData'=>[],'msg'=>$statusResult['describe']],JSON_UNESCAPED_UNICODE));
                return $this->createResponse(StatusCode::INVOICE_GET_TICKET_STATUS_ERROR,[],$statusResult['describe']);
            }elseif($statusResult['result'][0]['status'] == '2'){
                OnlineInvoice::where('invoice_serial_num',$serialNos)->update([
                    'status'=>1,
                    'invoice_no'=>$statusResult['result'][0]['invoiceNo'],
                    'invoice_dm'=>$statusResult['result'][0]['invoiceCode'],
                    'pdf_url'=>$statusResult['result'][0]['pdfUrl']
                ]);
                LogHelper::doLog('openapi_getTicketResult',json_encode(['request'=>$request->all(),'code'=>StatusCode::INVOICE_GET_TICKET_STATUS_SUCCESS,'responseData'=>$statusResult['result'][0],'msg'=>$statusResult['describe']],JSON_UNESCAPED_UNICODE));
                return $this->createResponse(StatusCode::INVOICE_GET_TICKET_STATUS_SUCCESS,$statusResult['result'][0],$statusResult['describe']);
            }elseif(in_array($statusResult['result'][0]['status'],['22','24'])){
                OnlineInvoice::where('invoice_serial_num',$serialNos)->update(['status'=>2]);
                LogHelper::doLog('openapi_getTicketResult',json_encode(['request'=>$request->all(),'code'=>StatusCode::INVOICE_GET_TICKET_STATUS_FALSE,'responseData'=>$statusResult['result'][0],'msg'=>$statusResult['describe']],JSON_UNESCAPED_UNICODE));
                return $this->createResponse(StatusCode::INVOICE_GET_TICKET_STATUS_FALSE,$statusResult['result'][0],$statusResult['describe']);
            }else{
                LogHelper::doLog('openapi_getTicketResult',json_encode(['request'=>$request->all(),'code'=>StatusCode::INVOICE_GET_TICKET_STATUS_WAIT,'responseData'=>$statusResult['result'][0],'msg'=>$statusResult['describe']],JSON_UNESCAPED_UNICODE));
                return $this->createResponse(StatusCode::INVOICE_GET_TICKET_STATUS_WAIT,$statusResult['result'][0],$statusResult['describe']);
            }
        }catch (\Exception $e){
            LogHelper::doLog('openapi_getTicketResult',json_encode(['request'=>$request->all(),'code'=>StatusCode::INVOICE_GET_TICKET_STATUS_ERROR,'responseData'=>[],'msg'=>$e->getMessage()],JSON_UNESCAPED_UNICODE));
            return $this->createResponse(StatusCode::INVOICE_GET_TICKET_STATUS_ERROR,[],"查询失败");
        }
    }

    /**
     * 订单发票，获取开票明细
     * @param $applyid
     * @param $isRep
     * @param $providerid
     * @return array
     * @throws InvoiceException
     */
    public function getOrderDetail($applyid,$isRep,$providerid){
        $invoiceService = new InvoiceService();
        $applyItem = FinanceTicketApplyItem::query()->where('applyid',$applyid)->where('isvoid',0)->select('dproductname','dproductnums','dproductunit','confirmamount','orderid','dproductspec','tax_service_code')->get();
        $details = [];
        foreach ($applyItem as $keyItem => $valueItem){
            $tmpgoodsname 					= $valueItem["dproductname"];
            $tmpnum 						= $valueItem["dproductnums"];
            $tmpunit 						= $valueItem["dproductunit"];
            $tmpconfirmamount 				= $valueItem["confirmamount"];
            $spec                           = $valueItem['dproductspec'];

            $orderid 						= $valueItem["orderid"];
            $details[$keyItem]['goodsName']      =   $tmpgoodsname;
            if($orderid > 0){
                $suffix =   $this->getProductNameSuffix($orderid); //获取开票明细后缀
                $details[$keyItem]['goodsName']   =   $tmpgoodsname.$suffix;
            }

            if($isRep == 1){
                $details[$keyItem]['num']             = $tmpnum * -1;    //商品数量
            }else{
                $details[$keyItem]['num']             = $tmpnum;    //商品数量
            }
            $details[$keyItem]['withTaxFlag']            = "1";             			//是不是含税
            $details[$keyItem]['price']         	= round(sprintf("%.8f", $tmpconfirmamount)/floatval($tmpnum), 8);//单价
            $financeApply = FinanceTicketApply::query()->where('applyid',$applyid)->select('ticketinfoid','invoicetime')->first();
            $ticketInfoid = $financeApply['ticketinfoid'];
            $ticketType = FinanceTicketInfo::query()->where('ticketinfoid',$ticketInfoid)->value('ticketype');
            $isNami = $this->ticketIsNami($applyid);
            if($providerid == 12){  //南京研科，20230401开始从小规模纳税人变成一般纳税人，税率也变
                if($isRep){
                    if($financeApply['invoicetime'] < strtotime(self::NANJINYANKE_TAX_RATE_CHANGE)){
                        $details[$keyItem]['taxRate'] = "0.01";
                    }else{
                        if ($isNami) {
                            $details[$keyItem]['taxRate'] = "0.13";
                        } else {
                            $details[$keyItem]['taxRate'] = "0.06";
                        }
                    }
                }else{
                    if(time() < strtotime(self::NANJINYANKE_TAX_RATE_CHANGE)) {
                        $details[$keyItem]['taxRate'] = "0.01";
                    }else{
                        if ($isNami) {
                            $details[$keyItem]['taxRate'] = "0.13";
                        } else {
                            $details[$keyItem]['taxRate'] = "0.06";
                        }
                    }
                }
            }elseif(in_array($providerid,[1,13,18])){    //一般纳税人
                if($isNami){
                    $details[$keyItem]['taxRate']       	= "0.13";          			//税率
                }else{
                    $details[$keyItem]['taxRate']       	= "0.06";
                }

            }elseif(in_array($providerid, [16,5,15,19,11,20])){
                //写了配置的主体
                $details[$keyItem]['taxRate'] = $invoiceService->getRate($isNami, $isRep,
                    $financeApply['invoicetime'],$providerid);
            }else{	//小规模纳税人
                $details[$keyItem]['taxRate']       	= "0.01";
            }

            $details[$keyItem]['unit']                  = $tmpunit;
            $details[$keyItem]['goodsCode']           	= $valueItem['tax_service_code'];   //商品的税收分类编码
            $details[$keyItem]['invoiceLineProperty']   = "0";
            $details[$keyItem]['specType']              = $spec;
        }
        return $details;
    }

    public function getNnfpInvoiceType($ticketType){
        switch ($ticketType) {
            case InvoiceConstants::ELECTRONIC_VAT_GENERAL_INVOICES:
                $NuoNuoTicketType = NuoNuoConstants::GENERAL_ELECTRONIC_INVOICES;
                break;
            case InvoiceConstants::VAT_GENERAL_INVOICES:
                $NuoNuoTicketType = NuoNuoConstants::GENERAL_PAPER_INVOICES;
                break;
            case InvoiceConstants::VAT_SPECIAL_INVOICES:
                $NuoNuoTicketType = NuoNuoConstants::SPECIAL_INVOICES;
                break;
            case InvoiceConstants::ELECTRONIC_VAT_SPECIAL_INVOICES:
                $NuoNuoTicketType = NuoNuoConstants::VAT_ELECTRONIC_SPECIAL_INVOICES;
                break;
            case InvoiceConstants::ELECTRONIC_GENERAL_INVOICES:
                $NuoNuoTicketType = NuoNuoConstants::ELECTRONIC_VAT_GENERAL_INVOICES;
                break;
            case InvoiceConstants::ELECTRONIC_SPECIAL_INVOICES:
                $NuoNuoTicketType = NuoNuoConstants::ELECTRONIC_VAT_SPECIAL_INVOICES;
                break;
            default:
                $NuoNuoTicketType = '';
                break;
        }
        return $NuoNuoTicketType;
    }
    /**
     * 根据订单ID返回开票费用的后缀
     * @param $orderID
     * @return string
     */
    public function getProductNameSuffix($orderID){
        $categoryID = 0;
        if($orderID > 0){
            $sendProductID = DB::table('order_allot')->where(['oid' => $orderID])->value('sendproductid');
            if ($sendProductID) {
                $categoryID = DB::table('provider_product')->where(['providerproductid' => $sendProductID])->value('categoryid');
            }
        }
        return $this->getSuffixByCategoryID($categoryID);
    }
    /**
     * 根据测试项目的类别获取后缀
     * @param $categoryID
     * @return string
     */
    public function getSuffixByCategoryID($categoryID)
    {
        if($categoryID > 10 || $categoryID == 1){
            $testFeesCategory = [11,15,17,19,1];
            $notTestFeesCategory = [18,21,14,12];
            if(in_array($categoryID, $testFeesCategory)){
                return "测试费";
            }elseif(in_array($categoryID, $notTestFeesCategory)){
                return "费";
            }
        }
        return "";
    }

    /**
     * 查询开票主体的token
     * @param $tokenType
     * @param $taxNumber
     * @return false|mixed|string
     */
    public function getProviderAccessToken($tokenType,$taxNumber){
        if(empty($tokenType) || empty($taxNumber)){
            return false;
        }
        $token = AccessToken::query()->where('type',$tokenType)->where('appname',$taxNumber)->value('access_token');
        return !empty($token) ? $token : '';
    }

    public function getTokenTypeByTicketType($ticketType)
    {
        $tokenType = 0;
        if (empty($tokenType)) {
            foreach (self::NNFP_APPLICATION_TOKEN_TYPE as $value) {
                if (in_array($ticketType, $value['ticketType'])) {
                    $tokenType = $value['value'];
                }
            }
        }
        return $tokenType;
    }


    /**
     * 查询开票员
    **/
    public function getClerk($orderCouponType,$ticketype){
        $clerk = '';
        if($orderCouponType == 'order'){
            if(in_array($ticketype,[0,1,5])){
                $clerk = "刘思琦";
            }elseif(in_array($ticketype,[2,4,6])){
                $clerk = "刘娜";
            }
        }elseif($orderCouponType == 'coupon'){
            if(in_array($ticketype,[0,1,5])){
                $clerk = "徐昕";
            }elseif(in_array($ticketype,[2,4,6])){
                $clerk = "刘娜";
            }
        }
        return $clerk;
    }
    /**
     * 发票是否是纳米材料发票
    **/
    public function ticketIsNami($applyid){
        $isNami = 0;
        $ticketApply = FinanceTicketApply::query()->where('applyid',$applyid)->select('applyid','orderids','ticketvals')->first();
        if($ticketApply['ticketvals'] == "纳米材料"){
            $isNami = 1;
        }
        return $isNami;
    }

    /**
     *
    **/
    public function transBillingInfo($originInfo){
        $billingInfo = [
            "buyerName"  	=> trim($originInfo['title']),         //购方名称
            "buyerTaxNum"     	=> trim($originInfo['registrationo']),    //购方税号
            "buyerTel"=> trim($originInfo['registphone']),  //购方企业电话
            "buyerAddress"    	=> trim($originInfo['registaddress']), //购方企业地址
            "buyerAccount"    	=> trim($originInfo['depositbank']).trim($originInfo['banksn']),    //购方企业银行开户行及账号
            "orderNo"    	=> $originInfo['ticketOrderNo'],    //订单号
            "invoiceDate" => date('Y-m-d H:i:s',time()),    //订单时间
            "clerk"        => $originInfo['clerk'], 	//开票员
            "salerTaxNum"  => $originInfo['taxnumber'],   //销方企业税号
            "salerTel"    => $originInfo['phone'],        //销方企业电话
            "salerAddress" => $originInfo['address'],    //销方企业地址
            //销方企业银行开户行及账号
            "salerAccount"  => $originInfo['seller_opening_bank_name'].$originInfo['seller_account_number'],
            "invoiceType"       => $originInfo['invoiceType'],     //开票类型:1,正票;2,红票
            "remark"    =>$originInfo['ticketremark'],   //备注信息
            "payee" 		=> "潘予",    //收款人
            "checker" 		=> "李燕南",   //复核人
            "invoiceCode"   => $originInfo['invoiceCode'],  //冲红时填写的对应蓝票发票代码
            "invoiceNum"    => $originInfo['invoiceNum'],  //冲红时填写的对应蓝票发票号码
            "pushMode"     		=> "-1",        //推送方式:-1,不推送;0,邮箱;1,手机（默认）;2,邮箱、手机
            "invoiceDetail"       => $originInfo['invoiceDetail'],     //电子发票明细
            "invoiceLine"   => $originInfo['invoiceLine'],	//发票种类:p,普通发票(电票)(默认);c,普通发票(纸票);s,专用发票;e,收购发票(电票);f,收购发票(纸质);r,普通发票(卷式)
        ];
        return $billingInfo;
    }

    /**
     * 新增在线开票的发票数据
    **/
    public function addOnlineInvoice(Request $request){
        $originOnlineInvoice = $request->post('originOnlineInvoice');
        $transType = $request->post('transType');
        $onlineInvoiceService = new OnlineInvoiceService();
        if(empty($originOnlineInvoice) || empty($transType)){
            return $onlineInvoiceService->arrData(StatusCode::ADD_ONLINE_INVOICE_ERROR,'参数错误');
        }
        $originOnlineInvoice = json_decode($originOnlineInvoice,true);
        return $onlineInvoiceService->addOnlineInvoice($originOnlineInvoice,$transType);
    }

    /**
     * 获取转换为诺诺开票接口参数后的在线开票的发票数据
    **/
    public function getOriginOnlineInvoice(Request $request){
        $onlineInvoiceId = $request->post('onlineInvoiceId');
        $transType = $request->post('transType');
        $onlineInvoiceService = new OnlineInvoiceService();
        if(empty($onlineInvoiceId) || empty($transType)){
            return $onlineInvoiceService->arrData(StatusCode::GET_ONLINE_INVOICE_ERROR,'参数错误');
        }
        return $onlineInvoiceService->getOriginOnlineInvoice($onlineInvoiceId, $transType);
    }


    public function getRepType($ticketType)
    {
        $res = InvoiceConstants::REP_BILL;
        if (in_array($ticketType,[InvoiceConstants::ELECTRONIC_GENERAL_INVOICES,InvoiceConstants::ELECTRONIC_SPECIAL_INVOICES])) {
            $res = InvoiceConstants::REP_CONFIRM;
        }

        return $res;
    }


    public function cancelDigitalRedConfirm(Request $request)
    {
        $applyType = $request->input('applyType');
        $applyId = $request->input('applyId');

        $nnfpService = new NnfpService();
        $providerId = 0;
        if ($applyType == 1){
            $providerId = FinanceTicketApply::query()->where('applyid',$applyId)->value('providerid');
        } elseif ($applyType == 2) {
            $providerId = AccountCouponTicket::query()->where('cticketid',$applyId)->value('providerid');
        }
        $taxNumber = FinanceProvider::query()->where('providerid',$providerId)->value('taxnumber');

        $token = $this->getTokenTypeByTicketType(5);

        $res = $nnfpService->cancelRedConfirmWithLog($applyType,$applyId,$taxNumber,$token);

        $result = json_decode($res,true);

        if($result['code'] == 'E0000'){
            LogHelper::doLog('openapi_cancelRedConfirm',json_encode(['request'=>$request->all(),'code'=>StatusCode::INVOICE_RED_CONFIRM_CANCEL_SUCCESS,'responseData'=>$result['result'],'msg'=>$result['describe']],JSON_UNESCAPED_UNICODE));
            return $this->createResponse(StatusCode::INVOICE_CANCEL_CONFIRM_SUCCESS,$result['result'],$result['describe']);
        }else{
            LogHelper::doLog('openapi_cancelRedConfirm',json_encode(['request'=>$request->all(),'code'=>StatusCode::INVOICE_RED_CONFIRM_CANCEL_FAIL,'responseData'=>[],'msg'=>$result['describe']],JSON_UNESCAPED_UNICODE));
            return $this->createResponse(StatusCode::INVOICE_CANCEL_CONFIRM_ERROR,[],$result['describe']);
        }
    }

    public function refreshDigitalRedConfirm(Request $request)
    {
        $applyType = $request->input('applyType');
        $applyId = $request->input('applyId');
        $resultTag = $request->input('resultTag', 0);

        $nnfpService = new NnfpService();
        $providerId = 0;
        if ($applyType == 1){
            $providerId = FinanceTicketApply::query()->where('applyid',$applyId)->value('providerid');
        } elseif ($applyType == 2) {
            $providerId = AccountCouponTicket::query()->where('cticketid',$applyId)->value('providerid');
        }
        $taxNumber = FinanceProvider::query()->where('providerid',$providerId)->value('taxnumber');

        $tokenType = 9;
        $token = $this->getProviderAccessToken($tokenType,$taxNumber);
        $nnfpService->setTokenType($tokenType);

        $res = $nnfpService->refreshRedConfirm($applyType,$applyId,$taxNumber,$token, $resultTag);

        $result = json_decode($res,true);

        return $this->dealRefreshRedConfirmResponse($result, $applyType, $applyId);
    }
    public function getDefaultTicketType(Request $request)
    {
        try {
            $ticketType = $request->post('ticketType');
            $providerId = $request->post('providerId');
            if (!in_array($ticketType, InvoiceConstants::ALL_TICKET_TYPE) || empty($providerId)) {
                return $this->createResponse(StatusCode::ERROR, [], "获取发票类型失败，参数错误");
            }
            $invoiceService = new InvoiceService();
            $ticketType = $invoiceService->getDefaultTicketType($ticketType, $providerId);
            return $this->createResponse(StatusCode::OK, ['ticketType' => $ticketType], "获取发票类型成功");
        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], "获取发票类型失败，" . $e->getMessage());
        }
    }

    public function apply(InvoiceApplyRequest $request)
    {
        $applyInvoiceDto = new ApplyInvoiceDTO();
        $applyInvoiceDto->initFromInvoiceApplyRequest($request);
        try {
            $res = app(InvoiceService::class)->applyInvoice($applyInvoiceDto);
            return $this->createResponse(StatusCode::OK,$res,'success');
        } catch (InvoiceException $exception) {
            return $this->createResponse(StatusCode::ERROR, [], $exception->getMessage());
        }
    }


    public function getCurrency()
    {
        return $this->createResponse(StatusCode::OK,\Yanqu\YanquPhplib\Invoice\Constants\InvoiceConstants::CURRENCY,'');
    }

    public function getOrderRate(Request $request)
    {
        try {
            $res = app(InvoiceService::class)
                ->getOrderRate($request->input('currencyCode'), $request->input('oids'));
            return $this->createResponse(StatusCode::OK,$res,'success');
        }catch (InvoiceException $exception) {
            return $this->createResponse(StatusCode::ERROR, [], $exception->getMessage());
        }
    }

    public function getPreInvoiceData(Request $request)
    {
        try {
            $res = app(InvoiceService::class)
                ->getPreInvoiceData($request->input('accountid'), $request->input('oids'));
            return $this->createResponse(StatusCode::OK,$res,'success');
        }catch (InvoiceException $exception) {
            return $this->createResponse(StatusCode::ERROR, [], $exception->getMessage());
        }
    }
}
