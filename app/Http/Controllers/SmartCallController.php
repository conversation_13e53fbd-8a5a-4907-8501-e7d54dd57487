<?php

namespace App\Http\Controllers;

use App\Services\SmartCall\SmartCallService;
use App\Utils\StatusCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * 智能外呼控制器
 * 处理公网访问的智能外呼业务请求
 */
class SmartCallController extends Controller
{
    /**
     * 智能外呼服务
     * @var SmartCallService
     */
    private $smartCallService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->smartCallService = new SmartCallService();
    }

    /**
     * 创建/更新外呼任务
     * 根据网易七鱼接口文档实现
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveTask(Request $request)
    {
        try {
            // 验证基本参数
            $validator = Validator::make($request->all(), [
                // 必填参数
                'taskName' => 'required|string|max:64',
                'botId' => 'required|integer',
                'didNumbers' => 'required|array',
                'robotSeat' => 'required|integer|min:1',
                'recall' => 'required|integer|in:0,1',

                // 可选参数
                'account_id' => 'nullable|integer',
                'taskId' => 'nullable|integer', // 更新任务时必填
                'folderId' => 'nullable|integer',
                'lineType' => 'nullable|integer|in:0,1,3',
                'linePolicyId' => 'nullable|integer',
                'startType' => 'nullable|integer|in:0,1',
                'executeBeginTime' => 'nullable|integer',
                'executeEndTime' => 'nullable|integer',
                'cycle' => 'nullable|array',
                'executeTimeInterval' => 'nullable|array',
                'blacklistGroups' => 'nullable|array',
                'hangupSms' => 'nullable|integer|in:0,1',
                'hangupSmsMod' => 'nullable|integer|in:0,1',
                'hangupSmsConf' => 'nullable|array',
                'hangupSmsAdvancedConf' => 'nullable|array',
                'axb' => 'nullable|integer|in:0,1',
                'flashSms' => 'nullable|integer|in:0,1',
                'flashSmsConf' => 'nullable|array',
                'filterConf' => 'nullable|array',
                'recallConf' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }

            $accountId = $request->input('account_id');

            // 获取任务数据，排除account_id
            $taskData = $request->except(['account_id']);

            // 验证特定条件
            if (isset($taskData['lineType']) && $taskData['lineType'] == 1 && empty($taskData['linePolicyId'])) {
                return $this->createResponse(StatusCode::ERROR, [], '智能外显策略ID不能为空');
            }

            if (isset($taskData['startType']) && $taskData['startType'] == 1) {
                $requiredFields = ['executeBeginTime', 'executeEndTime', 'cycle', 'executeTimeInterval'];
                foreach ($requiredFields as $field) {
                    if (empty($taskData[$field])) {
                        return $this->createResponse(StatusCode::ERROR, [], "定时任务必须填写{$field}");
                    }
                }
            }

            if (isset($taskData['flashSms']) && $taskData['flashSms'] == 1 && empty($taskData['flashSmsConf'])) {
                return $this->createResponse(StatusCode::ERROR, [], '闪信配置不能为空');
            }

            if (isset($taskData['hangupSms']) && $taskData['hangupSms'] == 1) {
                $hangupSmsMod = isset($taskData['hangupSmsMod']) ? $taskData['hangupSmsMod'] : 0;
                if ($hangupSmsMod == 1 && empty($taskData['hangupSmsAdvancedConf'])) {
                    return $this->createResponse(StatusCode::ERROR, [], '高级模式下挂机短信配置不能为空');
                }
            }

            $result = $this->smartCallService->saveTask($taskData, $accountId);

            return $this->createResponse(StatusCode::OK, $result, '外呼任务保存成功');

        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '外呼任务保存失败: ' . $e->getMessage());
        }
    }

    /**
     * 导入客户信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function importCustomers(Request $request)
    {
        try {
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'customers' => 'required|array',
                'customers.*.name' => 'required|string|max:100',
                'customers.*.phone' => 'required|string|max:20',
                'customers.*.task_id' => 'required|string',
                'account_id' => 'nullable|integer',
                'encrypt_key' => 'nullable|string|size:16'
            ]);

            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }

            $customers = $request->input('customers');
            $accountId = $request->input('account_id');
            $encryptKey = $request->input('encrypt_key');

            $result = $this->smartCallService->importCustomers($customers, $accountId, $encryptKey);

            return $this->createResponse(StatusCode::OK, $result, '客户导入成功');

        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '客户导入失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取环境信息（用于调试）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEnvironmentInfo(Request $request)
    {
        try {
            $result = $this->smartCallService->getEnvironmentInfo();
            return $this->createResponse(StatusCode::OK, $result, '获取环境信息成功');

        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '获取环境信息失败: ' . $e->getMessage());
        }
    }
}
