<?php

namespace App\Http\Controllers\Invoice;

use App\Services\Invoice\OrderInvoiceService;
use App\Utils\StatusCode;
use Illuminate\Http\Request;

class OrderInvoiceController
{
    public function getInvoiceItemsByOrderIds(Request $request)
    {
        return response()->json([
            'status_code' => StatusCode::OK,
            'status_msg' => 'success',
            'data' => app(OrderInvoiceService::class)->getInvoiceItemsByOrderIds($request->all()),
        ]);
    }
}