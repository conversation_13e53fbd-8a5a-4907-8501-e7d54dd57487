<?php

namespace App\Http\Controllers\Buffet;

use App\Http\Controllers\Controller;
use App\Http\Requests\BuffetSampleCategory\UpdateCategorySortRequest;
use App\Services\Buffet\BuffetSampleCategoryService;
use Illuminate\Http\Request;

class BuffetSampleCategoryController extends Controller
{
    public function getBuffetCategoryIdPathList(Request $request)
    {
        $buffetId = (int)$request->input('buffet_id');
        $data = app(BuffetSampleCategoryService::class)->getBuffetCategoryIdPathList($buffetId);
        return $this->response($data);
    }

    public function getCategoryBuffetList(Request $request)
    {
        $categoryId = (int)$request->input('category_id');
        $result = app(BuffetSampleCategoryService::class)->getCategoryBuffetList($categoryId);
        return $this->response($result);
    }

    public function getBuffetCategoryIds(Request $request)
    {
        $buffetId = (int)$request->input('buffet_id');
        $result = app(BuffetSampleCategoryService::class)->getBuffetCategoryIds($buffetId);
        return $this->response($result);
    }

    public function checkCategoryBuffetCase(Request $request)
    {
        $param['category_id'] = (int)$request->input('category_id');
        $result = app(BuffetSampleCategoryService::class)->checkCategoryBuffetCase($param);
        return $this->response($result);
    }

    public function addCategory(Request $request)
    {
        $param['category_pid'] = (int)$request->input('category_pid');
        $param['category_name'] = $request->input('category_name');
        $param['buffet_ids'] = $request->input('buffet_ids');
        $result = app(BuffetSampleCategoryService::class)->addCategory($param);
        return $this->response($result);
    }

    public function updateCategory(Request $request)
    {
        $param['category_id'] = (int)$request->input('category_id');
        $param['category_name'] = $request->input('category_name');
        $param['buffet_ids'] = $request->input('buffet_ids');
        $result = app(BuffetSampleCategoryService::class)->updateCategory($param);
        return $this->response($result);
    }

    public function checkCategoryTreeBuffets(Request $request)
    {
        $param['category_id'] = (int)$request->input('category_id');
        $result = app(BuffetSampleCategoryService::class)->checkCategoryTreeBuffets($param);
        return $this->response($result);
    }

    public function deleteCategory(Request $request)
    {
        $param['category_id'] = (int)$request->input('category_id');
        $result = app(BuffetSampleCategoryService::class)->deleteCategory($param);
        return $this->response($result);
    }

    public function checkCaseRelation(Request $request)
    {
        $param['buffet_id'] = (int)$request->input('buffet_id');
        $param['category_id'] = (int)$request->input('category_id');
        $result = app(BuffetSampleCategoryService::class)->checkCaseRelation($param);
        return $this->response($result);
    }

    public function updateBind(Request $request)
    {
        $param['buffet_id'] = (int)$request->input('buffet_id');
        $param['category_ids'] = $request->input('category_ids');
        $result = app(BuffetSampleCategoryService::class)->updateBind($param);
        return $this->response($result);
    }

    public function updateCategorySort(UpdateCategorySortRequest $request)
    {
        $categoryIds = $request->input('category_ids');
        $result = app(BuffetSampleCategoryService::class)->updateCategorySort($categoryIds);
        return $this->response($result);
    }
}