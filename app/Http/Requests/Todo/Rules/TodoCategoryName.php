<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2024 (https://www.shiyanjia.com)
 * <AUTHOR> 2024/12/23 17:41
 */
namespace App\Http\Requests\Todo\Rules;

use App\Models\TodoConfig;
use App\Repositories\Todo\TodoConfigRepository;
use Illuminate\Contracts\Validation\Rule;

class TodoCategoryName implements Rule
{
    public function passes($attribute, $value)
    {
        return app(TodoConfigRepository::class)->isTodoCategoryNameValid($value);
    }

    public function message()
    {
        return '无效的待办分类名';
    }
}