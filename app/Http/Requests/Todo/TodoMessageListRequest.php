<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2025 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/2/25 14:22
 */
namespace App\Http\Requests\Todo;

use App\Http\Requests\ApiRequest;
use App\Http\Requests\Todo\Rules\TodoCategoryName;

class TodoMessageListRequest extends ApiRequest
{
    public function rules(): array
    {
        return [
            'todo_list.*.category_name' => ['required', 'string', new TodoCategoryName()],
            'todo_list.*.content' => ['required', 'string'],
            'todo_list.*.crm_account_id' => ['required', 'int']
        ];
    }
}