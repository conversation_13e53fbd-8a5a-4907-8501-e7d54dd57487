<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2024/8/28
 */

namespace App\Http\Requests\TypicalCaseLibrary;

use App\Http\Requests\ApiRequest;
use Illuminate\Support\Facades\Request;

class CaseSaveRequest extends ApiRequest
{
    public function rules(): array
    {
        return [
            'test_result.result_type' => 'required',
            'test_result.id' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'test_result.result_type.required' => '模板类型不能为空',
            'test_result.id.required' => '模板id不能为空',
        ];
    }
}