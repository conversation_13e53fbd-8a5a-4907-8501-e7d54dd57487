<?php

namespace App\Http\Requests\TypicalCaseLibrary;

use App\Http\Requests\ApiRequest;
use App\Utils\StatusCode;

class TestTemplateSubmitReviewRequest extends ApiRequest
{

    protected $validateCode = StatusCode::DEFAULT_BUSINESS_ERROR;

    public function rules(): array
    {
        return [
            // ERP：operator 登录的crm账号
            // 供应商：merchant_id、operator 成员id、account_id 供应商/成员对应的crm账号
            'origin' => 'required|integer',
            'merchant_id' => 'nullable|integer',
            'operator' => 'nullable|integer',
            'account_id' => 'nullable|integer',

            'id' => 'required|integer',
        ];
    }
}