<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2024/8/28
 */

namespace App\Http\Requests\TypicalCaseLibrary;

use App\Http\Requests\ApiRequest;
use App\Utils\StatusCode;

class TestTemplateSaveRequest extends ApiRequest
{
    protected $validateCode = StatusCode::DEFAULT_BUSINESS_ERROR;

    public function rules(): array
    {
        return [
            'type' => 'required|integer',
            'name' => 'required|string',
            'pattern' => 'required|string',
            'operator' => 'required|integer',
            'account_id' => 'nullable|integer',
            'origin' => 'required|integer',
            'kind' => 'required|integer',
        ];
    }

    public function messages(): array
    {
        return [
            'type.required' => '模板类型不能为空',
            'name.required' => '模板名称不能为空',
            'pattern.required' => '仪器id不能为空',
            'operator.required' => '操作人ID不能为空',
            'origin.required' => '来源不能为空',
            'kind.required' => '方法分类不能为空',
        ];
    }
}