<?php

namespace App\Http\Requests\AnalysisDataTemplate;

use App\Http\Requests\ApiRequest;

class GetTemplateListRequest extends ApiRequest
{
    public function rules(): array
    {
        return [
            "provider_product_id" => "required|integer|min:1",
            "provider_merchant_id" => "integer|min:1",
            "simple_list" => "integer|in:0,1",
            "keyword" => "string|filled",
        ];
    }
}
