<?php

namespace App\Http\Requests\ProviderMerchantPayment;

use App\Http\Requests\ApiRequest;

class RejectReconciliationApplicationRequest extends ApiRequest
{
    public function rules(): array
    {
        return [
            'admin_account_id' => 'required|integer',
            'reconciliation_application_id' => 'required|integer',
            'reject_content' => 'required|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'admin_account_id.required' => '操作人id不能为空',
            'admin_account_id.integer' => '操作人id必须是整数',
            'reconciliation_application_id.required' => '核销申请id不能为空',
            'reconciliation_application_id.integer' => '核销申请id必须是整数',
            'reject_content.required' => '驳回原因不能为空',
        ];
    }
}