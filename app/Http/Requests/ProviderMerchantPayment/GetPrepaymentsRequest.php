<?php

namespace App\Http\Requests\ProviderMerchantPayment;

use App\Constants\ProviderMerchantPayment\ProviderMerchantPrepaymentConstants;
use App\Http\Requests\ApiRequest;

class GetPrepaymentsRequest extends ApiRequest
{
    public function rules(): array
    {
        $statusMap = array_keys(ProviderMerchantPrepaymentConstants::STATUS_NAME_MAP);
        $invoiceStatusMap = array_keys(ProviderMerchantPrepaymentConstants::INVOICE_STATUS_NAME_MAP);
        return [
            'page' => 'required|integer',
            'payment_mode' => 'nullable|integer',
            'apply_account_id' => 'nullable|integer',
            'merchant_name' => 'nullable',
            'provider_merchant_responsible_crm_account_id' => 'nullable|integer',
            'status' => 'nullable|integer|in:' . implode(',', $statusMap),
            'statuses' => 'nullable|array',
            'order_no' => 'nullable',
            'prepayment_id' => 'nullable|integer',
            'invoice_status' => 'nullable|integer|in:' . implode(',', $invoiceStatusMap),
            'ekuaibao_flow_code' => 'nullable',
            'reconciliation_application_id' => 'nullable|integer',
            'prepayment_ids' => 'nullable|array',
        ];
    }

    public function messages(): array
    {
        return [
            'page.required' => '页码不能为空',
            'page.integer' => '页码必须是整数',
            'payment_mode.integer' => '支付方式必须是整数',
            'apply_account_id.integer' => '申请账号ID必须是整数',
            'provider_merchant_responsible_crm_account_id.integer' => '供应商负责人CRM账号ID必须是整数',
            'status.integer' => '状态必须是整数',
            'status.in' => '状态不合法',
            'statuses.array' => 'statuses必须是数组',
            'prepayment_id.integer' => '预存id必须是整数',
            'invoice_status.integer' => '开票状态必须是整数',
            'invoice_status.in' => '开票状态不合法',
            'reconciliation_application_id.integer' => '核销申请ID必须是整数',
            'prepayment_ids.array' => '预存ids必须是数组',
        ];
    }
}