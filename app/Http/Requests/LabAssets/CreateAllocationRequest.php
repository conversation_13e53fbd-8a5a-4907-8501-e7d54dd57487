<?php

namespace App\Http\Requests\LabAssets;

use App\Http\Requests\ApiRequest;
use App\Utils\StatusCode;

class CreateAllocationRequest extends ApiRequest
{
    protected $validateCode = StatusCode::DEFAULT_BUSINESS_ERROR;

    public function rules(): array
    {
        return [
            'lab_assets_id' => 'required|integer',
            'operate_type' => 'required|integer',
            'operate_date' => 'required|date',
            'to_lab' => 'required|integer',
            'operate_reason' => 'nullable|string',
            'operator' => 'required|integer',
            'receive_date' => 'nullable|date',
        ];
    }
}