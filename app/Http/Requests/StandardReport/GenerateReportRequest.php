<?php

namespace App\Http\Requests\StandardReport;
use App\Http\Requests\ApiRequest;

/**
 * 生成报告请求
 *
 * @property int $template_id
 * @property array $order_id_list
 * @property string $report_type
 * @property array $generate_param
 * @property int $updater
 * @property int $account_id
 * @property string $scene
 * @property int $standard_report_id
 *
 */
class GenerateReportRequest extends ApiRequest {

    public function rules(): array {
        return [
            'template_id' => 'required|integer',
            'order_id_list' => 'required|array',
            'report_type' => 'required|string',
            'generate_param' => 'array',
            'account_id' => 'integer',
            'updater' => 'integer',
            'scene' => 'string|nullable',
            'standard_report_id' => 'integer|nullable',
        ];
    }

}