<?php

namespace App\Http\Requests\StandardReport;

use App\Exceptions\BusinessException;
use App\Http\Requests\ApiRequest;
use Illuminate\Contracts\Validation\Validator;

class GetOrderResultImageRequest extends ApiRequest
{

    public function rules(): array
    {
        return [
            "oid_list" => "required|string",
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = $validator->errors()->all();
        throw new BusinessException(implode(',', $errors));
    }
}