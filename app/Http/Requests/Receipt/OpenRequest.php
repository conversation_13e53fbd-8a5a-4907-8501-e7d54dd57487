<?php

namespace App\Http\Requests\Receipt;

use App\Http\Requests\ApiRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Yanqu\YanquPhplib\Invoice\Constants\InvoiceConstants;

/**
 * @property int $applyType
 * @property int $applyId
 */
class OpenRequest extends ApiRequest
{
    public function rules(): array
    {
        return [
            'applyType' => ['int',Rule::in(InvoiceConstants::INVOICE_APPLY_TYPE_ORDER,
                InvoiceConstants::INVOICE_APPLY_TYPE_PRE_STORE),'required'],
            'applyId' => ['required','int']
        ];
    }
}
