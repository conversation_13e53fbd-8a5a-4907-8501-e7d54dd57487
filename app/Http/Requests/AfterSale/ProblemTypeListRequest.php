<?php

namespace App\Http\Requests\AfterSale;

use App\Http\Requests\ApiRequest;

/**
 * @property int $oid 订单ID
 * @property int $retest_on 复测时间
 * @property int $feedback_on 反馈时间
 * @property int $supplier_on 供应商反馈时间
 * @property string $type_name 问题类型名称
 * @property bool $with_bank 是否需要银行信息
 */
class ProblemTypeListRequest extends ApiRequest {
    public function rules (): array {
        return [
            'oid'         => 'integer',
            'retest_on'   => 'integer',
            'feedback_on' => 'integer',
            'supplier_on' => 'integer',
            'type_name'   => 'string',
            'with_bank'   => 'boolean',
        ];
    }
}