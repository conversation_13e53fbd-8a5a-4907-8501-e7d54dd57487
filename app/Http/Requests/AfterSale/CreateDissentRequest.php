<?php

namespace App\Http\Requests\AfterSale;

use App\Http\Requests\ApiRequest;

/**
 * @property int oid
 * @property int post_channel
 * @property int operator_id
 * @property string desc
 * @property string ip
 * @property bool is_visible_to_customer
 *
 */
class CreateDissentRequest extends ApiRequest {

    /**
     * 参考dto
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules (): array {
        return [
            'oid'                    => 'required|integer',
            'post_channel'           => 'required|integer',
            'operator_id'            => 'required|integer',
            'desc'                   => 'required|string',
            'ip'                     => 'required|string',
            'is_visible_to_customer' => 'required|boolean',
            'attachments'            => 'array'
        ];
    }
}