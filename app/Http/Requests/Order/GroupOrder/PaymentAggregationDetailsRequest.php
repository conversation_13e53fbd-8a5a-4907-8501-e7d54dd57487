<?php

namespace App\Http\Requests\Order\GroupOrder;

use Illuminate\Foundation\Http\FormRequest;

/**
 * @property int group_id
 * @property int accountid
 * @property string search_value
 * @property int ticketstatus
 * @property int pay_way
 * @property string start_time
 * @property string end_time
 * @property int type
 * @property int debt_type
 * @property int orderstate
 * @property int crm_ticket_status
 */
class PaymentAggregationDetailsRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'group_id' => ['int', 'required'],
            'accountid' => ['string', 'nullable'],
            'search_value' => ['string', 'nullable'],
            'ticketstatus' => ['int', 'nullable'],
            'pay_way' => ['int', 'nullable'],
            'start_time' => ['date', 'nullable'],
            'end_time' => ['date', 'nullable'],
            'type' => ['int', 'required'],
            'debt_type' => ['int', 'nullable'],
            'orderstate' => ['int', 'nullable'],
            'crm_ticket_status' => ['int', 'nullable'],
        ];
    }
}
