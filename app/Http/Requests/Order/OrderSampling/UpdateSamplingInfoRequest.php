<?php

namespace App\Http\Requests\Order\OrderSampling;

use App\Http\Requests\ApiRequest;

/**
 * @property int $oid                                                                            订单ID
 * @property int $account_id                                                                     账号ID
 * @property string $deliver_day                                                                 送样时间
 * @property int $deliver_id                                                                     取样员ID
 * @property string $remind_day                                                                  提醒时间
 * @property string $operate_from                                                                操作来源
 * @property int $operator_id                                                                    操作人ID
 * @property int $sampling_id                                                                    采样ID
 * @property string $province                                                                    省份
 * @property string $city                                                                        城市
 * @property string $deliver_consignee                                                           收件人
 * @property string $deliver_mobile                                                              收件人手机号
 * @property string $university_code                                                             学校代码
 * @property string $building_id                                                                 建筑ID
 * @property int $campus_id                                                                      校区ID
 * @property string $louzhuang                                                                   楼幢
 * @property string $louceng                                                                     楼层
 * @property string $loushi                                                                      楼室
 * @property int $link_id                                                                        链接ID
 * @property int $number                                                                         样品数量
 * @property string $reason                                                                      补寄原因
 *
 */
class UpdateSamplingInfoRequest extends ApiRequest {

    public function rules (): array {
        return [
            'oid'               => 'required|integer',
            'account_id'        => 'integer|nullable',
            'deliver_id'        => 'integer',
            'deliver_day'       => 'required|string',
            'deliver_consignee' => 'required|string',
            'deliver_mobile'    => 'required|string',
            'university_code'   => 'required|string',
            'building_id'       => 'required|integer',
            'campus_id'         => 'required|integer',
            'louzhuang'         => 'required|string',
            'louceng'           => 'required|string',
            'loushi'            => 'required|string',
            'province'          => 'required|integer',
            'city'              => 'required|integer',
            'remind_day'        => 'string|nullable',
            'operate_from'      => 'integer',
            'operator_id'       => 'integer',
            'link_id'           => 'integer|nullable',
            'number'            => 'integer|nullable',
            'reason'            => 'string|nullable',
        ];
    }
}