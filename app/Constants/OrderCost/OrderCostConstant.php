<?php

namespace App\Constants\OrderCost;

class OrderCostConstant
{
    const LOG_MODULE = 'order:orderCost';

    /**
     * 判断是否允许修改成本接口的判断结果为允许时的status
     */
    const SAVE_AMOUNT_ALLOW_SUCCESS_STATUS = 100;
    /**
     * 修改成本接口修改成功时的status
     */
    const SAVE_AMOUNT_SUCCESS_STATUS = 1;

    /**
     * 修改成本的来源-更新订单的集采超期折扣
     */
    const SAVE_AMOUNT_FROM_UPDATE_ORDER_TILT_MECHANISM_OVERDUE_DISCOUNT = "updateOrderTiltMechanismOverdueDiscount";

}