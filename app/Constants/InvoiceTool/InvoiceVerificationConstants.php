<?php

namespace App\Constants\InvoiceTool;

use App\Constants\Invoice\InvoiceTypeConstants;

class InvoiceVerificationConstants
{
    /**
     * 接口提供者-百度
     */
    const API_PROVIDER_BAIDU = 1;
    /**
     * 百度验真结果-成功
     */
    const BAIDU_VERIFY_RESULT_SUCCESS = '0001';
    /**
     * 百度发票验真接口的发票类型
     */
    const BAIDU_INVOICE_VERIFY_API_INVOICE_TYPE_MAP = [
        InvoiceTypeConstants::ELECTRONIC_GENERAL => 'elec_normal_invoice',
        InvoiceTypeConstants::PAPER_GENERAL => 'normal_invoice',
        InvoiceTypeConstants::PAPER_SPECIAL => 'special_vat_invoice',
        InvoiceTypeConstants::ELECTRONIC_SPECIAL => 'elec_special_vat_invoice',
        InvoiceTypeConstants::DIGITAL_ELECTRONIC_GENERAL => 'elec_invoice_normal',
        InvoiceTypeConstants::DIGITAL_ELECTRONIC_SPECIAL => 'elec_invoice_special',
    ];

    /**
     * 百度发票验真接口的无效发票状态
     */
    const BAIDU_VERIFY_INVOICE_INVALID_STATUS_MAP = [
        'Y' => '已作废',
        'H' => '已冲红',
        'BH' => '部分红冲',
        'QH' => '全额红冲',
    ];
}