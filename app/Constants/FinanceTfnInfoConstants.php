<?php

namespace App\Constants;

use App\Services\FinanceTfInfo\HardSearch;
use App\Services\FinanceTfInfo\LevenshteinSearch;
use App\Services\FinanceTfInfo\SimilarSearch;

class FinanceTfnInfoConstants
{
    /**
     * 匹配等级 全等
     */
    const SEARCH_LEVEL_HARD = 0;

    /**
     * 匹配等级 相似
     */
    const SEARCH_LEVEL_SIMILAR = 1;

    /**
     * 匹配等级 编辑距离最低
     */
    const SEARCH_LEVEL_LEVENSHTEIN = 2;

    const CLASS_MAP = [
        self::SEARCH_LEVEL_HARD => HardSearch::class,
        self::SEARCH_LEVEL_SIMILAR => SimilarSearch::class,
        self::SEARCH_LEVEL_LEVENSHTEIN => LevenshteinSearch::class
    ];

    const SEARCH_LEVEL_MAP = [
        self::SEARCH_LEVEL_HARD,
        self::SEARCH_LEVEL_SIMILAR,
        self::SEARCH_LEVEL_LEVENSHTEIN
    ];

    /**
     * 诺诺验证 未认证
     */
    const NUONUO_ACCREDITATION_WITHOUT = 0;

    /**
     * 诺诺认证 已通过
     */
    const NUONUO_ACCREDITATION_PASS = 1;

    /**
     * 诺诺认证 未通过
     */
    const NUONUO_ACCREDITATION_NOT_PASS = 2;
}