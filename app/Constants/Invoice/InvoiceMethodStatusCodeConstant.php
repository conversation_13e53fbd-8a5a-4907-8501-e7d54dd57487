<?php

namespace App\Constants\Invoice;

use App\Constants\NuoNuoConstants;
use App\Utils\StatusCode;

/**
 * 发票返回状态码与业务返回状态码映射
 */
class InvoiceMethodStatusCodeConstant
{
    const COUPON_OPEN_TICKET_MAP = [
        NuoNuoConstants::METHOD_SUCCESS_CODE => StatusCode::INVOICE_COUPON_OPEN_TICKET_SUCCESS,
        NuoNuoConstants::METHOD_OPEN_TICKET_ORDER_NO_REPEATABLE => StatusCode::INVOICE_COUPON_TICKET_REPEAT_ORDERNO,
        NuoNuoConstants::SERVICE_SUCCESS_WAITING => StatusCode::INVOICE_COUPON_RED_CONFIRM_PROGRESS,
        NuoNuoConstants::SERVICE_INVOICING => StatusCode::INVOICE_COUPON_RED_INVOICING,
        NuoNuoConstants::SERVICE_INVOICE_FAIL => StatusCode::INVOICE_COUPON_RED_INVOICE_FAIL,
        NuoNuoConstants::SERVICE_INVOCIE_NULLIFY => StatusCode::INVOICE_COUPON_INVOICE_NULLIFY,
    ];

    const ORDER_OPEN_TICKET_MAP = [
        NuoNuoConstants::METHOD_SUCCESS_CODE => StatusCode::INVOICE_ORDER_OPEN_TICKET_SUCCESS,
        NuoNuoConstants::METHOD_OPEN_TICKET_ORDER_NO_REPEATABLE => StatusCode::INVOICE_ORDER_TICKET_REPEAT_ORDERNO,
        NuoNuoConstants::SERVICE_SUCCESS_WAITING => StatusCode::INVOICE_ORDER_RED_CONFIRM_PROGRESS,
        NuoNuoConstants::SERVICE_INVOICING => StatusCode::ORDER_RED_INVOICING,
        NuoNuoConstants::SERVICE_INVOICE_FAIL => StatusCode::ORDER_RED_INVOICE_FAIL,
        NuoNuoConstants::SERVICE_INVOCIE_NULLIFY => StatusCode::ORDER_INVOICE_NULLIFY,
    ];

    const REFRESH_RED_CONFIRM_MAP = [
        NuoNuoConstants::METHOD_SUCCESS_CODE => StatusCode::INVOICE_GET_TICKET_STATUS_SUCCESS,
        NuoNuoConstants::SERVICE_SUCCESS_WAITING => StatusCode::INVOICE_RED_CONFIRM_PROGRESS,
        NuoNuoConstants::SERVICE_NO_PROCESS_REQUIRE => StatusCode::INVOICE_RED_CONFIRM_FINISHED,
        NuoNuoConstants::SERVICE_INVOICING => StatusCode::INVOICE_GET_TICKET_STATUS_WAIT,
        NuoNuoConstants::SERVICE_INVOICE_FAIL => StatusCode::INVOICE_GET_TICKET_STATUS_FALSE,
        NuoNuoConstants::SERVICE_INVOCIE_NULLIFY => StatusCode::INVOICE_GET_TICKET_STATUS_NULLIFY,
        NuoNuoConstants::SERVICE_INVOICE_RED_CONFIRM_CANCEL => StatusCode::INVOICE_CANCEL_CONFIRM_SUCCESS,
        NuoNuoConstants::SERVICE_INVOICE_RED_CONFIRM_APPLY_FAIL => StatusCode::RED_CONFIRM_APPLY_FAIL,
    ];
}