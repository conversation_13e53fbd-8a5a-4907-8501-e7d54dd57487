<?php

namespace App\Constants\ProviderMerchantPayment;

class ProviderMerchantPaymentErrorLogConstants
{
    /**
     * 申请类型-预存
     */
    const APPLICATION_TYPE_PREPAYMENT = 1;
    /**
     * 申请类型-结算
     */
    const APPLICATION_TYPE_SETTLEMENT = 2;
    /**
     * 申请类型-发票核销单据
     */
    const APPLICATION_TYPE_INVOICE_RECONCILIATION = 3;

    /**
     * 步骤-提交预存
     */
    const STEP_SUBMIT_PREPAYMENT = 1;
    /**
     * 步骤-驳回预存
     */
    const STEP_REJECT_PREPAYMENT = 2;
    /**
     * 步骤-生成核销单
     */
    const STEP_CREATE_RECONCILIATION = 3;
    /**
     * 步骤-驳回核销单
     */
    const STEP_REJECT_INVOICE_RECONCILIATION = 4;
    /**
     * 步骤-供应商专员审核结算申请
     */
    const STEP_AUDIT_SETTLEMENT_BY_SUPPLIER_SPECIALIST = 5;

    const STEP_NAME_MAP = [
        self::STEP_SUBMIT_PREPAYMENT => '提交预存',
        self::STEP_REJECT_PREPAYMENT => '驳回预存',
        self::STEP_CREATE_RECONCILIATION => '生成核销单',
        self::STEP_REJECT_INVOICE_RECONCILIATION => '驳回核销单',
        self::STEP_AUDIT_SETTLEMENT_BY_SUPPLIER_SPECIALIST => '提交结算',
    ];
}