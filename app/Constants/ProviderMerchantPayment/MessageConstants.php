<?php

namespace App\Constants\ProviderMerchantPayment;

class MessageConstants
{
    const PREPAYMENT_NOT_EXIST = '预存申请不存在';
    const PREPAYMENT_AUTO_PROCESS_CANNOT_SAVE_EKUAIBAO_FLOW_CODE = '系统自动处理的预存申请不能修改易快报单号';
    const PREPAYMENT_STATUS_NOT_ALLOW_TO_SAVE_EKUAIBAO_FLOW_CODE = '预存申请状态为%s，不能填写易快报单号';
    const EKUAIBAO_FLOW_NOT_EXIST = '易快报单据不存在';
    const EKUAIBAO_AMOUNT_NOT_EQUAL = '此易快报单据金额%s与申请金额%s不一致，请检查';
    const EKUAIBAO_WRITTEN_OFF_AMOUNT_NOT_EQUAL = '此易快报单据核销金额%s与核销申请金额%s不一致，请检查';
    const EKUAIBAO_PAYEE_CARD_NO_NOT_EQUAL = '此易快报单据收款卡号[%s]与系统收款卡号[%s]不一致，请检查';
    const EKUAIBAO_FLOW_ID_DUPLICATE_WITH_PREPAYMENT = '与预存申请[%s]的易快报单据重复，请检查';
    const EKUAIBAO_FLOW_ID_DUPLICATE_WITH_RECONCILIATION = '与核销申请[%s]的易快报单据重复，请检查';
    const PREPAYMENT_STATUS_NOT_ALLOW_TO_CREATE_EKUAIBAO_FLOW = '预存申请状态为%s，不能创建易快报单据';
    const PREPAYMENT_MERCHANT_NOT_SAME = '预存申请必须是相同的供应商主体';
    const PREPAYMENT_STATUS_NOT_SAME = '预存申请的状态必须一致';
    const PREPAYMENT_PAYMENT_MODE_NOT_ALLOW_UPLOAD_INVOICE = '非对公的预存申请不能上传发票';
    const PREPAYMENT_INVOICE_STATUS_NOT_SAME = '预存申请的发票状态必须一致';
    const PREPAYMENT_ALREADY_INVOICE = '已开票的预存申请不能再次关联发票';
    const PREPAYMENT_PARTIAL_INVOICE_CANNOT_UPLOAD_INVOICE_WITH_OTHERS = '部分开票的预存申请不能和其他预存申请一起上传发票';
    const PREPAYMENT_SIGNATORY_NOT_SAME = '预存申请的签约主体必须一致';
    const SETTLE_INFO_NOT_EMPTY = '结算信息不能为空';
    const SETTLE_INFO_NOT_SAME = '结算信息必须一致';
    const EKUAIBAO_FLOW_CODE_EXIST = '已经创建过易快报单据，不能再次创建';
    const EKUAIBAO_STAFF_NOT_FOUND = '易快报员工不存在，请检查是否配置了易快报账号的手机号或邮箱';
    const RECONCILIATION_NOT_EXIST = '核销申请不存在';
    const RECONCILIATION_STATUS_NOT_ALLOW_TO_SAVE_EKUAIBAO_FLOW_CODE = '核销申请状态为%s，不能填写易快报单号';
    const RECONCILIATION_AUTO_PROCESS_CANNOT_SAVE_EKUAIBAO_FLOW_CODE = '系统自动处理的核销申请不能修改易快报单号';
    const PREPAYMENT_STATUS_NOT_ALLOW_TO_UPDATE_TO_PAID = '预存申请状态不是已申请状态，不能更新为已打款状态';
    const PREPAYMENT_AMOUNT_AND_CALLBACK_NOT_EQUAL = '预存申请金额与易快报回调金额不一致，不能更新为已打款状态';
    const UPDATE_PREPAYMENT_STATUS_TO_PAID_FAILED = '更新预存申请状态为已打款失败';
    const RECONCILIATION_STATUS_NOT_ALLOW_TO_UPDATE_TO_PAID = '核销申请状态不是已确认状态，不能更新为已打款状态';
    const RECONCILIATION_AND_CALLBACK_AMOUNT_NOT_EQUAL = '发票核销申请金额与易快报回调金额不一致，不能更新为已打款状态';
    const SETTLE_STATUS_NOT_ALLOW_TO_UPDATE_TO_PAID = '结算申请状态不是待结算状态，不能更新为已打款状态';
    const SETTLE_PAYABLE_AMOUNT_AND_CALLBACK_AMOUNT_NOT_EQUAL = '结算申请应付金额与易快报回调金额不一致，不能更新为已打款状态';
    const UPDATE_SETTLE_STATUS_TO_PAID_FAILED = '更新结算申请状态为已打款失败';
    const INVALID_OPERATOR = '无效的操作人';
    const REJECT_RECONCILIATION_REASON = '请填写驳回核销原因';
    const RECONCILIATION_STATUS_NOT_ALLOW = '核销申请状态不正确';
    const RECONCILIATION_NOT_PREPAYMENT = '核销申请不是预存类型，无法创建预存申请的核销单据';
    const RECONCILIATION_NOT_SETTLEMENT = '核销申请不是结算类型，无法创建结算申请的核销单据';
    const RECONCILIATION_STATUS_NOT_ALLOW_TO_REUPLOAD_INVOICE = '核销申请已驳回时才能重新上传发票';
    const ORDER_SETTLE_NOT_EXIT = '结算申请不存在';
    const ORDER_SETTLE_AUTO_PROCESS_CANNOT_SAVE_EKUAIBAO_FLOW_CODE = '系统自动处理的结算申请不能修改易快报单号';
    const ORDER_SETTLE_STATUS_NOT_ALLOW_TO_SAVE_EKUAIBAO_FLOW_CODE = '结算申请状态为[%s]，不能填写易快报单号';
    const EKUAIBAO_FLOW_ID_DUPLICATE_WITH_SETTLE = '与结算申请[%s]的易快报单据重复，请检查';
    const PREPAYMENT_ID_OR_RECONCILIATION_ID_OR_SETTLE_ID_REQUIRED = '预存ID、核销申请ID和结算申请ID必填一个';
    const EKUAIBAO_AMOUNT_NOT_EQUAL_TO_SETTLE = '此易快报单据金额[%s]与待打款金额[%s]不一致，请检查';
    const PENDING_PAYMENT_AMOUNT_IS_ZERO_FOR_EKUAIBAO_FLOW_CODE = '待打款金额为0，无需填写易快报单号';
    const SYSTEM_RECEIVE_CARD_NO_IS_EMPTY = '系统收款卡号为空，请检查';
    const ORDER_SETTLE_STATUS_NOT_ALLOW_TO_CREATE_EKUAIBAO_FLOW = '结算申请状态为[%s]，不能创建易快报单据';
    const SETTLE_INFO_NOT_BANK_CARD = '请核对账户信息是否是银行卡';
    const SETTLE_INFO_NOT_CORPORATE = '请核对账户信息是否是对公信息';
    const EKUAIBAO_STAFF_NOT_CONFIGURED = '未查询到当前crm账号对应的易快报员工信息，请确认是否已在易快报上配置邮箱或手机号';
    const EKUAIBAO_PAYEE_INFO_NOT_CONFIGURED = '易快报单据收款账户未配置，请检查';
    const EKUAIBAO_PAYEE_NAME_NOT_EQUAL = '易快报单据收款账户名称[%s]与系统收款账户名称[%s]不一致，请检查';
    const EKUAIBAO_FLOW_STEP_NOT_SUPPORT = '当前流程步骤为[%s]，不支持操作，请检查';
    const SETTLE_HAS_INVALID_SETTLE = '存在无效的结算申请，请检查';
    const UPLOAD_INVOICE_DISABLED_FOR_LEGACY_SETTLEMENT = '上传发票功能上线前的结算申请无法上传发票，请检查';

    const SETTLE_MERCHANT_NOT_SAME = '结算申请必须是相同的供应商主体';
    const SETTLE_PAYMENT_MODE_NOT_ALLOW_UPLOAD_INVOICE = '非对公的结算申请不能上传发票';
    const SETTLE_STATUS_NOT_SAME = '结算申请的状态必须一致';
    const SETTLE_INVOICE_STATUS_NOT_SAME = '结算申请的发票状态必须一致';
    const SETTLE_ALREADY_INVOICE = '结算申请[%s]已开票，不能再次关联发票';
    const SETTLE_PARTIAL_INVOICE_CANNOT_UPLOAD_INVOICE_WITH_OTHERS = '部分开票的结算申请[%s]不能和其他结算申请一起上传发票';
    const SETTLE_NO_INVOICE_NEEDED = '结算申请[%s]无需开票，不能上传发票';
    const SETTLE_CAN_ONLY_UPLOAD_ONE_INVOICE = '多笔结算申请只能上传一张发票';
    const SETTLE_INVOICE_AMOUNT_MISMATCH = '发票总金额[%s]与待上传发票金额金额[%s]不一致，请检查';
    const SETTLE_INVOICE_AMOUNT_MUST_LESS_THAN_OR_EQUAL_TO_PENDING_PAYMENT_AMOUNT = '发票金额必须小于等于待打款金额';
}