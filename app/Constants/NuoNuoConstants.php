<?php

namespace App\Constants;

class NuoNuoConstants
{
    /**
     * 红字确认单申请函数
     */
    const METHOD_RED_CONFIRM = 'nuonuo.OpeMplatform.saveInvoiceRedConfirm';

    /**
     * 申请开票函数
     */
    const METHOD_APPLY_INVOICE = 'nuonuo.OpeMplatform.requestBillingNew';

    /**
     * 申请开票函数-除全电开票外
     */
    const METHOD_APPLY_INVOICE_ELECTRONIC = 'nuonuo.ElectronInvoice.requestBillingNew';
    /**
     * 查询红字确认单函数
     */
    const METHOD_QUERY_RED_CONFIRM = 'nuonuo.OpeMplatform.queryInvoiceRedConfirm';

    /**
     * 取消红字确认单函数
     */
    const METHOD_CANCEL_RED_CONFIRM = 'nuonuo.OpeMplatform.confirmInfoCancel';


    const METHOD_FAST_INVOICE_RED = 'nuonuo.OpeMplatform.fastInvoiceRed';

    /**
     * 发票列表查询接口
     */
    const METHOD_QUERY_INVOICE_LIST = 'nuonuo.OpeMplatform.queryInvoiceList';

    const METHOD_QUERY_INVOICE_RESULT = 'nuonuo.OpeMplatform.queryInvoiceResult';

    /**
     * 关键字查询名称及开票代码接口
     */
    const METHOD_QUERY_PREFIX = 'nuonuo.speedBilling.prefixQuery';


    /**
     * 开票代码查询名称及税号接口
     */
    const METHOD_QUERY_NAME_AND_TAX_BY_CODE = 'nuonuo.speedBilling.queryNameAndTaxByCode';

    /**
     * 开收据
     */
    const METHOD_OPEN_RECEIPT = 'nuonuo.OpeMplatform.sj';

    /**
     * 作废收据
     */
    const METHOD_CANCEL_RECEIPT = 'nuonuo.OpeMplatform.invalidElectronic';

    /**
     * 查询收据
     */
    const METHOD_QUERY_RECEIPT = 'nuonuo.OpeMplatform.queryInfos';

    /**
     * 诺诺返回成功code
     */
    const METHOD_SUCCESS_CODE = 'E0000';

    const METHOD_FAIL_CODE = 'E9500';

    /**
     * 诺诺开票order_no重复
     */
    const METHOD_OPEN_TICKET_ORDER_NO_REPEATABLE = 'E9106';

    /**
     *  诺诺speedBilling模块成功返回code
     */
    const METHOD_SPEED_BILLING_SUCCESS_CODE = 'S0000';

    /**
     * 诺诺业务返回
     */
    const SERVICE_ERROR_CODE = 'F4000';

    /**
     * 诺诺业务等待code
     */
    const SERVICE_SUCCESS_WAITING = 'F0000';

    /**
     * 没有正在进行的红字确认单
     */
    const SERVICE_NO_PROCESS_REQUIRE = 'F0001';

    /**
     * 诺诺红票开票中
     */
    const SERVICE_INVOICING = 'F0002';


    /**
     * 发票开票失败
     */
    const SERVICE_INVOICE_FAIL = 'F0003';

    /**
     * 发票已作废
     */
    const SERVICE_INVOCIE_NULLIFY = 'F0004';

    const SERVICE_INVOICE_RED_CONFIRM_CANCEL = 'F0005';

    /**
     * 红字确认单申请失败
     */
    const SERVICE_INVOICE_RED_CONFIRM_APPLY_FAIL = 'F0006';

    /**
     * 全电开票token类型
     */
    const TOKEN_TYPE_FULL_ELECTRIC = 9;

    /**
     * 除全电开票外token类型
     */
    const TOKEN_TYPE_ELECTRON_INVOICE = 6;

    /**
     * 快速开票应用app token类型
     */
    const TOKEN_TYPE_FAST_INVOICE = 10;


    /**
     * 电子发票(增值税专用发票)-即数电专票(电子)
     */
    const ELECTRONIC_VAT_SPECIAL_INVOICES = 'bs';

    /**
     * 电子发票(普通发票)-即数电普票(电子)
     */
    const ELECTRONIC_VAT_GENERAL_INVOICES = 'pc';

    /**
     * 全电纸质发票(增值税专用发票)
     */
    const ELECTRONIC_SPECIAL_INVOICES = 'es';

    /**
     * 全电纸质发票(普通发票)
     */
    const ELECTRONIC_GENERAL_INVOICES = 'ec';

    /**
     * 普通发票（电票）
     */
    const GENERAL_ELECTRONIC_INVOICES = 'p';

    /**
     * 普通发票（纸票）
     */
    const GENERAL_PAPER_INVOICES = 'c';

    /**
     * 专用发票
     */
    const SPECIAL_INVOICES = 's';

    /**
     * 增值税电子专用发票
     */
    const VAT_ELECTRONIC_SPECIAL_INVOICES = 'b';

    /**
     * 数电票发票类型列表
     */
    const ELECTRONIC_VAT_INVOICE_TYPE_LIST = [
        self::ELECTRONIC_VAT_SPECIAL_INVOICES,
        self::ELECTRONIC_VAT_GENERAL_INVOICES,
        self::ELECTRONIC_SPECIAL_INVOICES,
        self::ELECTRONIC_GENERAL_INVOICES
    ];

    /**
     * 申请方身份 销方
     */
    const APPLY_SOURCE_SALE = 0;

    const QUERY_LIST_RESULT_MAX_CODE = 'E1007';

    const QUERY_LIST_BUSY_CODE = 'E1009';

    const QUERY_NOT_THROW_CODE_ARRAY = [
        self::QUERY_LIST_RESULT_MAX_CODE,
        self::QUERY_LIST_BUSY_CODE
    ];

    /**
     * 发票请求列表 0为按照订单添加时间查询
     */
    const QUERY_LIST_REQUEST_TYPE_ORDER_TIME = 0;
    /**
     * 发票请求列表 1为按照开票时间查询
     */
    const QUERY_LIST_REQUEST_TYPE_INVOICE_TIME = 1;
    const QUERY_LIST_PAGE_SIZE = 200;

    /**
     * 红冲时使用的发票类型
     */
    const REP_INVOICE_LINE = [
        self::GENERAL_ELECTRONIC_INVOICES => self::ELECTRONIC_VAT_GENERAL_INVOICES,
        self::VAT_ELECTRONIC_SPECIAL_INVOICES => self::ELECTRONIC_VAT_SPECIAL_INVOICES,
        self::GENERAL_PAPER_INVOICES => self::ELECTRONIC_VAT_GENERAL_INVOICES,
        self::SPECIAL_INVOICES => self::ELECTRONIC_VAT_SPECIAL_INVOICES,
    ];
}