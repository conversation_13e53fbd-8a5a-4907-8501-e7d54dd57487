<?php

namespace App\Exceptions;

use Throwable;
use Yanqu\YanquPhplib\YqLog\YqLog;

class BusinessException extends BaseBusinessException {
    protected $logModule;

    protected $context = [];

    public function __construct (
        string     $message = "",
        int        $code = 99,
        ?Throwable $previous = null,
        string     $logModule = 'openapi',
        array $context = []
    ) {
        $this->logModule = $logModule;
        $this->context = $context;
        parent::__construct($message, $code, $previous);
    }

    public function setContext(array $context = [])
    {
        $this->context = $context;
        return $this;
    }

    public function report () {
        /*
         * Throwable 与 exception问题
         */
        YqLog::logger($this->logModule)
            ->error($this->getMessage(),
                [
                    'context'  => $this->context,
                    'code'     => $this->getCode(),
                    'file'     => $this->getFile(),
                    'line'     => $this->getLine(),
                    'trace'    => $this->getTraceAsString(),
                    'previous' => $this->getPrevious() ? $this->getPrevious()->getMessage() : '',
                ]
            );
    }
}
