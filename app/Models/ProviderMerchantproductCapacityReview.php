<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * 
 *
 * @property int $id 测试项目容量审核id
 * @property int $providermerchantid 供应商id，sci_provider_merchant.providermerchantid
 * @property int $merchantproductid 供应商测试项目id，sci_provider_merchantproduct.merchantproductid
 * @property int $status 状态 0待处理 1已处理
 * @property int $instrument_number 仪器数量
 * @property int $capacity_of_tests_per_uni_per_day 每台每天测试样品数
 * @property string|null $review_time 审核时间
 * @property int $auditor 审核人id
 * @property string $create_time 创建时间
 * @property string $update_time 更新时间
 * @property int $type 业务类型 1容量审核 2符合实情
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview whereAuditor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview whereCapacityOfTestsPerUniPerDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview whereInstrumentNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview whereMerchantproductid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview whereProvidermerchantid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview whereReviewTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantproductCapacityReview whereUpdateTime($value)
 * @mixin \Eloquent
 */
class ProviderMerchantproductCapacityReview extends Model
{

    public $timestamps = false;

    protected $table = 'provider_merchantproduct_capacity_review';

    protected $primaryKey = 'id';
}
