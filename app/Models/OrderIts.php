<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2024 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/4/1 16:42
 */
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\OrderIts
 *
 * @property int $itid 自增主键
 * @property int|null $itorderid
 * @property string|null $itorderno
 * @property string|null $itcontacter
 * @property string|null $itmobile
 * @property string|null $it_email
 * @property int|null $adtime
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderIts whereAdtime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderIts whereItcontacter($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderIts whereItid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderIts whereItmobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderIts whereItorderid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderIts whereItorderno($value)
 */
class OrderIts extends Model
{
    protected $table = 'order_its';

    protected $primaryKey = 'id';

    public $timestamps = false;
}