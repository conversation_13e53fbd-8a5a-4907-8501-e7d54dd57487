<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


/**
 * 
 *
 * @property int $audit_id 审核记录id
 * @property int $providermerchantid 供应商id
 * @property int|null $crm_account_id ERP系统发起申请的用户
 * @property int $modify_status 供应商资料变更处理状态，1-待审核/2-撤销申请/3-申请驳回/4-审核通过/5-免审核
 * @property string $detail 供应商资料变更审核提交的档案详情信息（json格式）
 * @property int $platform 发起申请的平台，1-供应商系统，2-ERP
 * @property int $create_time 变更审核记录创建时间
 * @property int $update_time 变更审核记录更新时间
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantModifyAudit newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantModifyAudit newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantModifyAudit query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantModifyAudit whereAuditId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantModifyAudit whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantModifyAudit whereCrmAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantModifyAudit whereDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantModifyAudit whereModifyStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantModifyAudit wherePlatform($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantModifyAudit whereProvidermerchantid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProviderMerchantModifyAudit whereUpdateTime($value)
 * @mixin \Eloquent
 */
class ProviderMerchantModifyAudit extends Model
{

    public $timestamps = false;

    protected $table = 'provider_merchant_modify_audit';

    protected $primaryKey = 'audit_id';
}
