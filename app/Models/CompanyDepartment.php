<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\CompanyDepartment
 *
 * @property int $id
 * @property int $company_id 公司id 对应sci_company表的id
 * @property string $name 部门名称
 * @property string $direction 研发方向
 * @property string $sample_type 样品类型
 * @property int $instrument_status 自有仪器 1仪器较全，2有仪器但不全，3无仪器，4不清楚
 * @property string $instrument_detail 自有仪器详细说明
 * @property int $cooperating_suppliers_status 供应商情况 1深度合作，2合作不善，3无供应商，4不清楚
 * @property string $supplier_detail 供应商详细说明
 * @property int $overflow_demand 外溢需求 1机会很多，2可能有机会，3机会渺茫
 * @property string $overflow_demand_detail 外溢需求详细说明
 * @property string $cooperation 合作情况 多选 1已签框架，2已签竞标，3已建团体，4已做预存款，5已入驻供应商
 * @property string $cooperation_detail 合作情况详情
 * @property int $annual_budget 部门年预算 1.0-10万，2.10-20万，3.20-50万，4.50万以上
 * @property string $annual_budget_detail 部门年预算详细说明
 * @property int $repayment_cycle 还款周期 1.月结 2.季度结 3.半年结 4.年结5.随报随销(1个月内)6.其他
 * @property string $repayment_cycle_detail 还款周期详细说明
 * @property int $purchase_plan 采购计划 1.有采购划 2.无计划 3.不清楚
 * @property string $purchase_plan_detail 采购计划详细说明
 * @property string $breakthrough_direction 突破方向
 * @property string|null $remark 部门备注
 * @property int $isvoid
 * @property string $create_time 创建时间
 * @property string $update_time 更新时间
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereAnnualBudget($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereAnnualBudgetDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereBreakthroughDirection($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereCooperatingSuppliersStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereCooperation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereCooperationDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereDirection($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereInstrumentDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereInstrumentStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereOverflowDemand($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereOverflowDemandDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment wherePurchasePlan($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment wherePurchasePlanDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereRepaymentCycle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereRepaymentCycleDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereSampleType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereSupplierDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\CompanyDepartment whereUpdateTime($value)
 * @mixin \Eloquent
 */
class CompanyDepartment extends Model
{
    public $timestamps = false;

    protected $table = 'company_department';

    protected $guarded = [];
}
