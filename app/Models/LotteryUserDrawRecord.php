<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LotteryUserDrawRecord extends Model
{
    protected $table = 'lottery_user_draw_record';

    public $timestamps = false;

    /**
     * 获取抽奖记录关联的收货地址
     */
    public function address()
    {
        return $this->hasOne(LotteryUserAddress::class, 'id', 'address_id');
    }

    /**
     * 获取抽奖记录关联的奖品信息
     */
    public function prize()
    {
        return $this->hasOne(LotteryActivityPrize::class, 'id', 'prize_id');
    }

    /**
     * 获取抽奖记录关联的活动信息
     */
    public function activity()
    {
        return $this->hasOne(LotteryActivity::class, 'id', 'activity_id');
    }
}