<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class GroupSpend extends Model
{
    protected $table = "group_spend";
    protected $primaryKey = 'spendid';
    public $timestamps = false;

    const STATUS_APPLY = 1;
    const STATUS_CONFIRM = 2;
    const STATUS_COMPLETED = 100;
    const STATUS_CANCELLED_MANAGER = 97;
    const STATUS_CANCELLED_USER = 98;
    const STATUS_CANCELLED_ADMIN = 99;
    /**
     * 根据消费的订单号查找消费记录
     * @param $orderId
     * @return array
     */
    public static function getConsumeDataByOrderId($orderId): array
    {
        return self::query()
            ->where('orderid', $orderId)
            ->pluck('spendid')
            ->toArray();
    }

    public static function getCancelledData($updateTime): array
    {
        return self::query()
            ->whereIn('status', [
                self::STATUS_CANCELLED_MANAGER,
                self::STATUS_CANCELLED_USER,
                self::STATUS_CANCELLED_ADMIN
            ])
            ->where('update_time', '>=', $updateTime)
            ->get()
            ->toArray();
    }

    public function getGroupSpend($groupIdArr)
    {
        if(empty($groupIdArr)){
            return [];
        }
        return self::query()
            ->whereIn('supplierid', $groupIdArr)
            ->whereIn('status', [self::STATUS_APPLY, self::STATUS_CONFIRM, self::STATUS_COMPLETED])
            ->groupBy('supplierid')
            ->select(DB::raw('sum(amount) as amount'), 'supplierid')
            ->get()
            ->toArray();
    }
}
