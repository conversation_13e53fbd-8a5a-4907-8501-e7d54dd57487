<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\NanometerHazardousCas
 *
 * @property int|null $id
 * @property string|null $name
 * @property string|null $aliasname
 * @property string|null $cas
 * @property string|null $categoryname
 * @property string|null $type
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerHazardousCas newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerHazardousCas newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerHazardousCas query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerHazardousCas whereAliasname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerHazardousCas whereCas($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerHazardousCas whereCategoryname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerHazardousCas whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerHazardousCas whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerHazardousCas whereType($value)
 * @mixin \Eloquent
 */
class NanometerHazardousCas extends Model
{
    protected $table            = "nanometer_hazardous_cas";
    protected $primaryKey       = 'id';
    public $timestamps          = false;
}