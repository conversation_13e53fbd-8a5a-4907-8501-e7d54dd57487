<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2024/8/28
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\TypicalCaseTestTemplatePatternRelation
 *
 * @property int $id id
 * @property int $template_id 模板类型
 * @property int $pattern_id 仪器id
 * @property int|null $isvoid 是否可用
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestTemplatePatternRelation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestTemplatePatternRelation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestTemplatePatternRelation query()
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestTemplatePatternRelation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestTemplatePatternRelation whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestTemplatePatternRelation wherePatternId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestTemplatePatternRelation whereTemplateId($value)
 * @mixin \Eloquent
 */
class TypicalCaseTestTemplatePatternRelation extends Model
{
    protected $table = "typical_case_test_template_and_pattern_relation";
    protected $primaryKey = "id";
    public $timestamps = false;
    protected $guarded = []; // 不可以注入数据字段
}