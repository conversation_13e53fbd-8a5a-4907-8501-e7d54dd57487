<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\NanometerBrand
 *
 * @property int $brandid 纳米材料 品牌
 * @property string $name 品牌名称
 * @property string $letter 品牌名称前两位拼音大写
 * @property string|null $img 品牌图片地址
 * @property int|null $addtime 添加时间
 * @property int|null $isvoid 0正常/1作废
 * @property int $category_id 类型1化学试剂2生物试剂3科研仪器4科研耗材
 * @property int $sort 从小到大排序
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerBrand newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerBrand newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerBrand query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerBrand whereAddtime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerBrand whereBrandid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerBrand whereCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerBrand whereImg($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerBrand whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerBrand whereLetter($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerBrand whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerBrand whereSort($value)
 * @mixin \Eloquent
 */
class NanometerBrand extends Model
{
    protected $table = "nanometer_brand";
    protected $primaryKey = 'brandid';
    public $timestamps = false;
}
