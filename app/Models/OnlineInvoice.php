<?php


namespace App\Models;


use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id 自增主键
 * @property int|null $type 1订单发票/2预存发票
 * @property int|null $applyid sci_finance_ticketapply.applyid
 * @property int|null $cticketid sci_account_couponticket.cticketid
 * @property string|null $invoice_serial_num 发票流水号
 * @property string|null $order_no 订单号
 * @property string|null $order_date 订单时间
 * @property string|null $invoice_line 发票种类:p,普通发票(电票)(默认);c,普通发票(纸票);s,专用发票;e,收购发票(电票);f,收购发票(纸质);r,普通发票(卷式)
 * @property string|null $invoice_dm 发票代码
 * @property string|null $invoice_no 发票号码
 * @property string|null $pdf_url pdf地址
 * @property string|null $buyer_name 购方名称
 * @property string|null $buyer_tax_number 购方税号
 * @property string|null $buyer_tel 购方电话
 * @property string|null $buyer_address 购方企业地址
 * @property string|null $buyer_account 购方企业银行开户行及账号
 * @property string|null $saler_tax_num 销方企业税号
 * @property string|null $saler_tel 销方企业电话
 * @property string|null $saler_address 销方企业地址
 * @property string|null $saler_account 销方企业银行开户行及账号
 * @property string|null $invoice_type 开票类型:1,正票;2,红票
 * @property string|null $remark 备注信息
 * @property string|null $clerk 开票员
 * @property string|null $payee 收款人
 * @property string|null $checker 复核人
 * @property string|null $rep_invoice_code 冲红时填写的对应蓝票发票代码
 * @property string|null $rep_invoice_num 冲红时填写的对应蓝票发票号码
 * @property string|null $push_mode 推送方式:-1,不推送;0,邮箱;1,手机（默认）;2,邮箱、手机
 * @property int|null $status 0暂未查到开票结果/1开票成功/2开票失败
 * @property int|null $is_void 0作废/1正常
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice query()
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereApplyid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereBuyerAccount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereBuyerAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereBuyerName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereBuyerTaxNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereBuyerTel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereChecker($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereClerk($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereCticketid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereInvoiceDm($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereInvoiceLine($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereInvoiceNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereInvoiceSerialNum($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereInvoiceType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereIsVoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereOrderDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereOrderNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice wherePayee($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice wherePdfUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice wherePushMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereRepInvoiceCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereRepInvoiceNum($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereSalerAccount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereSalerAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereSalerTaxNum($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereSalerTel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineInvoice whereType($value)
 * @mixin Model
 */
class OnlineInvoice extends Model
{
    protected $table = "online_invoice";
    protected $primaryKey = 'id';
    public $timestamps = false;

}
