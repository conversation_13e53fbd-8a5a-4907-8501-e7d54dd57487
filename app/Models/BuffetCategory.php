<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\BuffetCategory
 *
 * @property int $categoryid 自增主键
 * @property string|null $catename
 * @property int|null $parentid 父节点id
 * @property int|null $sort
 * @property string|null $ticketvals 开票内容
 * @property int|null $adtime
 * @property int|null $isvoid 0正常/1作废
 * @property int $is_show 是否pc展示，1显示，0隐藏
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BuffetCategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BuffetCategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BuffetCategory query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BuffetCategory whereAdtime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BuffetCategory whereCategoryid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BuffetCategory whereCatename($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BuffetCategory whereIsShow($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BuffetCategory whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BuffetCategory whereParentid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BuffetCategory whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\BuffetCategory whereTicketvals($value)
 * @mixin \Eloquent
 */
class BuffetCategory extends Model
{
    protected $table = "buffet_category";
    protected $primaryKey = 'categoryid';
    public $timestamps = false;
}
