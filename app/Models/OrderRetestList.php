<?php


namespace App\Models;


use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\OrderRetestList
 *
 * @property int $listid 自增主键
 * @property int|null $origin_oid 源订单OID
 * @property int $oid 订单id
 * @property string $test_type afterSale为售后，reTest为换仪器复测
 * @property int $test_source 置为复测来源：默认1正常置为复测，2提交异议置为复测
 * @property int|null $add_time 新增时间
 * @property int|null $delete_time 软删时间时间
 * @property int|null $status 1正常，0代表废弃
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderRetestList newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderRetestList newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderRetestList query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderRetestList whereAddTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderRetestList whereDeleteTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderRetestList whereListid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderRetestList whereOid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderRetestList whereOriginOid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderRetestList whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderRetestList whereTestSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderRetestList whereTestType($value)
 * @mixin \Eloquent
 */
class OrderRetestList extends Model
{
    protected $table = "order_retest_list";
    protected $primaryKey = 'listid';
    public $timestamps = false;

    /**
     * status状态
     */
    const RECORD_STATUS_NORMAL = 1;
}


