<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id id
 * @property int $oid 订单id
 * @property string $osn 订单编号
 * @property string $reverse_osn 订单编号
 * @property string $promotionpid 绑定推广的产品id
 * @property string $providermerchantremark 供应商系统增加的订单备注
 * @property int|null $backticket 包含该订单的发票退票次数
 * @property int|null $isrecall 是否撤回过申请 0 否 1是
 * @property int|null $helpid 协助人
 * @property int|null $grouporderid 团体订单的标记，未定为null，0为不是团体订单，其他数字则为所属的groupid
 * @property int|null $frompersonal 是否是加入时带入的个人信用金订单, null未定, 0 否 1是
 * @property string|null $first_scan_time 首次订单扫码时间，默认为空--林奔-20230608
 * @property int|null $risk_retest_exceed 复测超期风险项：0无，1复测1天内超期，2复测2天内超期，3复测7天内超期--林奔-20231018
 * @property string $user_order_express 用户寄样快递，crm订单详情手动添加
 * @property int $ticket_service_is_connect 客服回访，是否接通，0未处理，1已接通，2未接通；templateid:5,以最新客服沟通记录数据更新
 * @property int $ticket_service_is_satisfaction 客服回访：结果是否满意，0未处理，1满意，2不满意，3其他；templateid:19/23,以最新客服沟通记录数据更新
 * @property int $ticket_service_follow_status 工作台跟进状态：0待处理，1已处理，2仅做记录
 * @property string|null $ticket_service_follow_remark 工作台客服回访跟进备注
 * @property int $supplier_rating_stimulate_status 供应商评价激励订单：0非激励，1未评价，2已评价
 * @property int $repeat_supplier_rating_stimulate_status 复测子单激励评价：0非激励，1未评价，2已评价
 * @property int $risk_cost_anomaly 订单成本异常风险项：0否，1是
 * @property string|null $risk_cost_anomaly_text 成本异常风险项文本数组对象
 * @property int $cost_anomaly_dispose_status 成本异常处理状态：0待处理，1修改成本，2无需处理，3暂不处理
 * @property string|null $cost_anomaly_dispose_remark 成本异常处理-无需处理原因备注
 * @property string|null $last_push_evaluate_notification_time 最新一次推送订单待评价微信通知的时间
 * @property int $supplier_feedback_status 供应商反馈状态 1待处理 2已处理
 * @property int $course_order_complete_method 课程订单完成方式  0:系统自动确认（用户支付即确认） 1:  人工手动确认（后台操作后确认）李振磊-2024-0226
 * @property int $sample_storage_mode 样品保存方式：0常温，>0 sci_sampling_save_way->id
 * @property int $group_pay_pass_time 团体支付审核通过时间
 * @property int $is_common_sample 是否多订单同样品，默认0否，1是
 * @property int $is_send_common_sample_notice 是否发送通知，默认0否，1是
 * @property int $is_confirm_sample_order_data 是否确认共样订单结果，默认0无需确认，1待确认，2已确认
 * @property string $update_time 更新时间
 * @property int $sample_processing_requirement_id 样品处理要求id  sci_sample_processing_requirement.id
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereBackticket($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereCostAnomalyDisposeRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereCostAnomalyDisposeStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereCourseOrderCompleteMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereFirstScanTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereFrompersonal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereGroupPayPassTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereGrouporderid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereHelpid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereIsCommonSample($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereIsConfirmSampleOrderData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereIsSendCommonSampleNotice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereIsrecall($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereLastPushEvaluateNotificationTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereOid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereOsn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend wherePromotionpid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereProvidermerchantremark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereRepeatSupplierRatingStimulateStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereReverseOsn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereRiskCostAnomaly($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereRiskCostAnomalyText($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereRiskRetestExceed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereSampleProcessingRequirementId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereSampleStorageMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereSupplierFeedbackStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereSupplierRatingStimulateStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereTicketServiceFollowRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereTicketServiceFollowStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereTicketServiceIsConnect($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereTicketServiceIsSatisfaction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereUpdateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExtend whereUserOrderExpress($value)
 * @mixin \Eloquent
 */
class OrderExtend extends Model
{
    protected $table = "order_extend";
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $guarded = [];
}
