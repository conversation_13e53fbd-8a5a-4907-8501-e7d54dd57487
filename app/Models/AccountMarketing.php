<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\MarketingTask
 *
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing query()
 * @mixin \Eloquent
 * @property int $markeid ID
 * @property int|null $accountid accountid
 * @property string|null $name 用户姓名
 * @property string|null $mobile 联系注册时手机号码
 * @property int|null $times 注册时间
 * @property int|null $acctype 1高校学生/2高校教职工/3国有研究机构员工/4认证企业员工/5企业员工、个人
 * @property int|null $provinceid 省份ID
 * @property int|null $city 城市id
 * @property int|null $area 区县id
 * @property int|null $univscode 学校id
 * @property string|null $univname 学校名称
 * @property int|null $depcode 学院ID
 * @property string|null $college 学院
 * @property string|null $companyname 公司名称
 * @property string|null $address 详细地址
 * @property int|null $graduation 1不确定/2未毕业/3已毕业
 * @property string|null $daoshi 导师姓名
 * @property int|null $recharge 0未充值/1已充值/2有意向充值
 * @property int|null $source (用户注册来源)0-待确认/1-小木虫等网络平台/2-微信公众号推文/3-微信朋友圈图片/4-朋友线下推荐/5-我们的推广人员推荐/6-其他
 * @property int|null $demand (是否有需求)0-待确认/1-有需求，在未来30天内/2-有需求，不知道什么时候/3-没需求，只是随便看看/4-其他
 * @property int|null $core (是否课题组核心人物)0-待确认/1-是/2-否
 * @property int|null $potential (是否潜力用户)0待确认/1是/2不是
 * @property int|null $emphasisuser (是不是重点0不是/1是)
 * @property string|null $demandin (用户需求信息)
 * @property string|null $maindemand (主要需求有哪些)
 * @property int|null $weixin 是否加微信 1是/0否
 * @property string|null $gam 添加人
 * @property string|null $remarks 其他备注
 * @property int|null $phoneid 电话记录ID
 * @property int|null $atstatus (目前状态)0-待确认/1-愿意推荐普通用户/2-愿意推荐普通用户及7类用户/3-还未信任，所以不愿意推荐
 * @property int|null $currentstatus (当前状态)0-待确认/1-已发展团体充值/2-已发展按单提成/3-暂时不愿意深度合作
 * @property int|null $gift (是否已赠送礼品)0-未赠送/1-已赠送
 * @property int|null $visit 拜访记录ID
 * @property int|null $status 类型（1新注册用户/2待回访用户/3重点用户列表/4所有大师兄，老师类用户）
 * @property int|null $lastime 最后一条需要电话的Task记录生成时间(用于排序)
 * @property string|null $lastwhy 最后一条需要电话的Task的原因
 * @property int|null $laastcalltime 最后联系时间（**********）
 * @property int|null $neednotify 1需要产生电话任务/0不需要产生电话任务
 * @property int|null $adtime 记录的系统生成时间
 * @property int|null $isvoid 0正常/1作废
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereAccountid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereAcctype($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereAdtime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereArea($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereAtstatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereCollege($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereCompanyname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereCore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereCurrentstatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereDaoshi($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereDemand($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereDemandin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereDepcode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereEmphasisuser($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereGam($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereGift($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereGraduation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereLaastcalltime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereLastime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereLastwhy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereMaindemand($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereMarkeid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereNeednotify($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing wherePhoneid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing wherePotential($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereProvinceid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereRecharge($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereRemarks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereTimes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereUnivname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereUnivscode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereVisit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountMarketing whereWeixin($value)
 */
class AccountMarketing extends Model {

    protected $table = 'account_marketing';
}