<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $emailid
 * @property int $accountid
 * @property string|null $email
 * @property int|null $isvoid
 * @property int|null $isdefault
 * @property string $addtime
 *
 * @method static \Illuminate\Database\Eloquent\Builder|AccountEmail newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccountEmail newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccountEmail query()
 * @method static \Illuminate\Database\Eloquent\Builder|AccountEmail whereEmailid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountEmail whereAccountid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountEmail whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountEmail whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountEmail whereIsdefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountEmail whereAddtime($value)
 *
 */
class AccountEmail extends Model
{
    protected $table = "account_email";
    protected $primaryKey = 'emailid';
    public $timestamps = false;
}
