<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class PrepaymentAmount extends Model
{
    protected $table = "prepayment_amount";
    protected $primaryKey = 'id';
    public $timestamps = false;

    const RECHARGE_TYPE_CASH = 0;
    const RECHARGE_TYPE_GIVE = 1;

    public static function store($prepaymentAmountInsert): int
    {
        return DB::table('prepayment_amount')->insertGetId($prepaymentAmountInsert);
    }

    public static function getPrepaymentInfo($prepaymentInfo, $cticketid): array
    {
        $res = self::query()
            ->where('cticketid', '=', $cticketid)
            ->where('accountid', '=', $prepaymentInfo['accountid'])
            ->where('groupid', '=', $prepaymentInfo['groupid'])
            ->where('recharge_type', '=', $prepaymentInfo['recharge_type'])
            ->where('providerid', '=', $prepaymentInfo['providerid'])
            ->first();
        if (empty($res)) {
            return [];
        }
        return $res->toArray();
    }

    /**
     * @param $prepaymentId
     * @param $update
     * @return int
     */
    public static function updatePrepaymentByPrepaymentId($prepaymentId, $update): int
    {
        return self::query()
            ->where('id', $prepaymentId)
            ->update($update);
    }

    public static function getNonTicketConsumeData($accountId, $groupId): array
    {
        $res = self::query()
            ->where('accountid', '=', $accountId)
            ->where('groupid', '=', $groupId)
            ->where('cticketid', '=', 0)
            ->where('recharge_type', '=', self::RECHARGE_TYPE_CASH)
            ->first();
        if (empty($res)) {
            return [];
        }
        return $res->toArray();

    }

    public static function getRemainPrepaymentData($accountId, $groupId): array
    {
        return self::query()
            ->from('prepayment_amount as a')
            ->leftJoin('account_couponticket as b', 'a.cticketid', '=','b.cticketid')
            ->select('a.*', 'a.id as prepayment_id', 'a.remain_amount as amount', 'b.providerid')
            ->where('accountid', '=', $accountId)
            ->where('groupid', '=', $groupId)
            ->where('remain_amount', '>', 0)
            ->orderBy('pay_back_time', 'asc')
            ->get()
            ->toArray();
    }

    /**
     * 查询个人或团体剩余余额 - 加锁
     * @param int $accountId
     * @param int $groupId
     * @param int $addTime
     * @return int
     */
    public static function getBalanceAndAddLock(int $accountId, int $groupId, int $addTime) :int
    {
        return self::query()
            ->where('accountid', $accountId)
            ->where('groupid', $groupId)
            ->where('pay_back_time', '<', $addTime)
            ->lockForUpdate()
            ->sum('remain_amount');

    }

    public function getSplitPrepaymentAmount($accountId, $groupId): array
    {
        $data = self::query()
            ->where('accountid', $accountId)
            ->where('groupid', $groupId)
            ->groupBy(['recharge_type'])
            ->select(DB::raw('sum(remain_amount) as amount'), 'recharge_type')
            ->get()
            ->toArray();
        $data = array_column($data, 'amount', 'recharge_type');
        return [
            'cash' => !empty($data['0']) ? $data['0'] : 0,
            'give' => !empty($data['1']) ? $data['1'] : 0,
        ];

    }

    public function getYanSuPrepaymentAmount($accountId, $groupId): array
    {
        $data = self::query()
            ->from('prepayment_amount as a')
            ->leftJoin('account_couponticket as b', 'a.cticketid', '=','b.cticketid')
            ->where('a.accountid', $accountId)
            ->where('a.groupid', $groupId)
            ->where('b.providerid', 16)
            ->groupBy(['a.recharge_type'])
            ->select(DB::raw('sum(sci_a.remain_amount) as amount'), 'a.recharge_type')
            ->get()
            ->toArray();
        $data = array_column($data, 'amount', 'recharge_type');
        return [
            'cash' => !empty($data['0']) ? $data['0'] : 0,
            'give' => !empty($data['1']) ? $data['1'] : 0,
        ];

    }

    public static function getRechargePrepaymentData($couponData): array
    {
        return self::query()
            ->where('applyid', '=', $couponData['applyid'])
            ->select('id as prepayment_id', 'remain_amount as amount', 'remain_amount', 'consume_amount', 'recharge_type', 'cticketid')
            ->get()
            ->toArray();
    }

    /**
     * 取消预存，对应需要取消的预存票id
     * @param $applyid
     * @param $ticketId
     * @param $rechargeType
     * @return array
     */
    public static function getNeedCancelPrepaymentData($applyid, $ticketId, $rechargeType): array
    {
        return self::query()
            ->where('applyid', $applyid)
            ->when($rechargeType == self::RECHARGE_TYPE_CASH, function ($query) use ($ticketId) {
                $query->where('cticketid', $ticketId);
            })
            ->where('recharge_type', $rechargeType)
            ->get()
            ->toArray();

    }

    /**
     * 预存取消回款时，票余额清零
     * @param $prepaymentId
     * @return int
     */
    public static function recyclePrepaymentAmount($prepaymentId): int
    {
        $update = [
            'consume_amount' => DB::raw('recharge_amount'),
            'remain_amount' => 0,
        ];
        return self::query()
            ->whereIn('id', $prepaymentId)
            ->update($update);
    }

}
