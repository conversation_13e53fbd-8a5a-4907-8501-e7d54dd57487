<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2024/9/10
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\TypicalCaseReviewLog
 *
 * @property int $id id
 * @property int|null $case_id 案例ID
 * @property int|null $review_passed 是否通过
 * @property int $reviewer crm_accountid
 * @property string|null $remark 审核备注
 * @property string $create_time 创建时间
 * @property string $update_time 更新时间
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseReviewLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseReviewLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseReviewLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseReviewLog whereCaseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseReviewLog whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseReviewLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseReviewLog whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseReviewLog whereReviewPassed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseReviewLog whereReviewer($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseReviewLog whereUpdateTime($value)
 * @mixin \Eloquent
 */
class TypicalCaseReviewLog extends  Model
{
    protected $table = "typical_case_review_log";
    protected $primaryKey = "id";
    public $timestamps = false;

}
