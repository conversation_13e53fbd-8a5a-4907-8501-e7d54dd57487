<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Utils\LogHelper;
use App\Utils\YuntongxunSms\SDK\YuntongxunSDK;
use Yanqu\YanquPhplib\MessageCenter\SendAliSMSClient;

class ShopOrder extends Model
{
    protected $table = "shop_order";
    protected $primaryKey = 'orderid';
    public $timestamps = false;

    /**
     * 校验重复提交
     * @param $orderid
     * @return array
     */
    public static function repeatSubmit($accountid,$goodid,$puynums){
        $count  =   DB::table("shop_order")
            ->where("accountid",$accountid)
            ->where("goodid",$goodid)
            ->where("puynums",$puynums)
            ->whereBetween('postime',[time()-2,time()])
            ->count();
        if($count > 0){
            return false;
        }
        return true;
    }

    /**
     * 自动发放电子卡预警机制
     * @param $accountid
     * @return bool
     */
    public static function checkUserCanAutoSendEcard($accountid){
        $amount_30day = DB::table("shop_order")
            ->leftjoin('shop_good','shop_good.goodid','=','shop_order.goodid')
            ->whereBetween('shop_order.postime',[strtotime("-30 day"),time()])
            ->where('shop_order.isvoid',1)
            ->whereIn('shop_order.status',[3,4])
            ->where('shop_good.similar',1)
            ->where('shop_order.accountid',$accountid)
            ->sum('shop_good.price');
        $count_30day = DB::table("shop_order")
            ->leftjoin('shop_good','shop_good.goodid','=','shop_order.goodid')
            ->whereBetween('shop_order.postime',[strtotime("-30 day"),time()])
            ->where('shop_order.isvoid',1)
            ->whereIn('shop_order.status',[3,4])
            ->where('shop_good.similar',1)
            ->where('shop_order.accountid',$accountid)
            ->count();
        if ($amount_30day >= 2000 || $count_30day >= 5) {
            return false;
        }
        return true;
    }

    /**
     * 校验库存并锁定数据
     * @param $orderid
     */
    public static function checkStock($orderid=0,$goodid,$puynums){
        if($orderid > 0){
            $orderinfo  =   DB::table("shop_order")->where("orderid",$orderid)->select("goodid","puynums")->first();
            $goodid =   $orderinfo->goodid;
            $puynums =   $orderinfo->puynums;
        }
        $shop_good_info =   self::getShopGood($goodid);
        $is_amount = DB::table('shop_ecard_type')->where('id',$shop_good_info->ecardtype)->value('is_amount');
        $builder = DB::table("shop_ecard")
            ->where("type",$shop_good_info->ecardtype);
        if($is_amount == 1) { //面额
            $builder->where("amount",$shop_good_info->ecardamount);
        }else { //规格
            $builder->where("specification",$shop_good_info->ecardamount);
        }
        $ids = $builder->where("status",1)
            ->where("isvoid",0)->limit($puynums)
            ->pluck("id");
        $ids    =   json_decode(json_encode($ids),true);
        if(count($ids) !== (int)$puynums){
            return false;
        }
        $res = DB::table("shop_ecard")->whereIn("id",$ids)->where("status","1")->update(["status"=>"3"]);
        if($res) {
            return $ids;
        }else {
            return false;
        }
    }

    /**
     * 开始发放购物卡
     * @param $orderid
     * @param $orderinfo
     */
    public static function actionGrant($orderid,$ids){
        if(empty($ids)){
            return false;
        }
        $orderinfo  =   DB::table("shop_order")->where("orderid",$orderid)->first();
        if($orderinfo->status == 2){
            $goodinfo   =   self::getShopGood($orderinfo->goodid);
            $ecard  =   DB::table("shop_ecard")->whereIn("id",$ids)->select("cardnumber","cardcode")->get();
            DB::table("shop_ecard")->whereIn("id",$ids)
                ->update(["status"=>"2","sendtime"=>time(),"sendtype"=>"1","accountid"=>$orderinfo->accountid,"orderno"=>$orderinfo->orderno]);
            $order_upate    =   [];
            $order_upate['status'] = 4;
            $order_upate['paytime'] = time();
            $cardnumbers    =   "";
            $couriernumber  =   "";
            foreach ($ecard as $vo){
                $cardnumbers    =   $cardnumbers.$vo->cardnumber.";";
                $couriernumber = $couriernumber.$vo->cardnumber . '+' . $vo->cardcode." | ";
            }
            $order_upate['couriernumber']   =   $couriernumber;
            DB::table("shop_order")->where('orderid', $orderinfo->orderid)->update($order_upate);
            $cardnumbers    =   rtrim($cardnumbers,';');
            $note = $goodinfo->name . "系统自动发放成功,卡号:" . $cardnumbers;
            DB::table("shoporder_log")
                ->insert(["accountid"=>$orderinfo->accountid,"orderid"=>$orderinfo->orderid,"goodid"=>$orderinfo->goodid,"postime"=>time(),"note"=>$note]);
        }
        return true;
    }

    /**
     * 获取购物卡信息
     * @param $goodid
     */
    public static function getShopGood($goodid){
        return DB::table("shop_good")->where("goodid",$goodid)->first();
    }

    /**
     * 发送短信
     * @param $orderid
     * @return bool
     */
    public static function sendSms($orderid){
        $orderinfo  =   DB::table("shop_order")->where("orderid",$orderid)->where("status","4")->where("isvoid","1")->first();
        if(empty($orderinfo)){
            return false;
        }
        $goodinfo   =   self::getShopGood($orderinfo->goodid);
        $mobile  =  $orderinfo->mobile;
        if(empty($mobile)){
            $mobile =   DB::table("account")->where("accountid",$orderinfo->accountid)->value("loginame");
        }

        $smsAliSmsClient = new SendAliSMSClient();
        $smsAliSmsClient->setBiz("shopOrder.sendNotifySms");
        $smsAliSmsClient->setExt([
            "phone"       => $mobile,
            "goodinfoName"   => $goodinfo->name,
            "orderid" => $orderid
        ]);
        $smsAliSmsClient->send();
        return true;
    }

    /**
     * 解除购物卡锁定
     * @param $ids
     */
    public static function cancelLock($ids){
       return DB::table("shop_ecard")->whereIn("id",$ids)->update(["status"=>"1"]);
    }
}
