<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


/**
 * 
 *
 * @property int $suppliernoticeid 自增主键
 * @property string $title 发布标题
 * @property string|null $content 发布内容
 * @property int $saccountid 操作人crmid
 * @property string $notice_provider_merchant_ids 通知的非全选范围内的供应商的id
 * @property int $postime 发布时间
 * @property int $isvoid 是否作废(1作废/0正常)
 * @property string $created_at
 * @property string $updated_at
 * @property int $is_top 是否置顶 1置顶2不置顶
 * @property int $effectived_at 生效时间
 * @property int $lose_at 失效时间
 * @property string|null $annexes 附件
 * @property string|null $notice_provider_merchant_json 存储前端传入的通知供应商的json数据
 * @property int $notice_all_choose_type 全选类型 0为未全选 1为全选 2为自营全选 3为第三方全选 4为仅单选
 * @property int|null $important 是否是重要通知，1是重要，0是不重要
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice query()
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereAnnexes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereEffectivedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereImportant($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereIsTop($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereLoseAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereNoticeAllChooseType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereNoticeProviderMerchantIds($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereNoticeProviderMerchantJson($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice wherePostime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereSaccountid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereSuppliernoticeid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SupplierNotice whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SupplierNotice extends Model
{
    protected $table = "supplier_notice";
    protected $primaryKey = 'suppliernoticeid';
    public $timestamps = false;

    protected $guarded = [];
}
