<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2024/9/10
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


/**
 * App\Models\TypicalCaseTestProcessAndResult
 *
 * @property int $id id
 * @property int|null $case_id 案例ID
 * @property string|null $pretreated 前处理
 * @property string|null $step_or_params 测试参数
 * @property string|null $data_handle 数据处理
 * @property string|null $other_instructions 其他测试说明
 * @property string|null $result_data 结果数据
 * @property string|null $reason_and_solution 原因和解决方法
 * @property string|null $attention_content 实验注意事项
 * @property string $create_time 创建时间
 * @property string $update_time 更新时间
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestProcessAndResult newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestProcessAndResult newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestProcessAndResult query()
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestProcessAndResult whereCaseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestProcessAndResult whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestProcessAndResult whereDataHandle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestProcessAndResult whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestProcessAndResult whereOtherInstructions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestProcessAndResult wherePretreated($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestProcessAndResult whereReasonAndSolution($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestProcessAndResult whereResultData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestProcessAndResult whereStepOrParams($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TypicalCaseTestProcessAndResult whereUpdateTime($value)
 * @mixin \Eloquent
 */
class TypicalCaseTestProcessAndResult extends Model
{
    protected $table = "typical_case_test_process_and_result";
    protected $primaryKey = "id";
    public $timestamps = false;
}
