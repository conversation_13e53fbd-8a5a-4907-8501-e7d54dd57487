<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2024/9/10
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\OrderTestProcessAndResult
 *
 * @property int $id id
 * @property int|null $process_id 过程ID
 * @property string|null $pretreated 前处理
 * @property string|null $step_or_params 测试参数
 * @property string|null $data_handle 数据处理
 * @property string|null $other_instructions 其他测试说明
 * @property string $create_time 创建时间
 * @property string $update_time 更新时间
 * @method static \Illuminate\Database\Eloquent\Builder|OrderTestProcessAndResult newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderTestProcessAndResult newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderTestProcessAndResult query()
 * @mixin \Eloquent
 * @property string $step_template_ids 案例关联多个步骤模板记录，逗号分隔
 */
class OrderTestProcessAndResult extends Model
{
    protected $table = "order_test_process_and_result";
    protected $primaryKey = "id";
    public $timestamps = false;
}
