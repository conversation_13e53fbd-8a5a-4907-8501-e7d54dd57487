<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\NanometerProductSkuRelation
 *
 * @property int $relation_id 关系id
 * @property int $product_id 产品编号id
 * @property int $sku_id 产品id
 * @property int $isvoid 是否删除
 * @property string $created_at 创建时间
 * @property string $updated_at 最后修改时间
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerProductSkuRelation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerProductSkuRelation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerProductSkuRelation query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerProductSkuRelation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerProductSkuRelation whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerProductSkuRelation whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerProductSkuRelation whereRelationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerProductSkuRelation whereSkuId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerProductSkuRelation whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class NanometerProductSkuRelation extends Model
{
    protected $table = "nanometer_product_sku_relation";
    protected $primaryKey = 'relation_id';
    public $timestamps = false;
}
