<?php


namespace App\Models;


use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\TicketServiceOid
 *
 * @property int $relationid
 * @property int|null $ticketserviceid 客服任务的id
 * @property int|null $oid 关联的订单id
 * @property string|null $osn 关联的订单号
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TicketServiceOid newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TicketServiceOid newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TicketServiceOid query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TicketServiceOid whereOid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TicketServiceOid whereOsn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TicketServiceOid whereRelationid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TicketServiceOid whereTicketserviceid($value)
 * @mixin \Eloquent
 */
class TicketServiceOid extends Model
{

    protected $table = "ticket_service_oid";
    protected $primaryKey = 'relationid';
    public $timestamps = false;
}