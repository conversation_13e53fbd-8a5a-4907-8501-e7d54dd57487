<?php


namespace App\Models;


use Illuminate\Support\Facades\DB;

class LabAssetsInformationModel
{
    /**
     * 获取仪器信息+审核信息
     * @param $assetIds
     * @param string[] $select
     * @return array
     */
    public function batchGetLabAssetInfoWithAudit($assetIds, $select = ["*"])
    {
        return DB::table('lab_assets_information as lab_assets_information')
            ->leftJoin('lab_assets_audit', function ($join) {
                $join->on('lab_assets_audit.lab_assets_id', '=', 'lab_assets_information.lab_assets_id')
                    ->where('lab_assets_audit.is_new', 1);
            })
            ->whereIn('lab_assets_information.lab_assets_id', $assetIds)
            ->select($select)
            ->get()
            ->toArray();
    }

    public function getInfoByAssetId($id,$select = ["*"])
    {
        return DB::table('lab_assets_information')->where('lab_assets_id', $id) ->select($select)->first();
    }
}