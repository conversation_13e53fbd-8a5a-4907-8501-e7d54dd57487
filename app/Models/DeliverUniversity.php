<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\DeliverUniversity
 *
 * @property int $duniversityid 自增主键
 * @property int|null $deliverid 取样员id
 * @property int|null $universitycode 取样高校的code
 * @property int|null $campuid 取样的分校区(0表示取的无分校的高校/非0表示是取的是分校)
 * @property string|null $samplingtime 每周的取样时间
 * @property string|null $dayclosetime 当天取样截至日期
 * @property string|null $sampremark 取样备注
 * @property int|null $status 100正常上门取样/99关闭上门取样
 * @property int|null $isvoid 作废标志(1作废/0正常)
 * @property string|null $supportdays 支持的取样日期
 * @property string|null $notsupportdays 不支持的取样日期
 * @property string|null $deliveridv2 支持多个取样员
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity whereCampuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity whereDayclosetime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity whereDeliverid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity whereDeliveridv2($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity whereDuniversityid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity whereNotsupportdays($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity whereSamplingtime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity whereSampremark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity whereSupportdays($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\DeliverUniversity whereUniversitycode($value)
 * @mixin \Eloquent
 */
class DeliverUniversity extends Model {

    protected $table = 'deliver_university';

    protected $primaryKey = 'duniversityid';

    public $timestamps = false;
}