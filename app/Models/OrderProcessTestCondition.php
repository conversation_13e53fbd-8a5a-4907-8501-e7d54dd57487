<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2024/9/10
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


/**
 * App\Models\OrderProcessTestCondition
 *
 * @property int $id id
 * @property int|null $process_id 案例ID
 * @property string|null $conditions 测试详情
 * @property string $create_time 创建时间
 * @property string $update_time 更新时间
 * @method static \Illuminate\Database\Eloquent\Builder|OrderProcessTestCondition newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderProcessTestCondition newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderProcessTestCondition query()
 * @mixin \Eloquent
 */
class OrderProcessTestCondition extends Model
{
    protected $table = "order_process_test_condition";
    protected $primaryKey = "id";
    public $timestamps = false;
}
