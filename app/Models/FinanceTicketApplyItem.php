<?php


namespace App\Models;


use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $itemid 自增主键
 * @property int|null $applyid sci_finance_ticketapply.applyid
 * @property int|null $orderid 订单id
 * @property string|null $orderno 订单编号
 * @property string|null $productname 产品名称
 * @property string|null $dproductname 测试项目的产品名称
 * @property string|null $dproductunit 单位
 * @property string|null $dproductnums 数量
 * @property int|null $accountid 帐号id
 * @property string|null $accountname 用户姓名
 * @property string|null $accountmobile 用户手机号码
 * @property string|null $accountcompanyname 学校名称/单位名称
 * @property string|null $accountdaoshi 导师
 * @property string|null $confirmamount 最终开票金额
 * @property string|null $confirmamount1 最终开票金额(备份)
 * @property string|null $offlinepayamount 最终的可用余额支付部分金额
 * @property string|null $weixinpayamount 最终微信支付部分的金额
 * @property string|null $alipayamount 最终支付宝支付部分的金额
 * @property string|null $couponpayamount 最终现金券支付部分的金额
 * @property string|null $grouppayamount 最终团体支付部分的金额
 * @property string|null $creditpayamount 最终信用金支付部分的金额
 * @property int|null $isvoid 是否作废(1作废/0正常)
 * @property int|null $adtime 提交时间
 * @property string|null $currencycode 货币代码
 * @property string|null $dproductspec 规格型号
 * @property string|null $tax_service_code 税收分类编码
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereAccountcompanyname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereAccountdaoshi($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereAccountid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereAccountmobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereAccountname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereAdtime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereAlipayamount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereApplyid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereConfirmamount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereConfirmamount1($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereCouponpayamount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereCreditpayamount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereCurrencycode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereDproductname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereDproductnums($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereDproductspec($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereDproductunit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereGrouppayamount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereItemid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereOfflinepayamount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereOrderid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereOrderno($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereProductname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereTaxServiceCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FinanceTicketApplyItem whereWeixinpayamount($value)
 * @mixin \Eloquent
 */
class FinanceTicketApplyItem extends Model
{
    protected $table = 'finance_ticketapplyitem';
    protected $primaryKey = 'itemid';
    public $timestamps = false;
}
