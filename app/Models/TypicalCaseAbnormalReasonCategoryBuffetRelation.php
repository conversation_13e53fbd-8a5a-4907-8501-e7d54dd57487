<?php

namespace App\Models;

/**
 * \App\Models\TypicalCaseAbnormalReasonCategoryBuffetRelation
 *

 * @mixin \Eloquent
 * @property int $id id
 * @property int $buffet_id 下单商品id
 * @property int $abnormal_reason_category_id 异常原因分类id
 * @property string $create_time 创建时间
 * @property string $update_time 更新时间
 * @property int $isvoid 是否废弃 0正常 1废弃
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TypicalCaseAbnormalReasonCategoryBuffetRelation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TypicalCaseAbnormalReasonCategoryBuffetRelation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TypicalCaseAbnormalReasonCategoryBuffetRelation query()
 */
class TypicalCaseAbnormalReasonCategoryBuffetRelation extends BaseModel
{
    protected $table = 'typical_case_abnormal_reason_category_buffet_relation';
}
