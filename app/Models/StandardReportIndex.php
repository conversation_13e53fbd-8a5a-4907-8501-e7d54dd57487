<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\StandardReportIndex
 *
 * @property int $id
 * @property string $report_no 报告编号
 * @property int $order_account_id
 * @property int $oid 订单id
 * @property string $osn 订单号
 * @property int $provider_product_id 测试项目id
 * @property int $buffet_address_id 办事处id
 * @property int $isvoid 是否已删除
 * @property int $standard_report_id 标准化报告id
 * @property-read \App\Models\BuffetAddress $buffetAddress
 * @property-read \App\Models\AccountInfo $orderAccount
 * @property-read \App\Models\ProviderProduct $providerProduct
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StandardReportIndex newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StandardReportIndex newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StandardReportIndex query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StandardReportIndex whereBuffetAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StandardReportIndex whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StandardReportIndex whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StandardReportIndex whereOid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StandardReportIndex whereOrderAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StandardReportIndex whereOsn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StandardReportIndex whereProviderProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StandardReportIndex whereReportNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\StandardReportIndex whereStandardReportId($value)
 * @mixin \Eloquent
 */
class StandardReportIndex extends Model {

    protected $table = 'standard_report_index';

    public $timestamps = false;


    /**
     * 订单预约人
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function orderAccount() {
        return $this->hasOne(AccountInfo::class,'accountid','order_account_id');
    }

    /**
     * 订单仪器
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function providerProduct() {
        return $this->hasOne(ProviderProduct::class,'providerproductid','provider_product_id');
    }

    /**
     * 订单仪器
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function buffetAddress() {
        return $this->hasOne(BuffetAddress::class,'addressuuid','buffet_address_uuid');
    }
}