<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\NanometerShopcar
 *
 * @property int $carid 纳米商城购物车
 * @property int $accountid 用户id
 * @property int|null $skuid 商品id
 * @property int|null $number 商品数量
 * @property int|null $isvoid 0正常/1作废
 * @property int|null $addtime 添加时间
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerShopcar newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerShopcar newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerShopcar query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerShopcar whereAccountid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerShopcar whereAddtime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerShopcar whereCarid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerShopcar whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerShopcar whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\NanometerShopcar whereSkuid($value)
 * @mixin \Eloquent
 */
class NanometerShopcar extends Model
{
    protected $table = "nanometer_shopcar";
    protected $primaryKey = 'carid';
    public $timestamps = false;
}
