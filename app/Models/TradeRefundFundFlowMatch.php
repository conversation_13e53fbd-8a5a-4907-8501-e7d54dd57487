<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\TradeRefundFundFlowMatch
 *
 * @property int $id id
 * @property int $flow_id sci_trade_refund_fund_flow.id
 * @property int $applyid 1:sci_finance_ticketapply的applyid 2:sci_account_couponapply的applyid
 * 4：sci_order的oid
 * 3.sci_group_invoice的invoiceid
 * @property int $applytype 发票类型 1订单/2预存
 * @property int $ticketid 发票ID 默认0 当applytype=2 取sci_account_couponticket.cticketid
 * @property int $oid sci_order.oid
 * @property float $match_amount 匹配金额
 * @property int $origin_fund_flow_type 退款来源流水类型 1：对公2：online
 * @property string $origin_fund_flow_id 退款来源流水id，受控制于origin_fund_flow_type 1：对公sci_auto_invoice.id，2：online sci_trade_fund_flow.id
 * @property int $isvoid 是否有效 0有效/1无效
 * @property string $create_time 创建时间
 * @property string $update_time 更新时间
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch whereApplyid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch whereApplytype($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch whereFlowId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch whereMatchAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch whereOid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch whereOriginFundFlowId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch whereOriginFundFlowType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch whereTicketid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TradeRefundFundFlowMatch whereUpdateTime($value)
 * @mixin \Eloquent
 */
class TradeRefundFundFlowMatch extends Model
{
    protected $table = 'trade_refund_fund_flow_match';
    protected $primaryKey = 'id';
    public $timestamps = false;
    protected $guarded = [];
}
