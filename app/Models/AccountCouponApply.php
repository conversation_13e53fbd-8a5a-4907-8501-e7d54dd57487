<?php


namespace App\Models;


use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\AccountCouponApply
 *
 * @property int $applyid 自增主键
 * @property string|null $applyno 编号
 * @property string|null $applyway online/offline
 * @property int|null $preaccount 0预存到个人/1预存到团体
 * @property int|null $ticketinfoid 对应的发票信息(作废)
 * @property int|null $accountid 申请预存的会员id
 * @property int|null $addressid 地区ID
 * @property string|null $applyamount 申请金额
 * @property string|null $ticketamount 赠送金额
 * @property string|null $ticketamount_cash 赠送给金额-现金部分
 * @property string|null $consignee (改版更新后不使用)
 * @property string|null $mobile (改版更新后不使用)
 * @property string|null $email (改版更新后不使用)
 * @property int|null $ticketype 0电子/1纸质/2专票/3invoice
 * @property string|null $needreport 1清单/2合同/3报告
 * @property string|null $title (改版更新后不使用)
 * @property string|null $registrationo (改版更新后不使用)
 * @property string|null $vals (改版更新后不使用)
 * @property int|null $province 省(改版更新后不使用)
 * @property int|null $city 市(改版更新后不使用)
 * @property int|null $area 区(改版更新后不使用)
 * @property string|null $address 地址(改版更新后不使用)
 * @property string|null $submitnote 用户提交留言/预存备注
 * @property string|null $ticketjson JSON格式提交申请的数据
 * @property string|null $financenote 财务备注
 * @property int|null $paytime 支付时间
 * @property string|null $payno 第三方支付流水
 * @property string|null $payway alipay/weixin/chinapay
 * @property int|null $paystatus 1未支付/100已支付
 * @property int|null $saccountid crm负责人
 * @property int|null $status 新(0用户初步申请/1待申请/2已申请/3已开票)旧(1新申请/2已支付/99已退回或无效/100已开票)
 * @property int|null $payment 0未还款/1已还款
 * @property int|null $ticketstatus 开票状态(0待开/1已开)(改版更新后不使用)
 * @property string|null $ticketnote 开票备注(改版更新后不使用)
 * @property int|null $adtime 提交时间
 * @property int|null $postime 提交申请时间
 * @property int|null $crmsubaccountid crm提交人的accountid 第一次提交时记录改为每次提交更改
 * @property string|null $receivemobile 接收短信手机号
 * @property int|null $isvoid 0正常/1作废
 * @property int|null $autostatus 1自动锁住，2自动失败
 * @property int $is_need_detail 是否需要明细 0 否 1是
 * @property int|null $givetype 返利方式 1购物卡+实验服/2测试费+实验服/3：积分
 * @property int|null $numlabcoat 实验服数量
 * @property int|null $sendcoat 实验服寄送状态 1已寄出/0未寄出
 * @property string|null $real_user 代预存真实用户
 * @property string|null $real_mobile 代预存联系方式
 * @property string|null $invoiceamount invoice的金额
 * @property string|null $currencycode 货币代码
 * @property string|null $exchangerate 汇率
 * @property int|null $invalid_time 经费有效期
 * @property int|null $coupon_type 预存项目类别 0测试费 1材料费用
 * @property string $next_follow_up_time 下次跟进时间
 * @property int $next_follow_up_account 下次跟进人
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply query()
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereAccountid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereAddressid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereAdtime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereApplyamount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereApplyid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereApplyno($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereApplyway($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereArea($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereAutostatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereConsignee($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereCouponType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereCrmsubaccountid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereCurrencycode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereExchangerate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereFinancenote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereGivetype($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereInvalidTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereInvoiceamount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereIsNeedDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereNeedreport($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereNextFollowUpAccount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereNextFollowUpTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereNumlabcoat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply wherePayment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply wherePayno($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply wherePaystatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply wherePaytime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply wherePayway($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply wherePostime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply wherePreaccount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereProvince($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereRealMobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereRealUser($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereReceivemobile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereRegistrationo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereSaccountid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereSendcoat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereSubmitnote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereTicketamount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereTicketamountCash($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereTicketinfoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereTicketjson($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereTicketnote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereTicketstatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereTicketype($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponApply whereVals($value)
 * @mixin \Eloquent
 * @property int $groupid 充值的团体id sci_group.groupid
 * @property int $without_achievement 不计入绩效 0为计入 1为不计入
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountCouponApply whereGroupid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AccountCouponApply whereWithoutAchievement($value)
 */
class AccountCouponApply extends Model
{
    protected $table = 'account_couponapply';
    protected $primaryKey = 'applyid';
    public $timestamps = false;
}
