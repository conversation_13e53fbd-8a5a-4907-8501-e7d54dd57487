<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\GroupRepaymentCouponItem
 *
 * @property int $id
 * @property int $coupon_id 优惠券id sci_coupon id
 * @property int $group_id 团体id sci_group id
 * @property int $is_receive 是否领取 0未领取 1已领取
 * @property int $coupon_item_id sci_coupon_item 表id
 * @property int $account_id sci_account 领取用户id
 * @property string $expire_time 优惠券过期时间
 * @property string $create_time
 * @property string $update_time
 * @property int $isvoid
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem whereAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem whereCouponId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem whereCouponItemId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem whereExpireTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem whereGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem whereIsReceive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem whereUpdateTime($value)
 * @mixin \Eloquent
 * @property int $apply_id 对应预存id
 * @property int $type 团体预存券类型 1为预存活动赠送的类型
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem whereApplyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\GroupRepaymentCouponItem whereType($value)
 */
class GroupRepaymentCouponItem extends Model
{
    public $table = 'group_prepayment_coupon_item';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $guarded = [];
}
