<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2024 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/5/6 18:14
 */
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\SubOrderProviderProductRelation
 *
 * @property int $id 关联id
 * @property int $buffetid 下单仪器id，sci_buffet@buffetid
 * @property int $providerproductid 子订单选择的测试项目id，sci_provider_product@providerproductid
 * @property string|null $provider_notifier_config 子单供应商通知人设置
 * @property int $isvoid 是否废弃，0-正常，1-已废弃
 * @property string $create_time 创建时间
 * @property string $update_time 更新时间
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\SubOrderProviderProductRelation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\SubOrderProviderProductRelation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\SubOrderProviderProductRelation query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\SubOrderProviderProductRelation whereBuffetid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\SubOrderProviderProductRelation whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\SubOrderProviderProductRelation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\SubOrderProviderProductRelation whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\SubOrderProviderProductRelation whereProviderNotifierConfig($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\SubOrderProviderProductRelation whereProviderproductid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\SubOrderProviderProductRelation whereUpdateTime($value)
 * @mixin \Eloquent
 */
class SubOrderProviderProductRelation extends Model
{
    protected $table = 'suborder_provider_product_relation';

    public $timestamps  = false;
}