<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2025 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/1/22 13:53
 */
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\OrderReportNotifyConfig
 *
 * @property int $id 配置项id
 * @property int $categoryid 业务线id，sci_buffet_category@categoryid
 * @property int $type 订单类型，1-主子订单
 * @property int $notifier 通知人，sci_crm_account@accountid
 * @property int $notify_to_pickup 是否需要通知到订单对接人
 * @property int $isvoid 是否废弃，0-正常，1-已废弃
 * @property string $create_time 创建时间
 * @property string $update_time 更新时间
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderReportNotifyConfig newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderReportNotifyConfig newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderReportNotifyConfig query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderReportNotifyConfig whereCategoryid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderReportNotifyConfig whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderReportNotifyConfig whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderReportNotifyConfig whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderReportNotifyConfig whereNotifier($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderReportNotifyConfig whereNotifyToPickup($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderReportNotifyConfig whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderReportNotifyConfig whereUpdateTime($value)
 * @mixin \Eloquent
 */
class OrderReportNotifyConfig extends Model
{
    protected $table = 'order_report_notify_config';

    protected $primaryKey = 'id';

    public $timestamps = false;
}