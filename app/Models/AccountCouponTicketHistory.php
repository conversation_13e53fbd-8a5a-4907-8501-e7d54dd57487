<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
/**
 * App\Models\AccountCouponTicketHistory
 *
 * @property int $ctickethistoryid 自增主键
 * @property int|null $cticketid sci_account_couponticket.cticketid
 * @property string|null $postnode 备注
 * @property int|null $postime 提交时间
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponTicketHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponTicketHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponTicketHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponTicketHistory whereCtickethistoryid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponTicketHistory whereCticketid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponTicketHistory wherePostime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccountCouponTicketHistory wherePostnode($value)
 */
class AccountCouponTicketHistory extends Model
{
    protected $table = 'account_coupontickethistory';
    protected $primaryKey = 'ctickethistoryid';
    protected $guarded = [];
}
