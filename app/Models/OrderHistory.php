<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\OrderHistory
 *
 * @property int $historyid 自增主键
 * @property int|null $orderid 订单ID
 * @property int|null $historytype 0用户产生/1CRM或者VIP端产生/2admin产生
 * @property int|null $accountid 操作用户帐号
 * @property int|null $saccountid 操作crm/vip的帐号accountid
 * @property int|null $adminid 操作的adminid
 * @property int|null $providermerchantid 供应商id
 * @property int|null $memberid1 供应商团队成员id
 * @property int|null $memberid 供应商团队成员id
 * @property string|null $showlevel 7777(四个占位分别为：用户/VIP或者CRM/ADMIN端的值/供应商端),0表示不显示,7表示显示
 * @property string|null $historydesc
 * @property int|null $operate_status 操作成功失败状态:1成功，0失败-林奔-********
 * @property int|null $adtime 添加时间
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory whereAccountid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory whereAdminid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory whereAdtime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory whereHistorydesc($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory whereHistoryid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory whereHistorytype($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory whereMemberid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory whereMemberid1($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory whereOperateStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory whereOrderid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory whereProvidermerchantid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory whereSaccountid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\OrderHistory whereShowlevel($value)
 * @mixin \Eloquent
 */
class OrderHistory extends Model{
    protected $table            = "order_history";
    protected $primaryKey       = "historyid";
    public $timestamps          = false;
    protected $guarded = [];
}
