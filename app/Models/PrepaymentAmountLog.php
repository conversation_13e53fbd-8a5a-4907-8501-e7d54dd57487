<?php

namespace App\Models;

use App\Services\Prepayment\PrepaymentV2Service;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class PrepaymentAmountLog extends Model
{
    protected $table = "prepayment_amount_log";
    protected $primaryKey = 'id';
    public $timestamps = false;

    /**
     * 个人预存
     */
    const DATA_TYPE_ACCOUNT = 1;
    /**
     * 团体充值
     */
    const DATA_TYPE_GROUP_RECHARGE = 2;
    /**
     * 团体消费
     */
    const DATA_TYPE_GROUP_SPEED = 3;

    /**
     * 消费
     */
    const IS_CONSUME = 1;
    /**
     * 非消费
     */
    const IS_CONSUME_FALSE = 0;

    /**
     * 作废
     */
    const IS_VOID = 1;
    /**
     * 非作废
     */
    const IS_VOID_FALSE = 0;
    /**
     * 取消
     */
    const IS_CANCEL = 1;
    /**
     * 非取消
     */
    const IS_CANCEL_FALSE = 0;

    public static function insertAll($prepaymentAmountInsertLog): bool
    {
        return DB::table('prepayment_amount_log')->insert($prepaymentAmountInsertLog);
    }

    /**
     * 查询互转-转出 对应发票转出金额
     *
     * @param $couponData
     * @return array
     */
    public static function getTransferOutData($couponData): array
    {
        return self::query()
            ->select('c.applyid', 'c.cticketid', 'c.recharge_type', 'c.pay_back_time','b.amount')
            ->from('prepayment_amount_log as b')
            ->leftJoin('prepayment_amount as c', 'c.id', '=', 'b.prepayment_id')
            ->where('b.data_type', '=', $couponData['data_type'])
            ->where('b.auto_id', '=', $couponData['source_id'])
            ->where('b.isvoid', '=', self::IS_VOID_FALSE)
            ->where('b.is_cancel', '=', self::IS_CANCEL_FALSE)
            ->get()
            ->toArray();
    }

    /**
     * @param $couponData
     * @return array
     */
    public static function getConsumeDataByOrderId($couponData): array
    {
        // 根据个人、团体查询
        if ($couponData['data_type'] == self::DATA_TYPE_ACCOUNT) {
            $dataType = self::DATA_TYPE_ACCOUNT;
            $autoIds = AccountCoupon::getConsumeDataByOrderId($couponData['targetorderid']);
        } else {
            $dataType = self::DATA_TYPE_GROUP_SPEED;
            $autoIds = GroupSpend::getConsumeDataByOrderId($couponData['targetorderid']);
        }
        return self::query()
            ->from('prepayment_amount_log as a')
//            ->join('account_coupon as b', 'a.auto_id', '=','b.couponid')
            ->join('prepayment_amount as c', 'a.prepayment_id', '=','c.id')
            ->selectRaw('(sci_a.amount - sci_a.refund_amount) amount')
            ->addSelect('a.id', 'c.id as prepayment_id', 'c.consume_amount', 'c.remain_amount', 'c.recharge_type')
            ->whereIn('a.auto_id', $autoIds)
            ->where('a.data_type', '=', $dataType)
            ->whereColumn('a.amount', '>', 'refund_amount')
            ->where('a.isvoid', '=', self::IS_VOID_FALSE)
            ->where('a.is_cancel', '=', self::IS_CANCEL_FALSE)
            ->where('a.is_consume', '=', self::IS_CONSUME)
            ->get()
            ->toArray();
    }

    public static function getAccountConsumeAmountByOrderId($orderId): float
    {
        return self::query()
            ->from('account_coupon as a')
            ->join('prepayment_amount_log as b', function ($join) {
                $join->on('b.auto_id', '=','a.couponid')
                    ->where('b.isvoid', '=', self::IS_VOID_FALSE)
                    ->where('b.is_cancel', '=', self::IS_CANCEL_FALSE)
                    ->where('b.is_consume', '=', self::IS_CONSUME);
            })
            ->where('a.targetorderid', '=', $orderId)
            ->where('a.amount', '<', 0)
            ->sum('b.amount');
    }

    public static function getConsumeDataByAccountId($couponData, $hasRefundPrepaymentId): array
    {
        $query = self::query()
            ->from('prepayment_amount_log as a')
            ->join('account_coupon as b', 'a.couponid', '=','b.couponid')
            ->join('prepayment_amount as c', 'a.prepayment_id', '=','c.id')
            ->selectRaw('sum(sci_a.amount) amount')
            ->addSelect('c.id as prepayment_id', 'c.consume_amount', 'c.remain_amount', 'c.recharge_type')
            ->where('a.accountid', '=', $couponData['accountid'])
            ->where('a.groupid', '=', $couponData['groupid']);
        if (!empty($hasRefundPrepaymentId)) {
            $query = $query->whereNotIn('a.prepayment_id', $hasRefundPrepaymentId);
        }

        return $query->where('a.isvoid', '=', self::IS_VOID_FALSE)
            ->where('a.is_cancel', '=', self::IS_CANCEL_FALSE)
            ->where('b.amount', '<', 0)
            ->whereNull('b.targetorderid')
            ->groupBy(['c.id', 'c.consume_amount', 'c.remain_amount', 'c.recharge_type'])
            ->get()
            ->toArray();
    }

    public static function refundAmountById($id, $amount): int
    {
        return self::query()
            ->where('id', $id)
            ->increment('refund_amount', $amount);
    }

    /**
     * 查询该记录是否已同步、并加锁
     * @param $autoId
     * @param $dataType
     * @return bool
     */
    public static function isSyncAndAddLock($autoId, $dataType): bool
    {
        return self::query()
            ->where('auto_id', $autoId)
            ->where('data_type', $dataType)
            ->where('isvoid', self::IS_VOID_FALSE)
            ->where('is_cancel', self::IS_CANCEL_FALSE)
            ->lockForUpdate()
            ->exists();
    }

    /**
     * 查询消费记录是否已被取消
     * @param $spendId
     * @return array
     */
    public static function getCancelledData($spendId): array
    {
        return self::query()
            ->where('auto_id', $spendId)
            ->where('data_type', self::DATA_TYPE_GROUP_SPEED)
            ->where('isvoid', self::IS_VOID_FALSE)
            ->where('is_cancel', self::IS_CANCEL_FALSE)
            ->lockForUpdate()
            ->get()
            ->toArray();
    }

    /**
     * 取消团体消费时，作废已同步的消费记录
     * @param $id
     * @return int
     */
    public static function cancelPrepayment($id): int
    {
        return self::query()
            ->where('id', $id)
            ->update([
                'isvoid' => self::IS_VOID,
            ]);
    }

    /**
     * 预存取消回款时，标记取消已同步的消费记录
     * @param $ids
     * @return int
     */
    public static function recyclePrepaymentLog($ids): int
    {
        return self::query()
            ->whereIn('id', $ids)
            ->update([
                'is_cancel' => self::IS_CANCEL,
            ]);
    }

    /**
     * @param $prepaymentIds
     * @return array
     */
    public static function getConsumePrepaymentLogByPrepaymentId($prepaymentIds): array
    {
        return self::query()
            ->select('id', 'prepayment_id', 'auto_id', 'data_type', 'is_consume', 'amount')
            ->whereIn('prepayment_id', $prepaymentIds)
            ->where('isvoid', self::IS_VOID_FALSE)
            ->where('is_cancel', self::IS_CANCEL_FALSE)
            ->orderBy('id', 'desc')
            ->get()
            ->toArray();
    }

    public static function getRechargePrepayment($prepaymentIds): array
    {
        return self::query()
            ->select('prepayment_id', 'auto_id', 'data_type', 'amount')
            ->whereIn('prepayment_id', $prepaymentIds)
            ->where('isvoid', self::IS_VOID_FALSE)
            ->where('is_cancel', self::IS_CANCEL_FALSE)
            ->where('is_consume', self::IS_CONSUME_FALSE)
            ->groupBy(['prepayment_id'])
            ->get()
            ->toArray();
    }

    public static function hasSpitPrepaymentAmount($autoId, $dataType): array
    {
        return self::query()
            ->where('auto_id', $autoId)
            ->where('data_type', $dataType)
            ->where('isvoid', self::IS_VOID_FALSE)
            ->where('is_cancel', self::IS_CANCEL_FALSE)
            ->selectRaw('sum(amount) amount')
            ->addSelect('prepayment_id')
            ->groupBy(['prepayment_id'])
            ->get()
            ->toArray();
    }


    /**
     * 查询个人互转-转出 对应发票转出金额
     * 只溯源使用 - 后面弃用
     * @param $couponData
     * @return array
     */
    public static function getTransferOutDataFromGroup($couponData): array
    {
        $targetAccount = $couponData['accountid'] ?: $couponData['groupid'];

        $sql = 'SELECT c.applyid, c.cticketid, c.recharge_type, c.pay_back_time, b.amount
                    FROM (
                    SELECT	rechargeid FROM sci_group_recharge 
                    WHERE remarks = "'.$couponData['remark'].'"
                        and targetaccount = '.$targetAccount.'
                        and is_match_transfer = 0
                        and postime <= '.$couponData['postime'].'
                        order by rechargeid desc
                        limit 1
                    ) a 
                    JOIN `sci_prepayment_amount_log` AS `b` ON `a`.`rechargeid` = `b`.`auto_id`
                    JOIN `sci_prepayment_amount` AS `c` ON `c`.`id` = `b`.`prepayment_id` 
                    WHERE
                        `b`.`data_type` = ' . PrepaymentAmountLog::DATA_TYPE_GROUP_RECHARGE .'
                        and b.`isvoid` = ' . PrepaymentAmountLog::IS_VOID_FALSE . ' 
                        and b.is_cancel = ' . PrepaymentAmountLog::IS_CANCEL_FALSE;
        $res = DB::select($sql);
        return json_decode(json_encode($res), true);

    }


    /**
     * 查询个人互转-转出 对应发票转出金额
     * 只溯源使用 - 后面弃用
     * 昊哥的互转
     * @param $couponData
     * @return array
     */
    public static function getTransferOutDataFromAccount($couponData): array
    {
        $targetAccount = $couponData['accountid'] ?: $couponData['groupid'];

        if (preg_match('/^'.PrepaymentV2Service::TRANSFER_FROM_FINANCE.'/', $couponData['remark'])) {
            $couponId = AccountCoupon::query()
                ->where('remark', '=', $couponData['remark'])
                ->where('targetaccount', '=', $targetAccount)
                ->where('postime', '<', $couponData['postime'])
                ->orderBy('postime', 'desc')
                ->value('couponid');
        } else {
            $couponId = AccountCoupon::query()
                ->where('remark', '=', $couponData['remark'])
                ->whereBetween('postime', [$couponData['postime'] - 2, $couponData['postime'] + 2])
                ->orderBy('postime', 'desc')
                ->value('couponid');
        }
        if (empty($couponId)) {
            return [];
        }

        return self::query()
            ->select('c.applyid', 'c.cticketid', 'c.recharge_type', 'c.pay_back_time', 'b.amount')
            ->from('prepayment_amount_log as b')
            ->leftJoin('prepayment_amount as c', 'c.id', '=', 'b.prepayment_id')
            ->where('b.data_type', '=', PrepaymentAmountLog::DATA_TYPE_ACCOUNT)
            ->where('b.auto_id', '=', $couponId)
            ->where('b.isvoid', '=', PrepaymentAmountLog::IS_VOID_FALSE)
            ->where('b.is_cancel', '=', PrepaymentAmountLog::IS_CANCEL_FALSE)
            ->get()
            ->toArray();
    }

}
