<?php

namespace App\Console\Commands;

use App\Jobs\OrderReportMigrateTask;
use App\Models\OrderReport;
use App\Models\OrderReportMoveLog;
use App\Services\Order\OrderReportService;
use Illuminate\Console\Command;
use Exception;
use Illuminate\Support\Collection;
use Yanqu\YanquPhplib\YqLog\YqLog;

class OrderReportMigrateDaily extends Command {

    protected $orderReportService;

    /**
     * 比startReportId小的文件不会被迁移
     *
     * @var string
     */
    protected $signature = 'order-report:migrate {startReportId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '每日文件迁移';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct (OrderReportService $reportService) {
        $this->orderReportService = $reportService;
        parent::__construct();
    }

    /**
     * 设定一个阈值,开始处理
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle () {
        $startReportId = $this->argument('startReportId');
        if (empty($startReportId)) {
            throw new Exception('需要设定一个初始id');
        }
        //取id最大的
        $reportMoveLog = OrderReportMoveLog::query()
            ->where('order_report_id', '>=', $startReportId)
            ->orderBy('order_report_id', 'desc')
            ->select(['order_report_id'])->first();

        $lastMigrateId = $startReportId;
        if (!empty($reportMoveLog)) {
            $lastMigrateId = $reportMoveLog->order_report_id;
        }

        //处理
        OrderReport::query()->where('reportid', '>', $lastMigrateId)
            ->select(['reportid'])
            ->orderBy('reportid')
            ->chunk(10, function (Collection $reports) {
                $reports->each(function (OrderReport $report) {
                    OrderReportMigrateTask::dispatch($report->reportid);
                });
            });
    }
}
