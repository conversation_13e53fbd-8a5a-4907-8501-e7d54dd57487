<?php

namespace App\Repositories\Coupon;
use App\Constants\CouponConstant;
use App\Models\Coupon;
use App\Models\CouponItem;
use Illuminate\Support\Facades\DB;

class CouponRepository
{
    /**
     * 获取优惠券基本信息
     * @param $couponId
     * @return Coupon|array|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|\Illuminate\Database\Query\Builder|object
     */
    public function getCouponInfo($couponId)
    {
        return Coupon::query()
            ->where('couponid', $couponId)
            ->select(['pids','categorys','brandids','skuids','excludepids','excludecategorys',
                'excludebrandids','excludeskuids'])
            ->first() ?? [];
    }

    /**
     * 根据用户已领取优惠券id获取优惠券信息
     * @param $couponItemId
     * @return array|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|\Illuminate\Database\Query\Builder|object
     */
    public function getCouponItem($couponItemId)
    {
        return CouponItem::query()
            ->leftJoin('coupon', 'coupon.couponid', 'coupon_item.couponid')
            ->where('coupon_item.isvoid', 0)
            ->where('coupon_item.itemid', '=', $couponItemId)
            ->select('coupon_item.couponid','coupon_item.couponno','coupon.title','coupon_item.status',
                'coupon_item.group_id',
                'coupon_item.adtime','coupon_item.effecttime','coupon_item.limitime','coupon.condition','coupon.jname',
                'coupon.conval','coupon.coupontype','coupon.maxcoupon','coupon.use_range','coupon.use_channel',
                'coupon.pids','coupon.categorys','coupon.brandids','coupon.excludepids','coupon.excludecategorys',
                'coupon.excludebrandids','coupon.cityids', 'coupon_item.itemid','coupon.excludeskuids','coupon.skuids')
            ->first() ?? [];
    }
}