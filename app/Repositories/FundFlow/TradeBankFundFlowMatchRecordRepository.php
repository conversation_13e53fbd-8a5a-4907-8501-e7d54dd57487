<?php

namespace App\Repositories\FundFlow;

use App\Models\TradeBankFundFlowMatch;
use Illuminate\Support\Facades\DB;

class TradeBankFundFlowMatchRecordRepository
{
    public static function getHasMatchBandFundFlowAmount($orderIds): array
    {
        return TradeBankFundFlowMatch::query()
            ->from('trade_bank_fund_flow_match as a')
            ->leftJoin('auto_invoice_match as b', 'a.match_id', '=', 'b.id')
            ->leftJoin('finance_ticketapply as c', 'a.applyid', '=', 'c.applyid')
            ->where(function ($query) {
                $query->where('a.applyid', 0)
                    ->orWhere('c.receivestatus', 1);
            })
            ->whereIn('a.oid', $orderIds)
            ->where('a.isvoid', 0)
            ->where('b.isvoid', 0)
            ->where('b.match_type', 3)
            ->where('b.ischeck', 1)
            ->select([DB::raw('sum(match_money) as amount'), 'oid'])
            ->groupBy('oid')
            ->get()
            ->keyBy('oid')
            ->toArray();
    }

    /**
     * Get ticket split amount data
     * @param array $ticketIds
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getTicketSplitAmount(array $ticketIds)
    {
        return TradeBankFundFlowMatch::query()
            ->from('trade_bank_fund_flow_match as a')
            ->join('auto_invoice_match as b', 'a.match_id', '=', 'b.id')
            ->whereIn('a.applyid', $ticketIds)
            ->where('a.applytype', 1)
            ->where('a.isvoid', 0)
            ->where('b.isvoid', 0)
            ->where('b.ischeck', 1)
            ->where('b.status', 1)
            ->where('b.match_type', 3)
            ->groupBy('a.applyid', 'a.serialno')
            ->select('a.applyid', 'a.serialno', DB::raw('SUM(sci_a.match_money) as amount'))
            ->get();
    }

}
