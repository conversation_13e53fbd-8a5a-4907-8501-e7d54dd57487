<?php

namespace App\Repositories\FundFlow;

use App\Models\AutoInvoiceMatch;
use Illuminate\Support\Facades\DB;

class AutoInvoiceRepository
{
    // 查询对公流水，人工填写，匹配订单、发票流水
    public function getOrderAndTicketMatchByHandFundFlowData($matchApplyIds): array
    {
        return AutoInvoiceMatch::query()
            ->from('auto_invoice_match as a')
            ->leftJoin('auto_invoice as b', 'b.serialno', '=', 'a.serialno')
            ->whereIn('a.applyid', $matchApplyIds)
            ->whereIn('a.applytype', [1, 4])
            ->where('a.isvoid', 0)
            ->where('a.ischeck', 1)
            ->where('a.status', 1)
            ->where('a.match_type', 3)
            ->select(['b.amount', 'b.unmatchamount', 'a.serialno', 'a.applyid', 'a.applytype', DB::raw('sci_a.amount as matchAmount'), 'a.create_time'])
            ->get()
            ->toArray();
    }

    public function getMultiMatchSerialNo($serialNos): array
    {
        return AutoInvoiceMatch::query()
            ->whereIn('serialno', $serialNos)
            ->whereIn('applytype', [1, 4])
            ->where('isvoid', 0)
            ->where('ischeck', 1)
            ->where('match_type', 3)
            ->groupBy('serialno')
            ->having(DB::raw('count(distinct applyid)>1'))
            ->pluck('serialno')
            ->toArray();
    }

    public function getMultiMatchApplyId($applyIds, $applyType=1): array
    {
        return AutoInvoiceMatch::query()
            ->whereIn('applyid', $applyIds)
            ->where('applytype', $applyType)
            ->where('isvoid', 0)
            ->where('ischeck', 1)
            ->where('match_type', 3)
            ->groupBy('applyid')
            ->having(DB::raw('count(distinct serialno)>1'))
            ->pluck('applyid')
            ->toArray();
    }

    /**
     * Get ticket match amount data
     * @param array $ticketIds
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getTicketMatchAmount(array $ticketIds)
    {
        return AutoInvoiceMatch::query()
            ->from('auto_invoice_match as a')
            ->whereIn('a.applyid', $ticketIds)
            ->where('a.applytype', 1)
            ->where('a.isvoid', 0)
            ->where('a.ischeck', 1)
            ->where('a.status', 1)
            ->whereIn('a.match_type', [1, 2])
            ->select('a.applyid', 'a.serialno', 'a.amount')
            ->get();
    }

    /**
     * Get manual match data for tickets
     * @param array $ticketIds
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getManualMatchData(array $ticketIds)
    {
        return AutoInvoiceMatch::query()
            ->from('auto_invoice_match as a')
            ->join('auto_invoice as b', 'a.serialno', '=', 'b.serialno')
            ->whereIn('a.applyid', $ticketIds)
            ->where('a.applytype', 1)
            ->where('a.isvoid', 0)
            ->where('a.ischeck', 1)
            ->where('a.status', 1)
            ->where('a.match_type', 3)
            ->select('a.applyid', 'a.serialno', 'a.amount as match_money', 'b.amount', 'b.unmatchamount', 'a.create_time')
            ->get();
    }

    /**
     * Get multi-match serial numbers
     * @param array $serialNos
     * @return array
     */
    public function getMultiMatchSerialNos(array $serialNos)
    {
        return AutoInvoiceMatch::query()
            ->from('auto_invoice_match as a')
            ->whereIn('a.serialno', $serialNos)
            ->where('a.applytype', 1)
            ->where('a.isvoid', 0)
            ->where('a.ischeck', 1)
            ->where('a.status', 1)
            ->where('a.match_type', 3)
            ->groupBy('a.serialno')
            ->havingRaw('COUNT(DISTINCT sci_a.applyid) > 1')
            ->pluck('a.serialno')
            ->toArray();
    }

    /**
     * Get multi-match apply IDs
     * @param array $ticketIds
     * @return array
     */
    public function getMultiMatchApplyIds(array $ticketIds)
    {
        return AutoInvoiceMatch::query()
            ->from('auto_invoice_match as a')
            ->whereIn('a.applyid', $ticketIds)
            ->where('a.applytype', 1)
            ->where('a.isvoid', 0)
            ->where('a.ischeck', 1)
            ->where('a.status', 1)
            ->where('a.match_type', 3)
            ->groupBy('a.applyid')
            ->havingRaw('COUNT(DISTINCT sci_a.serialno) > 1')
            ->pluck('a.applyid')
            ->toArray();
    }
}
