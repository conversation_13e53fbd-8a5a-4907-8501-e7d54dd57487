<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2025 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/2/20 15:12
 */
namespace App\Repositories\Order;

use App\Models\OrderVas;
use Yanqu\YanquPhplib\Openapi\OrderVas\Constants\OrderVasPropConstants;
use Yanqu\YanquPhplib\Openapi\OrderVas\Constants\OrderVasStatusConstants;

class OrderVasRepository
{
    /**
     * 获取已确认增值服务的订单
     * @param $oids
     * @param $vasUuids
     * @return array
     */
    public function getEnabledVasOids($oids, $vasUuids)
    {
        return OrderVas::query()
            ->whereIn('oid', $oids)
            ->whereIn('vasuuid', $vasUuids)
            ->whereIn('status', [
                OrderVasStatusConstants::STATUS_USER_CONFIRMED,
                OrderVasStatusConstants::STATUS_MANAGER_CONFIRMED
            ])
            ->pluck('oid')
            ->all();
    }

    /**
     * 获取订单已生效的增值服务
     * @param $oids
     * @return array
     */
    public function getEnabledOrdersVas($oids)
    {
        $enabledOrdersVas = [];

        $enabledOrderVasData = OrderVas::query()
            ->whereIn('oid', $oids)
            ->whereIn('status', [
                OrderVasStatusConstants::STATUS_USER_CONFIRMED,
                OrderVasStatusConstants::STATUS_MANAGER_CONFIRMED
            ])
            ->select('oid', 'vasuuid', 'real_vasuuid', 'status')
            ->get()
            ->groupBy('oid');
        foreach ($enabledOrderVasData as $oid => $vasData) {
            $enabledOrdersVas[$oid] = [];
            foreach ($vasData as $vasItem) {
                // 极致测服务标识特殊处理
                $enabledOrdersVas[$oid][] = [
                    'uuid' => $vasItem->real_vasuuid == OrderVasPropConstants::SERVICE_JIZHICE_UUID
                        ? $vasItem->real_vasuuid
                        : $vasItem->vasuuid,
                    'status' => $vasItem->status
                ];
            }
        }

        return $enabledOrdersVas;
    }
}