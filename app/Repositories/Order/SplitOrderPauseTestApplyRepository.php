<?php

namespace App\Repositories\Order;

use App\Models\SplitOrderPauseTestApply;

class SplitOrderPauseTestApplyRepository
{
    public function getById($id)
    {
        return SplitOrderPauseTestApply::find($id);
    }

    public function audit(SplitOrderPauseTestApply $apply, $auditStatus, $auditRemark)
    {
        $apply->audit_status = $auditStatus;
        $apply->audit_remark = $auditRemark;
        $apply->save();
    }

    public function resume(SplitOrderPauseTestApply $apply, $resumeType)
    {
        $apply->is_resumed = 1;
        $apply->resume_type = $resumeType;
        return $apply->save();
    }
}