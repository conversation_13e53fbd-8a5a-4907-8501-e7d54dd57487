<?php

namespace App\Repositories\Order;

use App\Models\OrderInfo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class OrderRepository
{
    /**
     * 获取订单信息
     * @param $oid
     * @param array $fields
     * @return OrderInfo|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|\Illuminate\Database\Query\Builder|\LaravelIdea\Helper\App\Models\_IH_OrderInfo_QB|object|null
     */
    public function getOrderInfoByFields($oid, array $fields)
    {
        return OrderInfo::query()
            ->where('oid', $oid)
            ->select($fields)
            ->first();
    }

    /**
     * @param array<int> $orderIds
     * @return Collection|OrderInfo[]
     */
    public function getOrdersCreditAmount(array $orderIds): Collection
    {
        return OrderInfo::query()->where('isvoid', 0)
            ->whereIn('oid', $orderIds)
            ->select(['oid','creditpayamount','osn'])
            ->get();
    }


    public function batchClearTicketId(array $orderIds)
    {
        return OrderInfo::query()->whereIn('oid', $orderIds)->update(['ticketid' => 0]);
    }

    /**
     * 获取当前订单的子单 id
     * @param $oid
     * @return array{
     *     main_oid: int,
     *     main_osn: string,
     *     child_oids: array
     * }
     */
    public function getSubOrders($oid)
    {
        $mainOrderId = OrderInfo::query()->where('oid', $oid)->value('mainorderid');
        $realOid = $mainOrderId > 0 ? $mainOrderId : $oid;

        $orders = OrderInfo::query()
            ->where('oid', $realOid)
            ->orWhere('mainorderid', $realOid)
            ->pluck('osn', 'oid')
            ->all();
        $childOrders = [];
        foreach ($orders as $oid => $osn) {
            if ($oid == $realOid) {
                continue;
            }
            $childOrders[$oid] = $osn;
        }

        return [
            'main_oid' => $realOid,
            'main_osn' => $orders[$realOid],
            'child_orders' => $childOrders
        ];
    }

    /**
     * 获取订单业务线id
     * @param $oid
     * @return mixed
     */
    public function getOrderCategoryId($oid)
    {
        return OrderInfo::query()->where('oid', $oid)->value('categoryid');
    }

    public function getAccountIdsByOrderIds($orderIds)
    {
        return OrderInfo::query()
            ->whereIn('oid', $orderIds)
            ->get()
            ->keyBy('oid')
            ->toArray();
    }


    public function getOidByOsn($osnArr) : array
    {
        return OrderInfo::query()
            ->whereIn('osn', $osnArr)
            ->select('oid', 'osn')
            ->get()
            ->keyBy('osn')
            ->toArray();
    }


    /**
     * 获取样品宝订单的配置信息
     * @param $oids
     * @return list<int, OrderInfo>
     */
    public function getSampleInsuranceOrdersConfig($oids)
    {
        return OrderInfo::query()
            ->whereIn('oid', $oids)
            ->select(['oid', 'isrecovery', 'orderstate'])
            ->get()
            ->keyBy('oid')
            ->all();
    }

    /**
     * 获取主单id
     * @param $oid
     * @return mixed
     */
    public function getMainOrderId($oid)
    {
        return OrderInfo::query()->where('oid', $oid)->value('mainorderid');
    }

    public function getOrdersByOids($oids)
    {
        return OrderInfo::whereIn('oid', $oids)
            ->select('oid', 'productname', 'categoryid')->get();
    }

    public function getOrderInfoByOid($oid, $columns = ['oid', 'osn'], $with = [])
    {
        return OrderInfo::query()->when(!empty($with), function (Builder $query) use ($with) {
            $query->with($with);
        })
            ->where('oid', $oid)->select($columns)->first();
    }

    public function getOriginOsnByOID($oid)
    {
        $orderInfo = OrderInfo::query()->where('oid', $oid)->select(['isrepeatest','osn'])->first();
        if($orderInfo == null) {
            return "";
        }
        if($orderInfo->isrepeatest == 0) {
            return $orderInfo->osn;
        }else{
            return OrderInfo::query()
                ->where('oid', $orderInfo->isrepeatest)
                ->value('osn') ?? '';
        }
    }
}
