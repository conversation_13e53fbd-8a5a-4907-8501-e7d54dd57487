<?php

namespace App\Repositories\Invoice;

use App\Constants\Invoice\OrderInvoiceApplyConstant;
use App\Entities\OrderTicket\FatherOrderTicketRedFlushUpdateDTO;
use App\Models\FinanceTicketApply;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class FinanceTicketRepository
{

    /**
     * @param array $applyIds
     * @return Collection|FinanceTicketApply[]
     */
    public function getTransformedTicketList(array $applyIds): Collection
    {
        return FinanceTicketApply::query()->whereIn('applyid', $applyIds)
            ->where('isvoid', 0)
            ->select(
                [
                    'applyid', 'applyamount', 'ticketstatus', 'ticketinfoid', 'orderids', 'parentickid',
                    'invoicedm', 'invoiceno', 'applyaccount', 'invoicetime', 'receivestatus', 'providerid','dzppdfurl',
                    'fpqqlsh'
                ]
            )
            ->with(['autoInvoiceMatch:id,applyid',
                    'financeTicketInfo:ticketinfoid,ticketype,seller_bank_id,title'])
            ->get();
    }

    /**
     * @param int $parentApplyId
     * @return int
     */
    public function getChildrenTicketCount(int $parentApplyId): int
    {
        return FinanceTicketApply::query()->where('parentickid', $parentApplyId)
            ->where('isvoid', 0)->count();
    }


    public function redFlushFatherTicket(int $applyId, FatherOrderTicketRedFlushUpdateDTO $updateDTO)
    {
        return FinanceTicketApply::query()->where('applyid', $applyId)
            ->update([
                'isvoid' => $updateDTO->isvoid,
                'ticketstatus' => $updateDTO->ticketstatus,
            ]);
    }


    public function batchOfflineRedFlushTicket(array $applyIds)
    {
        return FinanceTicketApply::query()->whereIn('applyid', $applyIds)
            ->update([
                "ticketstatus" => OrderInvoiceApplyConstant::TICKET_STATUS_WAIT,
                'redstroke_time' => Carbon::now()->toDateTimeString(),
                "invoiceno" => "",
                "noneedopen" => 0,
                "verify_fail" => 0,
                'ticket_day' => 0,
                'check_code' => '',
                'total_amount' => 0.00,
                'invoicetime' => 0,
                'fpqqlsh' => '',
                'auto_status' => 0,
                'isvoid' => 1
            ]);
    }

    public function deleteChildrenApplyByParentId($parentApplyId)
    {
        return FinanceTicketApply::query()->where('parentickid', $parentApplyId)->update(
            [
                'isvoid' => 1
            ]
        );
    }

    public function getTicketInfoByApplyIds($applyIds)
    {
        return FinanceTicketApply::query()
            ->whereIn('applyid', $applyIds)
            ->select(['applyid', 'orderids'])
            ->get();
    }

    public function getSubTicketIdByApplyId($ticketIds)
    {
        return FinanceTicketApply::query()
            ->whereIn('parentickid', $ticketIds)
            ->where('isvoid', 0)
            ->where('ticketstatus', OrderInvoiceApplyConstant::TICKET_STATUS_INVOICED)
            ->pluck('applyid')
            ->toArray();
    }
}