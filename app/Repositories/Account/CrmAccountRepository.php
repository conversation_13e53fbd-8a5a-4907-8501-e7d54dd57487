<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2024 (https://www.shiyanjia.com)
 * <AUTHOR> 2024/12/24 18:01
 */
namespace App\Repositories\Account;

use App\Constants\Account\CrmAccountStatusConstants;
use App\Models\CrmAccount;
use App\Models\AccountIndex;

class CrmAccountRepository
{
    /**
     * 获取账户信息
     * @param $accountId
     * @return CrmAccount|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    public function getNormalAccountInfo($accountId, $queryCallback = null)
    {
        $crmAccountQuery = CrmAccount::query()
            ->where('accountid', $accountId)
            ->where('status', CrmAccountStatusConstants::NORMAL)
            ->where('isnormal', 1)
            ->where('isvoid', 0);

        if (!empty($queryCallback) && is_callable($queryCallback)) {
            $queryCallback($crmAccountQuery);
        }

        return $crmAccountQuery->first();
    }

    /**
     * 通过账户编号获取账户信息
     * @param string $accountNo
     * @return CrmAccount|null
     */
    public function getAccountInfoByAccountNo(string $accountNo): ?CrmAccount
    {
        return CrmAccount::query()
            ->where('accountno', $accountNo)
            ->where('isnormal', 1)
            ->where('isvoid', 0)
            ->select(['accountid', 'loginame', 'mobile', 'qiyewxuserid'])
            ->first();
    }

    // 获取用户对应的绩效负责人
    public function getAchievementHead($accountId)
    {
        return AccountIndex::where('accountid', $accountId)->value('achievement_crm_accountid');
    }
}
