<?php

namespace App\Repositories\TypicalCase;

use App\Models\BuffetSampleCategory;

class BuffetSampleCategoryRepository
{
    public function getBySampleCategoryId($SampleCategoryId)
    {
        return BuffetSampleCategory::query()
            ->where('sample_category_id', $SampleCategoryId)
            ->where('isvoid', 0)
            ->get();
    }

    public function getByBuffetId($BuffetId)
    {
        return BuffetSampleCategory::query()
            ->where('buffet_id', $BuffetId)
            ->where('isvoid', 0)
            ->get();
    }

    public function updateCategoryBind(int $categoryId, array $buffetIds, array $removeCategoryIds)
    {
        $this->compareDiffAndUpdate('sample_category_id', $categoryId, 'buffet_id', $buffetIds);
        if ($buffetIds && $removeCategoryIds) {
            BuffetSampleCategory::query()
                ->whereIn('buffet_id', $buffetIds)
                ->whereIn('sample_category_id', $removeCategoryIds)
                ->where('isvoid', 0)
                ->update(['isvoid' => 1]);
        }
    }

    public function updateBuffetBind(int $buffetId, array $categoryIds)
    {
        $this->compareDiffAndUpdate('buffet_id', $buffetId, 'sample_category_id', $categoryIds);
    }

    public function compareDiffAndUpdate(string $entityType, int $entityId, string $bindEntityType, array $bindEntityIds)
    {
        // 去重
        $bindEntityIds = array_unique($bindEntityIds);
        // 对比现在已经关联的，删除去掉的，增加新增的
        $currentBindEntityIds = BuffetSampleCategory::query()
            ->where($entityType, $entityId)
            ->where('isvoid', 0)
            ->pluck($bindEntityType)
            ->toArray();

        $toDelete = array_diff($currentBindEntityIds, $bindEntityIds);
        foreach (array_chunk($toDelete, 50) as $batch) {
            BuffetSampleCategory::query()
                ->where($entityType, $entityId)
                ->whereIn($bindEntityType, $batch)
                ->where('isvoid', 0)
                ->update(['isvoid' => 1]);
        }

        $toAdd = array_diff($bindEntityIds, $currentBindEntityIds);
        foreach (array_chunk($toAdd, 50) as $batch) {
            $addData = [];
            foreach ($batch as $bindEntityId) {
                $addData[] = [
                    $entityType => $entityId,
                    $bindEntityType => $bindEntityId,
                ];
            }
            BuffetSampleCategory::query()->insert($addData);
        }
    }

    public function removeCategoryBind(array $removeCategoryIds)
    {
        BuffetSampleCategory::query()
            ->whereIn('sample_category_id', $removeCategoryIds)
            ->where('isvoid', 0)
            ->update(['isvoid' => 1]);
    }
}