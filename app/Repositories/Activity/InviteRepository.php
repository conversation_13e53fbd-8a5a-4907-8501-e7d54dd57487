<?php

namespace App\Repositories\Activity;

use App\Constants\Constant;
use App\Constants\InviteConstant;
use App\Models\NewInviteActivityUser;
use App\Models\OrderInfo;
use App\Models\Account;
use App\Models\AccountExtract;

class InviteRepository
{
    public function getOrderInfo($orderId)
    {
        return OrderInfo::query()->where("oid", $orderId)->select(
            "accountid", "offlinepayamount", "onlinepayamount", "couponpayamount",
            "grouppayamount", "creditpayamount", "groupcreditpayamount", "orderstate",
            "paytime", "osn", "oid"
        )->first();
    }

    public function userInfo($accountId)
    {
        return Account::query()->where('accountid', $accountId)->select(
            "postime", "istrainee", "inviter", "accountid"
        )->first();

    }

    public function getInviteUserId($userMobile)
    {
        return Account::query()->where("loginame", trim($userMobile))->value('accountid');
    }

    public function getOrderInviteRebateRecord($orderId)
    {
        return AccountExtract::query()->where("oid", $orderId)->first();
    }

    public function insertInviteRebateData($orderInfo, $inviteUserId, $type): bool
    {
        $orderAmount = $orderInfo->offlinepayamount + $orderInfo->onlinepayamount + $orderInfo->couponpayamount +
            $orderInfo->grouppayamount + $orderInfo->creditpayamount + $orderInfo->groupcreditpayamount;
        if ($type == 'old') {
            $orderRebateRation = InviteConstant::OLD_ORDER_REBATE_RATION;
            $source = InviteConstant::INVITE_ACTIVITY_OLD;
        } else {
            $orderRebateRation = InviteConstant::NEW_ORDER_REBATE_RATION;
            $source = InviteConstant::INVITE_ACTIVITY_NEW;
        }
        return AccountExtract::query()->insert([
            "accountid" => $inviteUserId,
            "orderaccountid" => $orderInfo->accountid,
            "oid" => $orderInfo->oid,
            "osn" => $orderInfo->osn,
            "orderprice" => $orderAmount,
            "actualcosprice" => $orderAmount,
            "proportion" => $orderRebateRation,
            "amount" => round($orderAmount * ($orderRebateRation / 100), 2),
            "isuser" => Account::query()->where("accountid",$inviteUserId)->value('istrainee'),
            "postime" => time(),
            "source" => $source
        ]);
    }

    public function getInviteActivityType($accountId)
    {
        return NewInviteActivityUser::query()->where("account_id",$accountId)
            ->where("isvoid",Constant::NOT_VOID)
            ->first();
    }
}
