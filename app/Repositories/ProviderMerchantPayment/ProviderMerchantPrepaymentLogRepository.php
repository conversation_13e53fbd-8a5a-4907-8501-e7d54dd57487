<?php

namespace App\Repositories\ProviderMerchantPayment;

use App\Models\ProviderPrestoreLog;

class ProviderMerchantPrepaymentLogRepository
{
    public function getPrepaymentBalanceMapByProviderMerchantIds($providerMerchantIds)
    {
        return ProviderPrestoreLog::whereIn('providermerchantid', $providerMerchantIds)
            ->selectRaw('providermerchantid, sum(amount) as balance')
            ->groupBy('providermerchantid')
            ->pluck('balance', 'providermerchantid');
    }
}