<?php


namespace App\Services;


use App\Models\Buffet;
use App\Models\ProviderProduct;

class ProviderProductService extends BaseService
{
    /**
     * 根据仪器获取测试项目Ids
     * @param $buffetId
     * @return array
     */
    public function getProviderProductIds($buffetId):array
    {
        $providerProductIds = [];
        if (!empty($buffetId)) {
            $providerProductIds = ProviderProduct::query()
                ->where("buffetid", $buffetId)
                ->pluck('providerproductid')
                ->toArray();
        }
        return $providerProductIds;
    }

    /**
     * 获取仪器信息
     * @param $buffetId
     * @param string[] $field
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    public function getBuffetInfo($buffetId,$field = ['buffetid'])
    {
        return Buffet::query()->where('buffetid',$buffetId)
            ->select($field)
            ->first();
    }

}