<?php
/**
 * 校内员工
 */

namespace App\Services;

use App\Http\Controllers\Constants\UniStaff\UniStaffConstants;
use App\Utils\EnvironmentHelper;
use App\Utils\LogHelper;
use App\Utils\Rsa;
use Illuminate\Support\Facades\DB;
use App\Utils\StatusCode;

class UniStaffService extends BaseService
{
    /**
     * 获取校内员工收益
     * @param $accountid
     */
    public function getStaffIncome($accountid)
    {
        $staffid = $this->getStaffid($accountid);
        if ($staffid > 0) {
            $notPay = 0.00; //未结算金额
            $alreadyPay = 0.00; //已结算金额
            //查询所有的子任务
            $subtaskids = DB::table("uni_staff_subtask")->where("staffid", $staffid)->pluck('subtaskid')->toArray();
            if (!empty($subtaskids)) {
                //查询所有的执行任务
                $executeids = DB::table("uni_staff_execute")->whereIn("subtaskid", $subtaskids)->pluck('executeid')->toArray();
                if (!empty($executeids)) {
                    //查询所有的结算记录
                    $settlement = DB::table("uni_staff_settlement")->whereIn("executeid", $executeids)->select("amount", "status")->get();
                    if ($settlement) {
                        foreach ($settlement as $vo) {
                            if ($vo->status == 1) {
                                $notPay += $vo->amount;
                            } else {
                                $alreadyPay += $vo->amount;
                            }
                        }
                    }
                }
            }
            $this->setData(["staffid" => $staffid, "notPay" => $notPay, "alreadyPay" => $alreadyPay]);
        } else {
            $this->setData(["staffid" => 0, "notPay" => 0.00, "alreadyPay" => 0.00]);
        }
    }

    /**
     * 可领取任务
     * @param $accountid
     */
    public function getApplyTask($accountid, $ip)
    {
        //获取用户的高校id 和城市id
        $univCityid = $this->getUserUnivCityid($accountid, $ip);
        $cityid = $univCityid['cityid'];
        $univsid = $univCityid['univsid'];

        //获取已经接取过的任务id
        //$taskids = $this->alreadyGetTask($accountid);

        //查询字段
        $field = ["taskid", "task_name", "deadline", "person_number", "settle_way", "settle_name", "settle_price"];

        //获取高校命中的任务
        $taskUnivs = [];
        if ($univsid > 0) {
            $taskUnivs = $this->getUnivTask($univsid, $field);
        }
        //获取城市命中的任务
        $taskCitys = [];
        if ($cityid > 0) {
            $taskCitys = $this->getCityTask($cityid, $field);
        }
        //获取无区域限制的任务
        $taskUn = $this->getUnTask($field);

        //数据拼接
        $data = array_merge($taskUnivs, $taskCitys, $taskUn);
        foreach ($data as $key => $vo) {
            $data[$key]['deadline'] = date('Y-m-d', $vo['deadline']);
        }
        $this->setData($data);
    }

    /**
     * 已领取的任务
     * @param $accountid
     */
    public function getEnrollTask($accountid, $status)
    {
        $staffid = $this->getStaffid($accountid);
        if ($staffid > 0) {
            if ($status == 0) {
                $status = [1, 2, 3];
            } else {
                $status = [$status];
            }
            $subtasks = DB::table("uni_staff_subtask as a")
                ->leftJoin("uni_staff_task as b", "a.taskid", "=", "b.taskid")
                ->where("a.staffid", $staffid)
                ->whereIn("a.status", $status)
                ->orderBy("a.create_time", "desc")
                ->select("a.subtaskid", "b.task_name", "a.create_time", "a.status")
                ->get();
            $subtasks = json_decode(json_encode($subtasks), true);
            foreach ($subtasks as $key => $subtask) {
                $subtasks[$key]['create_time'] = date('Y-m-d', $subtask['create_time']);
            }
            $this->setData($subtasks);
            return true;
        } else {
            return $this->serviceError(StatusCode::STAFF_ERROR, "未加入兼职生");
        }
    }

    /**
     * 获取执行任务
     * @param $status //0全部任务 1待执行 2审核中 3驳回中 4已完成
     */
    public function getSubTask($accountid, $status)
    {
        $staffid = $this->getStaffid($accountid);
        if ($staffid == 0) {
            return $this->serviceError(StatusCode::STAFF_ERROR, "未加入兼职生");
        }

        if ($status == 1) {
            //待执行任务
            $waitExecuteTask = $this->getWaitExecuteTask($staffid);
            $this->setData($waitExecuteTask);
        } elseif (in_array($status, [2, 3, 4])) {
            $execute = $this->getReviewTask($staffid);
            switch ($status) {
                case 2://审核中任务
                    $this->setData($execute['task2']);
                    break;
                case 3://驳回任务
                    $this->setData($execute['task3']);
                    break;
                default://已完成任务
                    $this->setData($execute['task4']);
            }
        } else {
            //全部任务
            $this->setData($this->allSubTask($staffid));
        }
        return true;
    }

    /**
     * 全部子任务
     * @param $staffid
     */
    public function allSubTask($staffid)
    {
        //待执行任务
        $waitExecuteTask = $this->getWaitExecuteTask($staffid);
        //已执行待审核
        $execute = $this->getReviewTask($staffid);
        //获取关闭任务
        $close = $this->getCloseSubTask($staffid);

        //数组拼接-获取列表
        $allTaskList = array_merge(
            $waitExecuteTask['list'],
            $execute['task2']['list'],
            $execute['task3']['list'],
            $execute['task4']['list'],
            $close['list']
        );
        //数组拼接-获取count
        $allTaskCount = [
            "waitExecute" => $waitExecuteTask['count'],
            "inExamine" => $execute['task2']['count'],
            "reject" => $execute['task3']['count'],
            "complete" => $execute['task4']['count'],
        ];
        return ["list" => $allTaskList, "count" => $allTaskCount];
    }

    /**
     * 获取已关闭任务
     * @param $staffid
     */
    public function getCloseSubTask($staffid)
    {
        $subtasks = DB::table("uni_staff_subtask")
            ->where("staffid", $staffid)
            ->where("status", 2)
            ->orderBy("create_time", "desc")
            ->get();
        $closeSubTask = [];
        foreach ($subtasks as $subtask) {
            $arr = [];
            $arr['subtaskid'] = $subtask->subtaskid;
            $arr['task_name'] = $this->getTaskName($subtask->taskid);
            $arr['create_time'] = date('Y-m-d', $subtask->create_time);
            $arr['status'] = 5;
            $arr['type'] = DB::table("uni_staff_task")->where("taskid", $subtask->taskid)->value('type');
            array_push($closeSubTask, $arr);
        }
        return ["list" => $closeSubTask, "count" => count($closeSubTask)];
    }

    /**
     * 查询非待执行任务
     */
    public function getReviewTask($staffid)
    {
        $subtaskids = DB::table("uni_staff_subtask")
            ->where("staffid", $staffid)
            ->where("status", 2)
            ->orderBy("create_time", "desc")
            ->pluck('subtaskid')->toArray();
        $task2 = []; //待审核
        $task3 = []; //审核驳回
        $task4 = []; //完成
        if (!empty($subtaskids)) {
            $executes = DB::table("uni_staff_execute")->whereIn("subtaskid", $subtaskids)->get();
            foreach ($executes as $vo) {
                $arr = [];
                $arr['subtaskid'] = $vo->subtaskid;
                $subtaskInof = DB::table("uni_staff_subtask")->where("subtaskid", $vo->subtaskid)->select("taskid", "create_time")->first();
                $arr['task_name'] = $this->getTaskName($subtaskInof->taskid);
                $arr['create_time'] = date('Y-m-d', $subtaskInof->create_time);
                $arr['create_time_stamp'] = $subtaskInof->create_time;
                $arr['type'] = DB::table("uni_staff_task")->where("taskid", $subtaskInof->taskid)->value('type');
                switch ($vo->status) {
                    case 1:
                        $arr['status'] = 2;
                        array_push($task2, $arr);
                        break;
                    case 2:
                        $arr['status'] = 4;
                        array_push($task4, $arr);
                        break;
                    case 3:
                        $arr['status'] = 3;
                        array_push($task3, $arr);
                        break;
                }
            }
        }
        return [
            "task2" => ["list" => $this->arraySort($task2, 'create_time_stamp'), "count" => count($task2)],
            "task3" => ["list" => $this->arraySort($task3, 'create_time_stamp'), "count" => count($task3)],
            "task4" => ["list" => $this->arraySort($task4, 'create_time_stamp'), "count" => count($task4)]
        ];
    }

    public function arraySort($array, $keys, $sort = SORT_DESC)
    {
        $keysValue = [];
        foreach ($array as $k => $v) {
            $keysValue[$k] = $v[$keys];
        }
        array_multisort($keysValue, $sort, $array);
        return $array;
    }

    /**
     * 获取待执行任务
     * @param $staffid
     */
    public function getWaitExecuteTask($staffid)
    {
        $subtasks = DB::table("uni_staff_subtask")
            ->where("staffid", $staffid)
            ->where("status", 2)
            ->orderBy("create_time", "desc")
            ->get();
        $tasks = [];
        if ($subtasks) {
            foreach ($subtasks as $vo) {
                $execute = DB::table("uni_staff_execute")->where("subtaskid", $vo->subtaskid)->first();
                if (!$execute) {
                    $arr = [];
                    $arr['subtaskid'] = $vo->subtaskid;
                    $subtaskInof = DB::table("uni_staff_subtask")->where("subtaskid", $vo->subtaskid)->select("taskid", "create_time")->first();
                    $arr['task_name'] = $this->getTaskName($subtaskInof->taskid);
                    $arr['create_time'] = date('Y-m-d', $subtaskInof->create_time);
                    $arr['status'] = 1;
                    $arr['type'] = DB::table("uni_staff_task")->where("taskid", $subtaskInof->taskid)->value('type');
                    array_push($tasks, $arr);
                }
            }
        }
        return ["list" => $tasks, "count" => count($tasks)];
    }

    /**
     * 获取任务名称
     * @param $taskid
     */
    public function getTaskName($taskid)
    {
        return DB::table("uni_staff_task")->where("taskid", $taskid)->value('task_name');
    }

    /**
     * 获取已经接取过的任务
     * @param $accountid
     * @return array
     */
    public function alreadyGetTask($accountid)
    {
        if ($accountid > 0) {
            $staffid = $this->getStaffid($accountid);
            if ($staffid > 0) {
                $taskids = DB::table("uni_staff_subtask")->where("staffid", $staffid)->pluck("taskid")->toArray();
                if (!empty($taskids)) {
                    return $taskids;
                }
            }
        }
        return [0];
    }

    /**
     * 获无区域限制任务
     */
    public function getUnTask($field)
    {
        $tasks = DB::table("uni_staff_task")->where("scope_type", 1)
            ->where("status", 1)
            ->where("deadline", ">", time())
            ->orderBy("create_time", "desc")
            ->orderBy("deadline", "asc")
            ->orderBy("taskid", "desc")
            ->select($field)
            ->get();
        if ($tasks) {
            return json_decode(json_encode($tasks), true);
        } else {
            return [];
        }
    }

    /**
     * 获取城市命中的任务
     * @param $cityid
     */
    public function getCityTask($cityid, $field)
    {
        $tasks = DB::table("uni_staff_task")->where("scope_type", 2)
            ->whereRaw('FIND_IN_SET(?,scope_citys)', [$cityid])
            ->where("status", 1)
            ->where("deadline", ">", time())
            ->orderBy("create_time", "desc")
            ->orderBy("deadline", "asc")
            ->orderBy("taskid", "desc")
            ->select($field)
            ->get();
        if ($tasks) {
            return json_decode(json_encode($tasks), true);
        } else {
            return [];
        }
    }

    /**
     * 获取学校限制的任务
     * @param $univsid
     */
    public function getUnivTask($univsid, $field)
    {
        $tasks = DB::table("uni_staff_task")->where("scope_type", 3)
            ->whereRaw('FIND_IN_SET(?,scope_univs)', [$univsid])
            ->where("status", 1)
            ->where("deadline", ">", time())
            ->orderBy("create_time", "desc")
            ->orderBy("deadline", "asc")
            ->orderBy("taskid", "desc")
            ->select($field)
            ->get();
        if ($tasks) {
            return json_decode(json_encode($tasks), true);
        } else {
            return [];
        }
    }

    /**
     * 获取用户的高校id和城市id
     */
    public function getUserUnivCityid($accountid, $ip)
    {
        $cityid = 0;
        $univsid = 0;
        if ($accountid > 0) {
            $accInfo = DB::table("account_info")->where("accountid", $accountid)->select("city", "universityid")->first();
            $cardInfo = DB::table("account_card")->where("accountid", $accountid)->select("city", "universityid")->first();
            if ($accInfo->city > 0) {
                $cityid = $accInfo->city;
            } else {
                if ($cardInfo && $cardInfo->city > 0) {
                    $cityid = $accInfo->city;
                }
            }
            $univcode = 0;
            if ($accInfo->universityid > 0) {
                $univcode = $accInfo->universityid;
            } else {
                if ($cardInfo && $cardInfo->universityid > 0) {
                    $univcode = $accInfo->universityid;
                }
            }
            if ($univcode > 0) {
                $univsid = DB::table("univs")->where("univscode", $univcode)->value('univsid');
            }
        }
        //如果城市和学校都为空，根据IP获取城市
        if ($cityid == 0 && $univsid == 0 && !empty($ip)) {
            $RegisterService = new RegisterService();
            $ipInfo = $RegisterService->getAliyunIPCityName($ip);
            if ($ipInfo["city"] != "") {
                $cityinfo = DB::table("regions")->where("layer", 2)->where("name", "like", "%" . $ipInfo["city"] . "%")->first();
                if (!empty($cityinfo)) {
                    $cityid = $cityinfo->regionid;
                }
            }
            if ($cityid == 0 && $ipInfo['province'] != "") {
                $cityinfo = DB::table("regions")->where("layer", 2)->where("name", "like", "%" . $ipInfo['province'] . "%")->first();
                if (!empty($cityinfo)) {
                    $cityid = $cityinfo->regionid;
                }
            }
        }

        return ["cityid" => $cityid, "univsid" => $univsid];
    }

    /**
     * 通过用户id获取兼职生id
     */
    public function getStaffid($accountid)
    {
        $staffid = DB::table("uni_staff")->where("accountid", $accountid)->where("isvoid", 0)->value('staffid');
        if (empty($staffid)) {
            $staffid = 0;
        }
        return $staffid;
    }

    /**
     * 获取结算任务
     */
    public function getSettlementTask($accountid, $status)
    {
        $staffid = $this->getStaffid($accountid);
        if (empty($staffid)) {
            return $this->serviceError(StatusCode::STAFF_ERROR, "未加入兼职生");
        }
        $list = [];
        if ($staffid > 0) {
            $subtaskids = DB::table("uni_staff_subtask")->where("staffid", $staffid)->where("status", 2)
                ->pluck('subtaskid')->toArray();
            if (!empty($subtaskids)) {
                //执行状态筛选
                if ($status == 1) {
                    $selectStatus = [1];
                } elseif ($status == 2) {
                    $selectStatus = [3];
                } else {
                    $selectStatus = [1, 2, 3];
                }
                //查询数据
                $executeList = DB::table("uni_staff_execute")->whereIn("subtaskid", $subtaskids)->whereIn("status", $selectStatus)->get();
                if ($executeList) {
                    foreach ($executeList as $execute) {
                        //基础数据
                        $executeStatus = $this->getExecuteStatus($execute->executeid);
                        $arr = [];
                        $arr['executeid'] = $execute->executeid;
                        $arr['subtaskid'] = $execute->subtaskid;
                        if(empty($execute->execute_name)){
                            $taskid = DB::table("uni_staff_subtask")->where("subtaskid",$execute->subtaskid)->value("taskid");
                            $execute_name = $this->getTaskName($taskid);
                        }else{
                            $execute_name = $execute->execute_name;
                        }
                        $arr['execute_name'] = $execute_name;
                        $arr['reject_cause'] = $execute->reject_cause;
                        $arr['submit_time'] = $execute->submit_time;
                        $arr['submit_time_name'] = date('Y-m-d H:i:s', $execute->submit_time);
                        $arr['status'] = $executeStatus['status'];
                        $arr['execute_status_name'] = $executeStatus['name'];
                        $arr['amount'] = $executeStatus['amount'];
                        //结算状态筛选 $status 0全部 1审核中 2驳回 3待结算 4已结算
                        if ($status == 3) {
                            //待结算筛选
                            if ($executeStatus['status'] == 3) {
                                array_push($list, $arr);
                            }
                        } elseif ($status == 4) {
                            //已结算筛选
                            if ($executeStatus['status'] == 4) {
                                array_push($list, $arr);
                            }
                        } else {
                            //全部
                            array_push($list, $arr);
                        }
                    }
                }
            }
        }
        //各个状态的数量统计
        $subtaskids = isset($subtaskids)?$subtaskids:[];
        $count = $this->settlementCount($subtaskids);
        $this->setData([
            "list"=>$this->arraySort($list, 'submit_time'),
            "count"=>$count,
        ]);
        return true;
    }

    /**
     * 统计结算列表的各个状态的数量
     * @param $accountid
     */
    public function settlementCount($subtaskids)
    {
        $inExamine = 0;
        $reject = 0;
        $waitSettlement = 0;
        $completeSettlement = 0;
        if (!empty($subtaskids)) {
            //审核中 已驳回
            $executeids = [];//结算查询
            $executeList = DB::table("uni_staff_execute")->whereIn("subtaskid", $subtaskids)->select("status", "executeid")->get();
            if (!empty($executeList)) {
                foreach ($executeList as $value) {
                    if ($value->status == 1) {
                        $inExamine++;
                    } elseif ($value->status == 3) {
                        $reject++;
                    } else {
                        array_push($executeids, $value->executeid);
                    }
                }
            }
            //待结算 已结算
            if (!empty($executeids)) {
                $settlementStatus = DB::table("uni_staff_settlement")->whereIn("executeid", $executeids)->select("status")->get();
                foreach ($settlementStatus as $value) {
                    if ($value->status == 1) {
                        $waitSettlement++;
                    } else {
                        $completeSettlement++;
                    }
                }
            }
        }
        return [
            "inExamine" => $inExamine,
            "reject" => $reject,
            "waitSettlement" => $waitSettlement,
            "completeSettlement" => $completeSettlement,
        ];
    }

    /**
     * 获取执行任务的状态
     * @param $executeid
     */
    public function getExecuteStatus($executeid)
    {
        $executeStatus = DB::table("uni_staff_execute")->where("executeid", $executeid)->value('status');
        if ($executeStatus == 1) {
            return ["status" => 1, "name" => "待审核", "amount" => 0.00];
        } elseif ($executeStatus == 3) {
            return ["status" => 2, "name" => "已驳回", "amount" => 0.00];
        } else {
            $settlementInfo = DB::table("uni_staff_settlement")->where("executeid", $executeid)->first();
            if ($settlementInfo) {
                if ($settlementInfo->status == 1) {
                    return ["status" => 3, "name" => "待结算", "amount" => $settlementInfo->amount];
                } else {
                    return ["status" => 4, "name" => "已结算", "amount" => $settlementInfo->amount];
                }
            } else {
                return ["status" => 5, "name" => "异常数据", "amount" => 0.00];
            }
        }
    }

    /**
     * 判断用户是否展示兼职生模块
     */
    public function isShow($accountId)
    {
        $accountInfo = DB::table("account_info")->where("accountid", $accountId)
            ->select("accountype", "universityid", "city")->first();
        $isShow = UniStaffConstants::DISPLAY_FALSE;
        $qrCode = "";
        $whiteData = DB::table("crowdsourcing_whitelist")->where("account_id",$accountId)->first();
        //在白名单
        if ($accountInfo && $whiteData) {
            $isShow = UniStaffConstants::DISPLAY_TRUE;
        }else{
            //查看是否是白名单学校-需要可以主动申请兼职生白名单
            if($accountInfo->universityid > 0){
                $univId = $this->univCodeConvertUnivId($accountInfo->universityid);
                $univWhitelist = DB::table("crowdsourcing_univ_whitelist")->where("univ_id",$univId)
                    ->where("isvoid",UniStaffConstants::NOT_VOID)
                    ->first();
                if($univWhitelist){
                    $isShow = UniStaffConstants::CAN_APPLY;
                    $qrCode = $univWhitelist->qr_code;
                }
            }
        }
        $this->setData(["isShow"=>$isShow,"qrCode"=>$qrCode]);
    }

    /**
     * 学校code转换学校id
     */
    public function univCodeConvertUnivId($univCode){
        return DB::table("univs")->where("univscode",$univCode)->value('univsid');
    }
}
