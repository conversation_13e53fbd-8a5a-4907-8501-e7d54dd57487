<?php


namespace App\Services;


use Illuminate\Support\Facades\DB;

class BusinessAreaService extends BaseService
{
    /**
     * 用户发布默认插入一条售前需求
     * @param $accountId
     * @param $external_userid
     * @param $saccountid
     * @param $categoryId
     * @param $period
     * @param $patterncnt
     * @param $budget
     * @param $detail
     * @return int
     */
    public function addPresaleCommunicationData($accountId,$external_userid,$saccountid,$categoryId,$period,$patterncnt,$budget,$detail)
    {
        $nickName = null;
        if(!empty($external_userid)) {
            $nickName = DB::table('customer_chatgroup')->where('external_userid', $external_userid)
                ->value('nickname');
        }
        //新增一条type为24的售前沟通消息
        return DB::table("presale_communication")->insertGetId([
            "external_userid" => $external_userid,
            "nickname" => $nickName,
            "saccountid" => $saccountid,
            "accountid" => $accountId,
            "type" => 24,
            "content" => "用户发布需求",
            "create_time" => time(),
            "categoryid" => $categoryId,
            "demand_period" => $period,
            "demand_patterncnt" => $patterncnt,
            "demand_budget" => $budget,
            "demand_detail" => $detail,
        ]);
    }

    /**
     * 新增发布需求记录
     * @param $categoryId
     * @param $accountId
     * @param $cityId
     * @param $taskNo
     * @param $title
     * @param $contacter
     * @param $contactel
     * @param $contactemail
     * @param $patterncnt
     * @param $budget
     * @param $period
     * @param $detail
     * @param $saccountid
     * @param $ip
     * @param $ctype
     * @param $external_userid
     * @param $is_first_order
     * @return int
     */
    public function addTaskData($categoryId,$accountId,$cityId,$taskNo,$title,$contacter,$contactel,$contactemail,$patterncnt,
                                $budget,$period,$detail,$saccountid,$ip,$ctype,$external_userid,$is_first_order)
    {
        $data = [
            'categoryid' => $categoryId,
            'accountid' => $accountId,
            'taskno' => $taskNo,
            'title' => $title,
            'contacter' => $contacter,
            'contactel' => $contactel,
            'contactemail' => $contactemail,
            'patterncnt' => $patterncnt,
            'budget' => $budget,
            'period' => $period,
            'detail' => $detail,
            'saccountid' => $saccountid,
            'city' => !empty($cityId) ? $cityId : null,
            'clientip' => $ip,
            'status' => 1,
            'ctype' => $ctype, //1pc，2小程序
            'postime' => time(),
            'external_userid' => $external_userid,
            'is_first_order' => $is_first_order
        ];
        //数据库操作
        return DB::table('task')->insertGetId($data);
    }

    /**
     * 更新任务附件
     * @param $files
     * @param $taskId
     */
    public function updateFileData($files,$taskId)
    {
        if (!empty($files) && is_array(explode(",", $files))) {
            $array_files = explode(",", $files);
            foreach ($array_files as $file) {
                $fileinfo = array();
                $fileinfo["taskid"] = $taskId;
                $fileinfo["path"] = $file;
                $fileinfo["postime"] = time();
                DB::table('task_file')->insert($fileinfo);
            }
        }
    }

    /**
     * 更新邮件推送内容
     * @param $contactemail
     * @param $contactel
     * @param $patterncnt
     * @param $budget
     * @param $period
     * @param $detail
     * @param $taskId
     * @param $files
     */
    public function addJobEmailData($contactemail,$contactel,$patterncnt,$budget,$period,$detail,$taskId,$files)
    {
        if (!empty($contactemail)) {
            $emailsubject = "您的需求提交成功";
            $emailbody = "";
            $emailbody = $emailbody . "<!DOCTYPE html>";
            $emailbody = $emailbody . "<html lang=\"en\">";
            $emailbody = $emailbody . "<head>";
            $emailbody = $emailbody . "<meta charset=\"UTF-8\">";
            $emailbody = $emailbody . "<title>科学指南针</title>";
            $emailbody = $emailbody . "</head>";
            $emailbody = $emailbody . "<body>";
            $emailbody = $emailbody . "<div style=\"width:800px;font-size:14px;padding:20px;\">";
            $emailbody = $emailbody . "尊敬的" . $contactel . "用户，您好！<br />";
            $emailbody = $emailbody . "感谢您选择科学指南针为您的科研保驾护航，让科研更简单是我们不懈的努力和追求。<br />";
            $emailbody = $emailbody . "很高兴您能信任我们，提交了如下实验需求:<br /><br />";
            $emailbody = $emailbody . "样品数量：" . $patterncnt . "<br />";
            $emailbody = $emailbody . "预算范围：" . $budget . "<br />";
            $emailbody = $emailbody . "完成周期：" . $period . "<br />";
            $emailbody = $emailbody . "样品内容：" . $detail . "<br /><br />";
            $emailbody = $emailbody . "我们的项目经理会很快与您联系。<br />";
            $emailbody = $emailbody . "若您等待较长时间没有等到我们经理的联系或者进一步的回复，可以点击下方的按钮进行催促。";
            $emailbody = $emailbody . "</div>";
            $emailbody = $emailbody . "<div style=\"padding:12px;height:80px;line-height:80px;\">";
            $emailbody = $emailbody . "<a href=\"https://www.shiyanjia.com/demand-urge.html?taskid=" . $taskId . "\" style=\"background-color:#001ba0;padding:12px 30px;color:#ffffff;font-size:16px;text-decoration:none;\">催促联系</a>";
            $emailbody = $emailbody . "</div>";
            $emailbody = $emailbody . "</body>";
            $emailbody = $emailbody . "</html>";

            $emailobject = array();
            $emailobject["emails"] = $contactemail;
            $emailobject["subject"] = $emailsubject;
            $emailobject["bodys"] = $emailbody;
            $emailobject["files"] = $files;
            $emailobject["adtime"] = time();
            Db::table('jobemail')->insert($emailobject);
        }
    }

    /**
     * 根据accountId获取企微id
     * @param $accountId
     * @return mixed|null
     */
    public function getExternalUseridByAccountId($accountId)
    {
        $externalUserid = null;
        if(!empty($accountId)) {
            $externalUserid = DB::table('map_researchgroup_member')
                ->where('accountid', $accountId)
                ->value('external_userid');
            if (empty($externalUserid)) {
                $unionId = DB::table('account_bind')->where('accountid', $accountId)->value('unionid');
                if (!empty($unionId)) {
                    $externalUserid = DB::table('customer')->where('unionid', $accountId)->value('external_userid');
                }
            }
        }
        return $externalUserid;
    }

    /**
     * 判断是否首单
     * @param $accountId
     * @return int
     */
    public function checkIsFirstOrder($accountId)
    {
        $is_first_order = 0; //默认未绑定用户非首单
        if(!empty($accountId)) {
            $oid = DB::table('order')->where('accountid',$accountId)->whereNotIn('orderstate',[98,99])->value('oid');
            if($oid > 0) {
                $is_first_order = 0;
            }else {
                $taskId = DB::table('task')->where('accountid',$accountId)->value('taskid');
                if($taskId > 0) {
                    $is_first_order = 0;
                }else {
                    $is_first_order = 1;
                }
            }
        }
        return $is_first_order;
    }

    /**
     * 获取任务编号
     * @return int
     */
    public function getTaskNO()
    {
        //不能直接取值查询去重，会导致内存不足。修改随机编号方法: 每小时编号数，最多9999
        $taskNo = substr(time(),0,6).sprintf("%04d", mt_rand(100,9999));
        $taskCount = DB::table('task')->where("taskno", $taskNo)->count();
        while (!empty($taskCount) && $taskCount > 0) {
            $taskNo = substr(time(),0,6).sprintf("%04d", mt_rand(100,9999));
            $taskCount = DB::table('task')->where("taskno", $taskNo)->count();
        }
        return $taskNo;
    }

}