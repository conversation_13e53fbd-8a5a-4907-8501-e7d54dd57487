<?php

namespace App\Services\ProviderMerchantPayment\Reconciliation;

use App\Constants\ProviderMerchantPayment\MessageConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantPaymentErrorLogConstants;
use App\Entities\ProviderMerchantPayment\ProviderMerchantInvoiceDTO;
use App\Entities\ProviderMerchantPayment\ReconciliationContextMapEntity;
use App\Exceptions\BusinessException;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Models\OrderSettle;
use App\Models\ProviderMerchantInvoiceReconciliationApplication as Reconciliation;
use App\Models\ProviderPrestore;
use App\Repositories\Admin\AdminRepository;
use App\Repositories\CrmAccount\CrmAccountRepository;
use App\Repositories\Ekuaibao\EkuaibaoCompanyRepository;
use App\Repositories\ProviderMerchant\ProviderMerchantRepository;
use App\Repositories\ProviderMerchantPayment\InvoiceReconciliationApplicationRepository as ReconciliationRepository;
use App\Repositories\ProviderMerchantPayment\OrderSettleRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantInvoiceRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantPaymentErrorLogRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantPrepaymentRepository;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoFlowService;
use App\Services\ProviderMerchantPayment\OrderSettle\OrderSettleService;
use App\Services\ProviderMerchantPayment\Prepayment\ProviderMerchantPrepaymentService;
use App\Services\ProviderMerchantPayment\ProviderMerchantPaymentService;
use App\Services\Supplier\ProviderMerchantService;
use Illuminate\Support\Facades\DB;
use Yanqu\YanquPhplib\AliyunHttp\AliyunException;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\EkuaibaoFlowCreatorConstants;
use Yanqu\YanquPhplib\Queue\MNSClient;

class ProviderMerchantInvoiceReconciliationService
{
    private $crmAccountId;
    private $adminAccountId;

    /**
     * 获取核销状态枚举
     * @return string[]
     */
    public function getReconcileStatusEnums($params): array
    {
        $enums = ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_ENUMS;
        if (isset($params['enum_scene']) &&
            $params['enum_scene'] == ProviderMerchantInvoiceReconciliationConstants::ENUM_SCENE_ADMIN) {
            //admin调用的话，去除 待专员审核
            $enums = array_filter($enums, function ($item) {
                return $item['value'] !=
                    ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_TO_BE_SPECIALIST_AUDITED;
            });
        }
        return $enums;
    }

    /**
     * 获取核销类型枚举
     * @return string[]
     */
    public function getReconcileTypeEnums(): array
    {
        return ProviderMerchantInvoiceReconciliationConstants::RECONCILE_TYPE_ENUMS;
    }

    /**
     * 获取供应商发票核销申请列表
     * @param $params
     * @return array
     */
    public function getReconciliationApplications($params): array
    {
        $params['page_size'] = $params['page_size'] ?? 10;
        $reconciliationRepository = new ReconciliationRepository();
        $builder = $reconciliationRepository->getReconciliationApplicationsBuilder($params);
        $count = $builder->count();
        $reconciliationList = $builder->select('id', 'create_time', 'apply_crm_account_id',
            'reconciliation_amount', 'reconciliation_type', 'ekuaibao_company_code', 'settle_infos_json',
            'process_status', 'ekuaibao_flow_code', 'provider_merchant_subject_name',
            'ekuaibao_flow_operator_crm_account_id')
            ->orderBy('create_time', 'desc')
            ->paginate($params['page_size'])->items();

        $reconciliationContextMapEntity = $this->buildReconciliationContextMapEntity($reconciliationList);

        $returnReconciliationList = [];
        foreach ($reconciliationList as $item) {
            $returnReconciliationList[] = $this->buildReconciliationApplication($item, $reconciliationContextMapEntity);
        }
        return ['list' => $returnReconciliationList, 'total' => $count];
    }

    /**
     * 构建供应商发票核销上下文映射实体
     *
     * @param $reconciliationList
     * @return ReconciliationContextMapEntity
     */
    private function buildReconciliationContextMapEntity($reconciliationList): ReconciliationContextMapEntity
    {
        $reconciliationContextMapEntity = new ReconciliationContextMapEntity();

        //列表所有的crm账号映射
        $crmAccountIds = collect($reconciliationList)->pluck('apply_crm_account_id')->unique()->toArray();
        $crmAccountRepository = new CrmAccountRepository();
        $crmAccountNameMap = $crmAccountRepository->getCrmAccountNameMapByCrmAccountIds($crmAccountIds)->toArray();
        $reconciliationContextMapEntity->setCrmAccountNameMap($crmAccountNameMap);

        //列表所有的易快报打款公司名称映射
        $ekuaibaoCompanyRepository = new EkuaibaoCompanyRepository();
        $ekuaibaoCompanyNameMap = $ekuaibaoCompanyRepository->getCompanyNameMapByCodes(
            collect($reconciliationList)->pluck('ekuaibao_company_code')->unique()->toArray());
        $reconciliationContextMapEntity->setEkuaibaoCompanyNameMap($ekuaibaoCompanyNameMap);

        //列表所有的最后一次核销失败日志映射
        $reconciliationApplicationIds = collect($reconciliationList)->pluck('id')->toArray();
        $providerMerchantPaymentErrorLogRepository = new ProviderMerchantPaymentErrorLogRepository();
        $lastReconcileErrorLogMap = $providerMerchantPaymentErrorLogRepository
            ->getLastReconcileErrorLogMapByReconciliationIds($reconciliationApplicationIds);
        $reconciliationContextMapEntity->setLastReconcileErrorLogMap($lastReconcileErrorLogMap);

        //列表所有原申请ids映射和供应商ids映射
        $reconciliationRepository = new ReconciliationRepository();
        $paymentRelations = $reconciliationRepository->getPaymentRelationsByReconciliationIds(
            $reconciliationApplicationIds);
        $reconcileSourceIdsMap = [];
        $providerMerchantIdsMap = [];
        $providerMerchantIds = [];
        foreach ($paymentRelations as $paymentRelation) {
            $reconciliationApplicationId = $paymentRelation['reconciliation_application_id'];
            $reconcileSourceIdsMap[$reconciliationApplicationId][] = $paymentRelation['related_application_id'];
            $providerMerchantIdsMap[$reconciliationApplicationId][] = $paymentRelation['provider_merchant_id'];
            $providerMerchantIds[] = $paymentRelation['provider_merchant_id'];
        }
        $reconciliationContextMapEntity->setReconcileSourceIdsMap($reconcileSourceIdsMap);
        $reconciliationContextMapEntity->setProviderMerchantIdsMap($providerMerchantIdsMap);

        //列表所有的供应商名称映射
        $providerMerchantRepository = new ProviderMerchantRepository();
        $providerMerchantNameMap = $providerMerchantRepository
            ->getProviderMerchantsByProviderMerchantIds($providerMerchantIds)
            ->pluck('contacter', 'providermerchantid')->toArray();
        $reconciliationContextMapEntity->setProviderMerchantNameMap($providerMerchantNameMap);
        return $reconciliationContextMapEntity;
    }

    /**
     * 构建核销申请的列表字段值
     *
     * @param Reconciliation $reconciliationApplication
     * @param ReconciliationContextMapEntity $reconciliationContextMapEntity
     * @return array
     */
    private function buildReconciliationApplication(
        Reconciliation                 $reconciliationApplication,
        ReconciliationContextMapEntity $reconciliationContextMapEntity): array
    {
        $crmAccountNameMap = $reconciliationContextMapEntity->getCrmAccountNameMap();
        $reconcileSourceIdsMap = $reconciliationContextMapEntity->getReconcileSourceIdsMap();
        $providerMerchantIdsMap = $reconciliationContextMapEntity->getProviderMerchantIdsMap();
        $providerMerchantNameMap = $reconciliationContextMapEntity->getProviderMerchantNameMap();
        $ekuaibaoCompanyNameMap = $reconciliationContextMapEntity->getEkuaibaoCompanyNameMap();
        $lastReconcileErrorLogMap = $reconciliationContextMapEntity->getLastReconcileErrorLogMap();
        $applyCrmAccountName = $crmAccountNameMap[$reconciliationApplication->apply_crm_account_id] ?? '';
        $reconciliationTypeMap = ProviderMerchantInvoiceReconciliationConstants::RECONCILE_TYPE_MAP;
        $reconcileStatusMap = ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_MAP;
        $providerMerchantIds = $providerMerchantIdsMap[$reconciliationApplication->id] ?? [];
        $providerMerchantIds = array_unique($providerMerchantIds);
        $reconcileSourceIds = $reconcileSourceIdsMap[$reconciliationApplication->id] ?? [];
        $providerMerchantNames = array_map(function ($providerMerchantId) use ($providerMerchantNameMap) {
            return $providerMerchantNameMap[$providerMerchantId] ?? '';
        }, $providerMerchantIds);

        $canSaveEkuaibaoFlowCode = false;
        if ($reconciliationApplication->process_status ==
            ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_TO_BE_REVIEWED &&
            $reconciliationApplication->ekuaibao_flow_operator_crm_account_id != env('CRM_ACCOUNT_ID_SYSTEM')) {
            $canSaveEkuaibaoFlowCode = true;
        }

        return [
            'reconciliation_application_id' => $reconciliationApplication->id,
            'reconcile_source_ids' => implode(',', $reconcileSourceIds),
            'reconciliation_type' => $reconciliationTypeMap[$reconciliationApplication->reconciliation_type] ?? '',
            'provider_merchant_names' => implode(',', $providerMerchantNames),
            'provider_merchant_subject_name' => $reconciliationApplication->provider_merchant_subject_name,
            'ekuaibao_company_name' => $ekuaibaoCompanyNameMap[$reconciliationApplication->ekuaibao_company_code] ?? '',
            'reconciliation_amount' => $reconciliationApplication->reconciliation_amount,
            'apply_time' => $reconciliationApplication->create_time,
            'formated_apply_time' => date('Y-m-d', strtotime($reconciliationApplication->create_time)),
            'apply_crm_account_name' => $applyCrmAccountName,
            'status' => $reconciliationApplication->process_status,
            'status_text' => $reconcileStatusMap[$reconciliationApplication->process_status] ?? '',
            'ekuaibao_flow_code' => !empty($reconciliationApplication->ekuaibao_flow_code) ?
                $reconciliationApplication->ekuaibao_flow_code : '--',
            'has_error_log' => isset($lastReconcileErrorLogMap[$reconciliationApplication->id]),
            'can_save_ekuaibao_flow_code' => $canSaveEkuaibaoFlowCode,
        ];
    }

    public function getReconciliationApplicationDetail($params)
    {
        $reconciliationApplicationId = $params['reconciliation_application_id'];
        $listFilter = ['reconciliation_application_id' => $reconciliationApplicationId];
        $applications = $this->getReconciliationApplications($listFilter);
        if (isset($applications['list'][0])) {
            $detail = $applications['list'][0];
            $detail['formated_apply_time'] = date('Y-m-d H:i', strtotime($detail['apply_time']));
            unset($detail['has_error_log']);
        } else {
            $detail = [];
        }
        return $detail;
    }

    /**
     * 创建供应商预存的发票核销申请并且保存关联关系
     *
     * @param $prepaymentIds
     * @param ProviderMerchantInvoiceDTO[] $invoiceDTOs
     * @param $crmAccountId
     * @return int
     * @throws BusinessException
     */
    public function createInvoiceReconciliationForPrepaymentAndSaveRelations($prepaymentIds, array $invoiceDTOs,
                                                                             $crmAccountId): int
    {
        $this->crmAccountId = $crmAccountId;
        //获取预存申请列表
        $providerMerchantPrepaymentRepository = new ProviderMerchantPrepaymentRepository();
        $prepayments = $providerMerchantPrepaymentRepository->getPrepaymentsByPrepaymentIds($prepaymentIds);

        //创建核销申请
        $reconciliationId = $this->insertReconciliation($prepayments, $invoiceDTOs);

        //保存核销申请与发票关联关系
        $this->saveReconciliationToInvoiceRelations($reconciliationId, $invoiceDTOs);

        //保存核销申请与付款关联关系
        $this->saveReconciliationToPrepaymentRelations($reconciliationId, $prepayments);

        return $reconciliationId;
    }

    /**
     * 创建核销申请
     *
     * @param $prepayments
     * @param ProviderMerchantInvoiceDTO[] $invoiceDTOs
     * @return int
     * @throws BusinessException
     */
    private function insertReconciliation($prepayments, array $invoiceDTOs): int
    {
        $this->checkCrmAccount();
        $invoiceAmounts = array_map(function ($invoiceDTO) {
            return $invoiceDTO->getInvoiceAmount();
        }, $invoiceDTOs);
        /** @var $firstPrepayment ProviderPrestore */
        $firstPrepayment = $prepayments->first();
        $providerMerchant = app(ProviderMerchantRepository::class)
            ->getProviderMerchantByProviderMerchantId($firstPrepayment->providermerchantid);
        $insertReconciliation = [
            'reconciliation_type' => ProviderMerchantInvoiceReconciliationConstants::RECONCILE_TYPE_PREPAYMENT,
            'reconciliation_amount' => array_sum($invoiceAmounts),
            'ekuaibao_company_code' => $firstPrepayment->company_id,
            'process_status' => ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_TO_BE_REVIEWED,
            'settle_info_id' => $firstPrepayment->settleinfoid,
            'settle_infos_json' => $firstPrepayment->settleinfojson,
            'apply_crm_account_id' => $this->crmAccountId,
            'provider_merchant_subject_name' => $providerMerchant->merchantname ?? '',
        ];
        $reconciliationRepository = new ReconciliationRepository();
        return $reconciliationRepository->insertReconciliation($insertReconciliation);
    }

    /**
     * 保存核销申请与发票关联关系
     *
     * @param $reconciliationId
     * @param ProviderMerchantInvoiceDTO[] $invoiceDTOs
     * @return void
     */
    private function saveReconciliationToInvoiceRelations($reconciliationId, array $invoiceDTOs)
    {
        $reconciliationRepository = new ReconciliationRepository();
        foreach ($invoiceDTOs as $invoiceDTO) {
            $relation = [
                'provider_merchant_invoice_id' => $invoiceDTO->getInvoiceId(),
                'reconciliation_application_id' => $reconciliationId,
            ];
            $reconciliationRepository->insertReconciliationToInvoiceRelations($relation);
        }
    }

    /**
     * 保存核销申请与付款关联关系
     *
     * @param $reconciliationId
     * @param $prepayments
     * @return void
     */
    private function saveReconciliationToPrepaymentRelations($reconciliationId, $prepayments)
    {
        $reconciliationRepository = new ReconciliationRepository();
        /** @var $prepayment ProviderPrestore */
        foreach ($prepayments as $prepayment) {
            $relation = [
                'reconciliation_application_id' => $reconciliationId,
                'relation_type' => ProviderMerchantInvoiceReconciliationConstants::RELATE_TO_PAYMENT_TYPE_PREPAYMENT,
                'related_application_id' => $prepayment->prestoreid,
                'provider_merchant_id' => $prepayment->providermerchantid,
            ];
            $reconciliationRepository->insertReconciliationToPaymentRelations($relation);
        }
    }

    /**
     * 驳回核销申请
     * @param $params
     * @return void
     * @throws BusinessException
     * @throws \Exception
     */
    public function rejectReconciliationApplication($params)
    {
        $reconciliationApplicationId = $params['reconciliation_application_id'];
        $this->adminAccountId = $params['admin_account_id'];
        $this->checkAdminAccount();

        $reconciliationRepository = new ReconciliationRepository();
        $application = $reconciliationRepository->getReconciliationApplicationById($reconciliationApplicationId);
        $this->checkBeforeRejectReconciliationApplication($params, $application);

        DB::beginTransaction();
        try {
            // 更新状态为已驳回
            $reconciliationRepository->updateReconciliationStatus(
                $reconciliationApplicationId,
                ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_REJECTED
            );

            // 写入驳回日志
            $this->logRejectError($reconciliationApplicationId, $params['reject_content'], $params['admin_account_id']);

            // 分类型处理
            $type = $application->reconciliation_type;
            if ($type == ProviderMerchantInvoiceReconciliationConstants::RECONCILE_TYPE_PREPAYMENT) {
                $this->handlePrepaymentRejection($reconciliationApplicationId);
            } elseif ($type == ProviderMerchantInvoiceReconciliationConstants::RECONCILE_TYPE_SETTLEMENT) {
                $this->handleSettlementRejection($reconciliationApplicationId);
            } else {
                throw new BusinessException('不支持的核销类型');
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function logRejectError($reconciliationId, $content, $adminAccountId = 0, $crmAccountId = 0)
    {
        $log = [
            'application_type' => ProviderMerchantPaymentErrorLogConstants::APPLICATION_TYPE_INVOICE_RECONCILIATION,
            'related_application_id' => $reconciliationId,
            'admin_account_id' => $adminAccountId,
            'crm_account_id' => $crmAccountId,
            'error_content' => $content,
            'step' => ProviderMerchantPaymentErrorLogConstants::STEP_REJECT_INVOICE_RECONCILIATION,
        ];
        (new ProviderMerchantPaymentErrorLogRepository())->insertErrorLog($log);
    }

    private function handlePrepaymentRejection(int $reconciliationId)
    {
        $repo = new ReconciliationRepository();
        $invoiceIds = $repo->getInvoiceIdsByReconciliationId($reconciliationId);
        $relations = $repo->getPaymentRelationsByReconciliationId($reconciliationId);

        $prepaymentIds = $relations
            ->where('relation_type', ProviderMerchantInvoiceReconciliationConstants::RELATE_TO_PAYMENT_TYPE_PREPAYMENT)
            ->pluck('related_application_id')
            ->toArray();

        if (!empty($prepaymentIds)) {
            app(ProviderMerchantInvoiceRepository::class)
                ->voidInvoiceToPrepaymentRelations($invoiceIds,$prepaymentIds);
            app(ProviderMerchantPrepaymentService::class)->savePrepaymentsInvoiceStatus($prepaymentIds);
        }
    }

    private function handleSettlementRejection(int $reconciliationId)
    {
        $repo = new ReconciliationRepository();
        $invoiceIds = $repo->getInvoiceIdsByReconciliationId($reconciliationId);
        $relations = $repo->getPaymentRelationsByReconciliationId($reconciliationId);

        $settlementIds = $relations
            ->where('relation_type', ProviderMerchantInvoiceReconciliationConstants::RELATE_TO_PAYMENT_TYPE_SETTLEMENT)
            ->pluck('related_application_id')
            ->toArray();

        if (!empty($settlementIds)) {
            app(ProviderMerchantInvoiceRepository::class)
                ->voidInvoiceToSettlementRelations($invoiceIds, $settlementIds);
            app(OrderSettleService::class)->saveOrderSettlesInvoiceStatus($settlementIds);
        }
    }


    /**
     * @param $params
     * @param Reconciliation|null $oldReconciliationApplication
     * @return void
     * @throws BusinessException
     */
    private function checkBeforeRejectReconciliationApplication($params,
                                                                Reconciliation $oldReconciliationApplication = null)
    {
        if (empty($params['reject_content'])) {
            throw new BusinessException(MessageConstants::REJECT_RECONCILIATION_REASON);
        }
        if (empty($oldReconciliationApplication)) {
            throw new BusinessException(MessageConstants::RECONCILIATION_NOT_EXIST);
        }
        if ($oldReconciliationApplication->process_status !=
            ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_TO_BE_REVIEWED) {
            throw new BusinessException(MessageConstants::RECONCILIATION_STATUS_NOT_ALLOW);
        }
    }

    /**
     * 确认核销
     * @param $params
     * @return void
     * @throws BusinessException
     */
    public function confirmReconcile($params)
    {
        $reconciliationApplicationId = $params['reconciliation_application_id'];
        $this->adminAccountId = $params['admin_account_id'];
        $reconciliationRepository = new ReconciliationRepository();
        $oldReconciliationApplication = $reconciliationRepository
            ->getReconciliationApplicationById($reconciliationApplicationId);
        //确认核销前检查
        $this->checkAdminAccount();
        $this->checkBeforeConfirmReconcile($oldReconciliationApplication);

        //更新核销申请状态
        $reconciliationRepository->updateReconciliationStatus($reconciliationApplicationId,
            ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_COMPLETED);
    }

    /**
     * @param $oldReconciliationApplication
     * @return void
     * @throws BusinessException
     */
    private function checkBeforeConfirmReconcile($oldReconciliationApplication)
    {
        if (empty($oldReconciliationApplication)) {
            throw new BusinessException(MessageConstants::RECONCILIATION_NOT_EXIST);
        }
        if ($oldReconciliationApplication->process_status !=
            ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_TO_BE_REVIEWED) {
            throw new BusinessException(MessageConstants::RECONCILIATION_STATUS_NOT_ALLOW);
        }
    }

    /**
     * 保存易快报单号
     * @param $params
     * @return void
     * @throws BusinessException
     * @throws CurlException
     */
    public function saveEkuaibaoFlowCode($params)
    {
        $ekuaibaoFlowCode = $params['ekuaibao_flow_code'];
        $reconciliationId = $params['reconciliation_application_id'];
        $this->crmAccountId = $params['crm_account_id'];
        $this->checkCrmAccount();

        //获取核销申请信息
        $reconciliationRepository = new ReconciliationRepository();
        $reconciliation = $reconciliationRepository->getReconciliationApplicationById($reconciliationId);
        $this->checkReconciliationBeforeSaveEkuaibaoFlowCode($reconciliation);

        //获取易快报单据详情
        $ekuaibaoFlowService = new EkuaibaoFlowService();
        $flowDetail = $ekuaibaoFlowService->getFlowDetailByFlowCode($ekuaibaoFlowCode);

        //检查单据信息
        $this->checkFlowBeforeSaveEkuaibaoFlowCode($flowDetail, $reconciliation);

        //保存易快报单号
        $ekuaibaoFlowId = $flowDetail['value']['id'] ?? '';
        $reconciliationRepository->updateEkuaibaoFlowAndOperator($reconciliationId, $ekuaibaoFlowCode, $ekuaibaoFlowId,
            $this->crmAccountId);
    }

    /**
     * @return void
     * @throws BusinessException
     */
    private function checkCrmAccount()
    {
        if (empty($this->crmAccountId)) {
            throw new BusinessException(MessageConstants::INVALID_OPERATOR);
        }
        $crmAccountRepository = new CrmAccountRepository();
        $crmAccount = $crmAccountRepository->getCrmAccountByCrmAccountId($this->crmAccountId);
        if (empty($crmAccount)) {
            throw new BusinessException(MessageConstants::INVALID_OPERATOR);
        }
    }

    private function checkAdminAccount()
    {
        if (empty($this->adminAccountId)) {
            throw new BusinessException(MessageConstants::INVALID_OPERATOR);
        }
        $adminAccount = app(AdminRepository::class)->getAdminAccountByAdminId($this->adminAccountId);
        if (empty($adminAccount)) {
            throw new BusinessException(MessageConstants::INVALID_OPERATOR);
        }
    }

    /**
     * 保存核销申请的易快报流水号前检查核销申请信息
     * @param Reconciliation|null $reconciliation
     * @return void
     * @throws BusinessException
     */
    private function checkReconciliationBeforeSaveEkuaibaoFlowCode(Reconciliation $reconciliation = null)
    {
        if (empty($reconciliation)) {
            throw new BusinessException(MessageConstants::RECONCILIATION_NOT_EXIST);
        }
        if ($reconciliation->process_status !=
            ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_TO_BE_REVIEWED) {
            $processStatusMap = ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_MAP;
            $statusName = $processStatusMap[$reconciliation->process_status] ?? '未知';
            $message = sprintf(MessageConstants::RECONCILIATION_STATUS_NOT_ALLOW_TO_SAVE_EKUAIBAO_FLOW_CODE,
                $statusName);
            throw new BusinessException($message);
        }

        if ($reconciliation->ekuaibao_flow_operator_crm_account_id == env('CRM_ACCOUNT_ID_SYSTEM')) {
            throw new BusinessException(MessageConstants::RECONCILIATION_AUTO_PROCESS_CANNOT_SAVE_EKUAIBAO_FLOW_CODE);
        }
    }

    /**
     * 保存核销申请的易快报流水号前检查单据信息
     * @param $flowDetail
     * @param Reconciliation $reconciliation
     * @return void
     * @throws BusinessException|CurlException
     */
    private function checkFlowBeforeSaveEkuaibaoFlowCode($flowDetail, Reconciliation $reconciliation)
    {
        if (empty($flowDetail)) {
            throw new BusinessException(MessageConstants::EKUAIBAO_FLOW_NOT_EXIST);
        }
        //校验核销金额相等
        $writtenOffMoneyDTO = app(EkuaibaoFlowService::class)->buildWrittenOffMoneyDTOBYFlowDetail($flowDetail);
        if (bccomp($writtenOffMoneyDTO->getStandard(), $reconciliation->reconciliation_amount, 2) != 0) {
            throw new BusinessException(sprintf(MessageConstants::EKUAIBAO_WRITTEN_OFF_AMOUNT_NOT_EQUAL,
                $writtenOffMoneyDTO->getStandard(), $reconciliation->reconciliation_amount));
        }

        //校验收款信息一致
        $providerMerchantPaymentService = new ProviderMerchantPaymentService();
        $providerMerchantPaymentService->checkFlowSettleInfoConsistency($flowDetail,
            $reconciliation->settle_infos_json, $reconciliation->settle_info_id);

        //校验是否重复
        $providerMerchantPaymentService->checkDuplicateEkuaibaoFlow($flowDetail);
    }

    /**
     * @throws BusinessException
     * @throws BusinessWithoutErrorReportException
     * @throws AliyunException
     * @throws \Exception
     */
    public function auditReconciliationBySpecialist($params)
    {
        $reconciliationId = $params['reconciliation_id'];
        $crmAccountId = $params['crm_account_id'];
        $auditResult = $params['audit_result'];
        $auditComment = $params['audit_comment'] ?? '';
        $this->crmAccountId = $crmAccountId;
        $this->checkCrmAccount();
        $reconciliationRepository = new ReconciliationRepository();
        $oldReconciliation = $reconciliationRepository->getReconciliationApplicationById($reconciliationId);
        if (empty($oldReconciliation)) {
            throw new BusinessWithoutErrorReportException(MessageConstants::RECONCILIATION_NOT_EXIST);
        }
        $this->checkBeforeAuditReconciliation($oldReconciliation);
        if ($auditResult == ProviderMerchantInvoiceReconciliationConstants::AUDIT_RESULT_PASS) {
            $this->auditPassReconciliationBySpecialist($oldReconciliation, $crmAccountId);
        } else {
            DB::beginTransaction();
            $this->rejectReconciliationBySpecialist($oldReconciliation, $auditComment, $crmAccountId);
            DB::commit();
        }
    }

    /**
     * @throws AliyunException
     */
    private function auditPassReconciliationBySpecialist(Reconciliation $oldReconciliation, $crmAccountId)
    {
        $reconciliationId = $oldReconciliation->id;
        //发送创建核销单据的MNS消息
        MNSClient::getInstance()->sendTopicJsonMsg(
            env('MNS_COMMON_EVENT_TOPIC_NAME'),
            [
                'application_type' => EkuaibaoFlowCreatorConstants::APPLICATION_TYPE_ORDER_SETTLE,
                'flow_type' => EkuaibaoFlowCreatorConstants::FLOW_TYPE_RECONCILIATION,
                'crm_account_id' => $crmAccountId,
                'reconciliation_id' => $reconciliationId,
            ],
            EkuaibaoFlowCreatorConstants::MNS_MESSAGE_TAG
        );

        //更新状态
        $reconciliationRepository = new ReconciliationRepository();
        $reconciliationRepository->updateReconciliation($reconciliationId, [
            'process_status' => ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_TO_BE_REVIEWED,
            'specialist_auditor_crm_account_id' => $crmAccountId,
        ]);
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    private function rejectReconciliationBySpecialist(Reconciliation $oldReconciliation, $auditComment, $crmAccountId)
    {
        if (empty($auditComment)) {
            throw new BusinessWithoutErrorReportException(MessageConstants::REJECT_RECONCILIATION_REASON);
        }
        $reconciliationId = $oldReconciliation->id;
        // 更新状态为已驳回
        $reconciliationRepository = new ReconciliationRepository();
        $reconciliationRepository->updateReconciliation($reconciliationId, [
            'process_status' => ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_REJECTED,
            'audit_comment_by_specialist' => $auditComment,
            'specialist_auditor_crm_account_id' => $crmAccountId,
        ]);

        // 写入驳回日志
        $this->logRejectError($reconciliationId, $auditComment, 0, $crmAccountId);

        // 分类型处理
        $type = $oldReconciliation->reconciliation_type;
        if ($type == ProviderMerchantInvoiceReconciliationConstants::RECONCILE_TYPE_SETTLEMENT) {
            $this->handleSettlementRejection($reconciliationId);
        } else {
            throw new BusinessWithoutErrorReportException('不支持的核销类型');
        }
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    private function checkBeforeAuditReconciliation(Reconciliation $oldReconciliation)
    {
        if ($oldReconciliation->process_status !=
            ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_TO_BE_SPECIALIST_AUDITED) {
            throw new BusinessWithoutErrorReportException(MessageConstants::RECONCILIATION_STATUS_NOT_ALLOW);
        }
    }

    public function createReconciliationForOrderSettle($settleIds, array $invoiceDTOs, $crmAccountId = 0): int
    {
        $this->crmAccountId = $crmAccountId;
        //获取结算申请列表
        $settles = app(OrderSettleRepository::class)->getOrderSettlesByIds($settleIds);

        //创建核销申请
        $reconciliationId = $this->insertReconciliationForOrderSettle($settles, $invoiceDTOs, $crmAccountId);

        //保存核销申请与发票关联关系
        $this->saveReconciliationToInvoiceRelations($reconciliationId, $invoiceDTOs);

        //保存核销申请与付款关联关系
        $this->saveReconciliationToOrderSettleRelations($reconciliationId, $settles);

        return $reconciliationId;
    }

    private function insertReconciliationForOrderSettle($settles, array $invoiceDTOs, $crmAccountId = 0): int
    {
        $invoiceAmounts = array_map(function ($invoiceDTO) {
            return $invoiceDTO->getInvoiceAmount();
        }, $invoiceDTOs);
        /** @var $firstSettle OrderSettle */
        $firstSettle = $settles->first();
        $providerMerchant = app(ProviderMerchantRepository::class)
            ->getProviderMerchantByProviderMerchantId($firstSettle->supplierid);
        $insertReconciliation = [
            'reconciliation_type' => ProviderMerchantInvoiceReconciliationConstants::RECONCILE_TYPE_SETTLEMENT,
            'reconciliation_amount' => array_sum($invoiceAmounts),
            'ekuaibao_company_code' => $firstSettle->company_id,
            'process_status' => ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_TO_BE_SPECIALIST_AUDITED,
            'settle_info_id' => $firstSettle->merchantsettleinfoid,
            'settle_infos_json' => $firstSettle->acceptbankinfo,
            'apply_crm_account_id' => $crmAccountId,
            'provider_merchant_subject_name' => $providerMerchant->merchantname ?? '',
        ];
        return (new ReconciliationRepository())->insertReconciliation($insertReconciliation);
    }

    private function saveReconciliationToOrderSettleRelations($reconciliationId, $settles)
    {
        $reconciliationRepository = new ReconciliationRepository();
        /** @var $settle OrderSettle */
        foreach ($settles as $settle) {
            $relation = [
                'reconciliation_application_id' => $reconciliationId,
                'relation_type' => ProviderMerchantInvoiceReconciliationConstants::RELATE_TO_PAYMENT_TYPE_SETTLEMENT,
                'related_application_id' => $settle->settleid,
                'provider_merchant_id' => $settle->supplierid,
            ];
            $reconciliationRepository->insertReconciliationToPaymentRelations($relation);
        }
    }
}
