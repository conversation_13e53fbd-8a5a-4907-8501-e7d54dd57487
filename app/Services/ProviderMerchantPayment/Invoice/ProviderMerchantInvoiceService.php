<?php

namespace App\Services\ProviderMerchantPayment\Invoice;

use App\Constants\Invoice\InvoiceTypeConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants;
use App\Entities\ProviderMerchantPayment\ProviderMerchantInvoiceDTO;
use App\Models\ProviderMerchantInvoice;
use App\Repositories\ProviderMerchantPayment\InvoiceReconciliationApplicationRepository;
use App\Repositories\ProviderMerchantPayment\OrderSettleRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantInvoiceRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantPrepaymentRepository;

class ProviderMerchantInvoiceService
{
    /**
     * @param array $invoices
     * @return ProviderMerchantInvoiceDTO[]
     */
    public function buildProviderMerchantInvoiceDTOs(array $invoices): array
    {
        $providerMerchantInvoiceDTOs = [];
        foreach ($invoices as $invoice) {
            $invoice['invoice_amount'] = $invoice['tax_inclusive_amount'];
            $providerMerchantInvoiceDTO = new ProviderMerchantInvoiceDTO($invoice);
            $providerMerchantInvoiceDTOs[] = $providerMerchantInvoiceDTO;
        }
        return $providerMerchantInvoiceDTOs;
    }

    /**
     * 保存发票列表
     * @param ProviderMerchantInvoiceDTO[] $newInvoiceDTOs
     * @return ProviderMerchantInvoiceDTO[]
     */
    public function saveInvoices(array $newInvoiceDTOs): array
    {
        //插入新增的发票
        foreach ($newInvoiceDTOs as $key => $invoiceDTO) {
            //如果发票id大于0，说明是已存在的发票，不需要插入
            if ($invoiceDTO->getInvoiceId() > 0) {
                continue;
            }
            $insertInvoice = [
                'buyer_name' => $invoiceDTO->getBuyerName(),
                'seller_name' => $invoiceDTO->getSellerName(),
                'invoice_type' => $invoiceDTO->getInvoiceType(),
                'invoice_amount' => $invoiceDTO->getInvoiceAmount(),
                'file_url' => $invoiceDTO->getFileUrl(),
                'tax_rate' => $invoiceDTO->getTaxRate(),
                'invoice_number' => $invoiceDTO->getInvoiceNumber(),
                'invoice_code' => $invoiceDTO->getInvoiceCode(),
                'invoice_time' => $invoiceDTO->getInvoiceTime(),
            ];
            $invoiceId = ProviderMerchantInvoice::insertGetId($insertInvoice);
            $invoiceDTO->setInvoiceId($invoiceId);
            $newInvoiceDTOs[$key] = $invoiceDTO;
        }
        return $newInvoiceDTOs;
    }

    public function getPaymentInvoices($params): array
    {
        $isOrderSettle = false;
        $invoiceIds = [];
        $invoiceReconciliationApplicationRepository = new InvoiceReconciliationApplicationRepository();
        $orderSettleRepository = app(OrderSettleRepository::class);
        if (!empty($params['reconciliation_application_id'])) {
            $reconciliationId = $params['reconciliation_application_id'];
            $invoiceIds = $invoiceReconciliationApplicationRepository
                ->getInvoiceIdsByReconciliationId($reconciliationId);
        } elseif (!empty($params['order_settle_id'])) {
            $isOrderSettle = true;
            $invoiceIds = $orderSettleRepository->getInvoiceIdsBySettleId($params['order_settle_id']);
        } elseif (!empty($params['prepayment_id'])) {
            $providerMerchantPrepaymentRepository = new ProviderMerchantPrepaymentRepository();
            $invoiceIds = $providerMerchantPrepaymentRepository->getInvoiceIdsByPrepaymentId($params['prepayment_id']);
        }
        $providerMerchantInvoiceRepository = new ProviderMerchantInvoiceRepository();
        $invoices = $providerMerchantInvoiceRepository->getInvoicesByInvoiceIds($invoiceIds);
        $returnInvoices = [];
        $invoiceToReconcileRelations = $providerMerchantInvoiceRepository
            ->getReconcileRelationsByInvoiceIds($invoiceIds);
        $invoiceToReconcileIds = $invoiceToReconcileRelations->pluck('reconciliation_application_id')->toArray();
        $reconcileApplications = $invoiceReconciliationApplicationRepository
            ->getReconciliationApplicationsByIds($invoiceToReconcileIds);

        $relatedOrderSettles = [];
        if ($isOrderSettle) {
            //合并开票的情况下，一定只有一张发票、一笔核销，所以不用写成map了
            $relatedOrderSettles = $this->getRelatedOrderSettles($invoiceToReconcileIds, $params['order_settle_id']);
        }

        foreach ($invoices as $invoice) {
            $reconcileInvoice = $this->buildPaymentInvoices($invoice);
            $reconciliationId = $invoiceToReconcileRelations->where('provider_merchant_invoice_id', $invoice->id)
                ->pluck('reconciliation_application_id')->first();
            $reconcileApplication = $reconcileApplications->where('id', $reconciliationId)->first();
            if (!empty($reconcileApplication)) {
                $processStatus = $reconcileApplication->process_status;
                $processStatusName = ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_MAP[$processStatus]
                    ?? '';
                $reconcileInvoice['reconciliation_application_id'] = $reconciliationId;
                $reconcileInvoice['reconcile_ekuaibao_flow_code'] = $reconcileApplication->ekuaibao_flow_code;
                $reconcileInvoice['reconcile_status_name'] = $processStatusName;
            } else {
                $reconcileInvoice['reconciliation_application_id'] = 0;
                $reconcileInvoice['reconcile_ekuaibao_flow_code'] = '';
                $reconcileInvoice['reconcile_status_name'] = '';
            }
            $reconcileInvoice['related_payments'] = $relatedOrderSettles;

            $returnInvoices[] = $reconcileInvoice;
        }
        //同一笔核销的发票排在一起
        array_multisort(
            array_column($returnInvoices, 'reconciliation_application_id'),
            SORT_DESC, $returnInvoices);

        return $returnInvoices;
    }

    private function getRelatedOrderSettles($reconcileIds, $orderSettleId): array
    {
        $paymentRelations = app(InvoiceReconciliationApplicationRepository::class)
            ->getPaymentRelationsByReconciliationIds($reconcileIds);
        $relatedOrderSettles = app(OrderSettleRepository::class)->getOrderSettlesWithProviderMerchantByIds(
            $paymentRelations->pluck('related_application_id')->toArray());
        $relatedOrderSettles = $relatedOrderSettles->where('settleid', '<>', $orderSettleId);
        $returnRelatedOrderSettles = [];
        foreach ($relatedOrderSettles as $relatedOrderSettle) {
            $returnRelatedOrderSettles[] = [
                'apply_amount' => $relatedOrderSettle->applyamt,
                'provider_merchant_contact_name' => $relatedOrderSettle->providerMerchant->contacter,
                'provider_merchant_id' => $relatedOrderSettle->providerMerchant->providermerchantid,
            ];
        }
        return $returnRelatedOrderSettles;
    }

    public function buildPaymentInvoices(ProviderMerchantInvoice $invoice): array
    {
        $invoiceTypeNameMap = InvoiceTypeConstants::INVOICE_TYPE_NAME_MAP;
        return [
            'invoice_id' => $invoice->id,
            'buyer_name' => $invoice->buyer_name,
            'seller_name' => $invoice->seller_name,
            'invoice_type' => $invoice->invoice_type,
            'invoice_type_name' => $invoiceTypeNameMap[$invoice->invoice_type] ?? '',
            'invoice_amount' => $invoice->invoice_amount,
            'tax_rate' => $invoice->tax_rate,
            'invoice_number' => $invoice->invoice_number,
            'invoice_time' => $invoice->invoice_time,
            'formated_invoice_time' => date('Y/m/d', strtotime($invoice->invoice_time)),
            'file_url' => $invoice->file_url,
            'reconciliation_application_id' => 0,
            'reconcile_ekuaibao_flow_code' => '',
            'reconcile_status_name' => '',
        ];
    }
}