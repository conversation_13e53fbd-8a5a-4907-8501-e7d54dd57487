<?php

namespace App\Services\StandardReport;

use App\Exceptions\BusinessException;
use App\Models\BuffetReportTemplate;
use App\Constants\StandardReport\BuffetReportTemplateStatusConstants;

trait TemplateOperate {

    /**
     * 尝试获取模板信息并检查模板是否可用
     * @param $templateId
     * @return BuffetReportTemplate|\Illuminate\Database\Eloquent\Model
     * @throws BusinessException
     */
    public function getValidTemplate ($templateId) {
        $templateInfo = BuffetReportTemplate::where('id', $templateId)
            ->where('isvoid', 0)
            ->select(['id', 'status', 'purpose'])
            ->first();
        if (!$templateInfo) {
            throw new BusinessException('模板不存在');
        }

        if ($templateInfo->status == BuffetReportTemplateStatusConstants::CLOSE) {
            throw new BusinessException('模板未启用');
        }

        $templateInfo->load(['steps','steps.widgets']);

        return $templateInfo;
    }
}