<?php

namespace App\Services\StandardReport;

use App\Constants\StandardReport\ReportCreateFromConstants;
use App\Entities\StandardReport\StandardReportDTO;
use App\Models\AccountInfo;
use App\Models\OrderInfo;
use App\Models\ProviderProduct;
use App\Models\StandardReport;
use App\Models\StandardReportIndex;
use Yanqu\YanquPhplib\Openapi\StandardReport\Constants\StandardReportSceneConstants;
use DB;
use Yanqu\YanquPhplib\Openapi\StandardReport\Constants\StandardReportPurposeConstants;

trait ReportSearch {

    /**
     * 构建索引
     */
    public function createIndex (StandardReport $report, StandardReportDTO $standardReportDTO) {
        $orderList = OrderInfo::with(['orderAllot:oid,sendproductid'])
            ->whereIn('oid', $standardReportDTO->getOrderIdList())
            ->select(['oid', 'osn', 'useruuid', 'accountid'])
            ->get();
        foreach ($orderList as $order) {
            $index = new StandardReportIndex();
            $index->report_no = $report->report_no;
            $index->order_account_id = $order->accountid;
            $index->oid = $order->oid;
            $index->osn = $order->osn;
            if ($order->orderAllot) {
                $index->provider_product_id = $order->orderAllot->sendproductid;
            }
            $index->buffet_address_id = $order->useruuid;
            $index->standard_report_id = $report->id;
            $index->save();
        }
    }

    /**
     * 更新索引
     */
    public function updateIndex(StandardReport $report, StandardReportDTO $standardReportDTO) {
        $orderList = OrderInfo::with(['orderAllot:oid,sendproductid'])
            ->whereIn('oid', $standardReportDTO->getOrderIdList())
            ->select(['oid', 'osn', 'useruuid', 'accountid'])
            ->get();
        foreach ($orderList as $order) {
            $index = StandardReportIndex::query()
                ->where("standard_report_id", $standardReportDTO->getStandardReportId())
                ->where("oid", $order->oid)
                ->where("isvoid", 0)
                ->first();
            if (!$index) {
                continue;
            }
            $index->report_no = $report->report_no;
            $index->order_account_id = $order->accountid;
            $index->oid = $order->oid;
            $index->osn = $order->osn;
            if ($order->orderAllot) {
                $index->provider_product_id = $order->orderAllot->sendproductid;
            }
            $index->buffet_address_id = $order->useruuid;
            $index->standard_report_id = $report->id;
            $index->save();
        }
    }

    /**
     * 获取报告列表
     *
     * @param int $page
     * @param int $pageSize
     * @param array $condition
     * @return array
     */
    public function getReportList (int $page = 1, int $pageSize = 10, array $condition = []): array {
        //搜索报告
        [$idList, $total] = $this->searchReport($page, $pageSize, $condition);

        //报告详细数据获取
        $reports = StandardReport::with([
            'reportIndex:oid,osn,provider_product_id,standard_report_id,order_account_id',
            'updaterInfo:accountid,realname',
            'reportIndex.orderAccount:accountid,contacter',
            'reportIndex.providerProduct:providerproductid,pname',
            'template:id,name,desc',
        ])
            ->whereIn('id', $idList)
            ->select([
                'id',
                'report_template_id',
                'report_no',
                'purpose',
                'scene',
                'word_generate_status',
                'pdf_convert_status',
                'word_file_id',
                'pdf_file_id',
                'created_from',
                'result_desc',
                'updater',
                'update_time',
            ])->orderByDesc('id')->get();

        $list = [];
        if ($reports->isNotEmpty()) {
            foreach ($reports as $report) {
                $list[] = $this->buildReportListItem($report);
            }
        }
        return [
            'list'  => $list,
            'total' => $total,
        ];
    }

    /**
     * 搜索报告
     * 百万级别数据空条件查询时间在1s左右(原因是需要对standard_report_id去重，所以查询速度较慢)
     * 后期存在性能风险，需要优化
     *
     * @param int $page
     * @param int $pageSize
     * @param array $condition
     * @return array
     */
    protected function searchReport (int $page = 1, int $pageSize = 10, array $condition = []): array {
        $query = StandardReportIndex::query();

        //订单号搜索
        if (!empty($condition['osn'])) {
            $query = $query->where('standard_report_index.osn', $condition['osn']);
        }

        if (!empty($condition['report_no'])) {
            $query->where('standard_report_index.report_no', $condition['report_no']);
        }

        if (!empty($condition['buffet_address_id'])) {
            $query->where('standard_report_index.buffet_address_id', $condition['buffet_address_id']);
        }

        //仪器名称搜索
        if (!empty($condition['buffet_name'])) {
            $providerProductIdList = ProviderProduct::query()
                ->where('pname', 'LIKE', '%'.$condition['buffet_name'].'%')
                ->pluck('providerproductid');
            $query->whereIn('standard_report_index.provider_product_id', $providerProductIdList);
        }

        //用户名(联系人搜索)
        if (!empty($condition['order_contacter'])) {
            $accountIds = AccountInfo::where(
                'contacter',
                'LIKE',
                '%'.$condition['order_contacter'].'%'
            )
                ->pluck('accountid');
            $query->whereIn('standard_report_index.order_account_id', $accountIds);
        }

        if (!empty($condition['purpose'])) {
            $query->leftJoin('standard_report', 'standard_report_index.standard_report_id', '=', 'standard_report.id')
                ->where('standard_report.purpose', $condition['purpose']);
        }

        $query = $query->where('standard_report_index.isvoid', 0)
            ->orderByDesc('standard_report_index.standard_report_id');

        $total = 100;

        $idList = $query->distinct('standard_report_index.standard_report_id')
            ->skip(($page - 1) * $pageSize)
            ->take($pageSize)
            ->pluck('standard_report_id')
            ->toArray();

        return [$idList, $total];
    }

    /**
     * 构建报告列表项
     *
     * @param StandardReport $report
     * @return array
     */
    protected function buildReportListItem (StandardReport $report): array {
        //获取仪器(测试项目)名称
        $buffetNameList = [];
        if ($report->reportIndex->isNotEmpty()) {
            foreach ($report->reportIndex as $reportItem) {
                if (!empty($reportItem->providerProduct)) {
                    $buffetNameList[] = $reportItem->providerProduct->pname;
                }
            }
        }

        //获取订单预约人
        $orderAccountList = [];
        if ($report->reportIndex->isNotEmpty()) {
            foreach ($report->reportIndex as $reportItem) {
                if (!empty($reportItem->orderAccount)) {
                    $orderAccountList[] = $reportItem->orderAccount->contacter;
                }
            }
        }

        $createFromDesc = ReportCreateFromConstants::TYPE_MAP[$report->created_from] ?? '';
        $sceneDesc = StandardReportSceneConstants::SCENE_DESC_MAP[$report->scene] ?? '';

        return [
            'id'                   => $report->id,
            'template_id'          => $report->report_template_id,
            'template_name'        => $report->template->name,
            'template_desc'        => $report->template->desc,
            'purpose'              => StandardReportPurposeConstants::PURPOSE_MAP[$report->purpose] ?? '',
            'report_no'            => $report->report_no,
            'created_from'         => $report->created_from,
            'osn_list'             => $report->reportIndex->pluck('osn')->toArray(),
            'oid_list'             => $report->reportIndex->pluck('oid')->toArray(),
            'order_contacter_list' => $orderAccountList,
            'buffet_name_list'     => $buffetNameList,
            'word_generate_status' => $report->word_generate_status,
            'pdf_convert_status'   => $report->pdf_convert_status,
            'update_time'          => $report->update_time,
            'word_file_id'         => $report->word_file_id,
            'pdf_file_id'          => $report->pdf_file_id,
            'result_desc'          => $report->result_desc,
            'updater_name'         => $report->updaterInfo ? $report->updaterInfo->realname : '',
            'create_scene_desc'    => $createFromDesc.'（'.$sceneDesc.'）',
        ];
    }
}