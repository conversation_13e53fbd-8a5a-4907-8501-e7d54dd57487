<?php
/**
 * 乐橙直播相关
 */

namespace App\Services\Live;

use App\Constants\LiveConstant;
use App\Models\AccessToken;
use App\Services\BaseService;
use Illuminate\Support\Facades\DB;
use Yanqu\YanquPhplib\Curl\CurlUtil;
use Yanqu\YanquPhplib\YqLog\YqLog;
use RuntimeException;
use Illuminate\Support\Facades\Cache;

class LiveService extends BaseService
{
    /**
     * 更新access_token
     * @return true
     */
    public function accessToken()
    {
        try {
            //请求接口
            $data = $this->publicRequestMethod('accessToken', "{}",'access_token');
            //处理返回值
            if ($data['result']['code'] == "0") {
                DB::table("access_token")
                    ->where("type", LiveConstant::LE_CHENG_ACCESS_TOKEN_TYPE)
                    ->update([
                        "access_token" => $data['result']['data']['accessToken'],
                        "addtime" => time()
                    ]);
            } else {
                throw new RuntimeException('获取token失败:' . $data['result']['code'] . "," . $data['result']['msg']);
            }
        } catch (\Exception $e) {
            YqLog::logger("openapi:LiveService:getAccessToken")->error($e);
        }
        return true;
    }

    public function getLiveStreamInfo($deviceIds)
    {
        try {
            $liveUrlList = [];
            foreach ($deviceIds as $key => $deviceId) {
                $data = $this->publicRequestMethod('getLiveStreamInfo', [
                    "token" => $this->getAaccessToken(),
                    "deviceId" => $deviceId,
                    "channelId" => 0,
                ], $key);
                if ($data['result']['code'] == '0') {
                    $streamInfo = $this->liveStreamInfo($data['result']['data']['streams']);
                    $streamInfo['device_id'] = $deviceId;
                    $liveUrlList[] = $streamInfo;
                }
            }
            return ["status" => true, "message" => "success", "data" => $liveUrlList];
        } catch (\Exception $e) {
            return ["status" => false, "message" => $e->getMessage()];
        }
    }

    /**
     * 签名
     * @param $time
     * @param $nonce
     * @return string
     */
    private function getSign($time, $nonce)
    {
        $appSecret = config("constants.lc_app_secret");
        $signTemplate = "time:" . $time . ",nonce:" . $nonce . ",appSecret:" . $appSecret;
        return md5($signTemplate);
    }

    /**
     * 获取公共请求参数
     * @param $requestId
     * @return array
     */
    private function getPublicRequestParams($key)
    {
        //获取appid
        $appId = config("constants.lc_appid");
        //版本
        $ver = LiveConstant::LE_CHENG_VER;
        //请求时间
        $time = time();
        //获取唯一id（请求记录的id）
        //随机数32位
        $nonce = md5($time.$key);
        //id 请求唯一标示ID，每次请求调用时传入唯一非空字符串
        $id = $time.$key;
        //获取签名
        $sign = $this->getSign($time, $nonce);

        return [
            "system" => [
                "ver" => $ver,
                "sign" => $sign,
                "appId" => $appId,
                "time" => $time,
                "nonce" => $nonce
            ],
            "params" => '{}',
            "id" => $id
        ];
    }

    /**
     * 获取access_token
     * @return mixed|null
     */
    private function getAaccessToken()
    {
        return DB::table("access_token")
            ->where("type", LiveConstant::LE_CHENG_ACCESS_TOKEN_TYPE)
            ->value('access_token');
    }

    /**
     * 公共请求方法
     * @param $path
     * @param $params
     * @return mixed
     * @throws \Yanqu\YanquPhplib\Exception\CurlException
     */
    private function publicRequestMethod($path, $params, $key = 0)
    {
        $url = config("constants.lc_url") . "/" . $path;
        //获取公共请求参数
        $requestData = $this->getPublicRequestParams($key);
        //设置私有参数
        $requestData['params'] = $params;
        $data = CurlUtil::postJson(
            $url,
            $requestData,
            ['Content-Type: application/x-www-form-urlencoded']
        );
        return json_decode($data, true);
    }


    public function liveStreamInfo($data)
    {
        $arr = [
            "hls" => "",
            "status" => "",
        ];
        foreach ($data as $value) {
            if ($value['streamId'] == 1 && strpos($value['hls'], config("constants.lc_hls_keywords")) !== false) {
                $arr['hls'] = $value['hls'];
                $arr['status'] = $value['status'];
                break;
            }
        }
        return $arr;
    }

    public function getMinToken($deviceId)
    {
        //判断是否存在缓存
        $minToken = Cache::store("redis")->get('min_token_'.$deviceId);
        if (empty($minToken)) {
            $url = config("constants.lc_url") . "/createWeChatMiniProgramToken";
            //获取公共请求参数
            $requestData = $this->getPublicRequestParams($deviceId);
            //设置私有参数
            $requestData['params'] = [
                "token" => AccessToken::where("type", AccessToken::TYPE_LC_LIVE)
                    ->value('access_token'),
                "deviceId" => $deviceId,
                "channelId" => 0,
            ];
            $data = CurlUtil::postJson(
                $url,
                $requestData,
                ['Content-Type: application/x-www-form-urlencoded']
            );
            $data = json_decode($data, true);
            $minToken = "";
            if ($data['result']['code'] == "0") {
                $minToken = $data['result']['data']['miniToken'];
                Cache::store("redis")->set(
                    "min_token_".$deviceId,
                    $minToken,
                    60
                );
            }
        }
        return $minToken;
    }

    public function removeDeviceWatermark()
    {
        $deviceIds = [
            "9B0851EPBVAA7EE", "9B0851EPBV67890", "9B0851EPBVFCEC6",
            "9B0851EPBVCDC85", "9F0358DPBV0FA2D", "9B0851EPBV38C07",
            "9B0851EPBV3BC7D", "9B0851EPBV20F86", "9B0851EPBV3E63C",
            "9E01CCFPBV52132", "9B0851EPBV7682D", "9B0851EPBV3F11D",
            "9B0851EPBV55A2F", "9B0851EPBV7BE24", "9B0851EPBV4BF4C",
            "9B0851EPBV8C6D8", "9B0851EPBVE573A", "9B0851EPBV83095",
            "9B0851EPBVF495F", "9B0851EPBV0F827"
        ];
        foreach ($deviceIds as $key => $deviceId) {
            $this->publicRequestMethod('setDeviceOsd', [
                "token" => $this->getAaccessToken(),
                "deviceId" => $deviceId,
                "channelId" => "0",
                "enable" => "on",
                "osd" => " "
            ], $key);
        }
    }
}
