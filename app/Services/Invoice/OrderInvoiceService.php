<?php

namespace App\Services\Invoice;

use App\Models\OrderInfo;
use App\Repositories\NanometerOrder\NanometerOrderRepository;
use App\Repositories\Order\OrderAllotRepository;
use App\Repositories\Order\OrderRepository;
use Illuminate\Support\Facades\DB;
use Yanqu\YanquPhplib\Openapi\Order\Constants\CategoryConstants;

class OrderInvoiceService
{
    /**
     * @param array $params
     * @return array<array{dProductName: string}>
     */
    public function getInvoiceItemsByOrderIds(array $params): array
    {
        $orderIds = $params['order_ids'] ?? [];
        $orders = app(OrderRepository::class)->getOrdersByOids($orderIds)->keyBy('oid');
        if ($orders->isEmpty()) {
            return [];
        }

        $orderAllots = app(OrderAllotRepository::class)->getProductInfos($orderIds)->keyBy('oid');
        $nanometerOrders = app(NanometerOrderRepository::class)->getNanometerOrderByOrderIds($orderIds)
            ->keyBy('oid');

        $nanometerOrderIds = $nanometerOrders->pluck('noid')->toArray();
        $nanoOrderPrices = DB::table('nanometer_order_price')
            ->where('isvoid', 0)
            ->whereIn('noid', $nanometerOrderIds)
            ->select("noid", "skuid")->get();

        $skuIds = $nanoOrderPrices->pluck('skuid')->toArray();
        $skuSaleNames = DB::table("nanometer_product_skusale")
            ->whereIn("skuid", $skuIds)->select('skuid', 'name')->get()->keyBy('skuid');

        $invoiceItems = [];
        foreach ($orderIds as $orderId) {
            /** @var OrderInfo $order */
            $order = $orders->get($orderId);
            if (empty($order)) {
                $invoiceItems[] = ['dProductName' => ''];
                continue;
            }
            $orderAllot = $orderAllots->get($orderId);
            switch ($order->categoryid) {
                case CategoryConstants::NANO_MALL:
                    $currentInvoiceItems = $this->getNanometerInvoiceItems($orderId, $nanometerOrders, $nanoOrderPrices,
                        $skuSaleNames);
                    $invoiceItems = array_merge($invoiceItems, $currentInvoiceItems);
                    break;
                case CategoryConstants::ONLINE_COURSES:
                    $sendProduct = $orderAllot->sendProduct ?? null;
                    $dProductName = $sendProduct ? $sendProduct->pname : '';
                    $dProductName = str_replace('在线', '', $dProductName);
                    $invoiceItems[] = ['dProductName' => $dProductName];
                    break;
                default :
                    if (!empty($order->productname)) {
                        $invoiceItems[] = ['dProductName' => $order->productname];
                    }
                    break;
            }
        }
        return $invoiceItems;
    }

    private function getNanometerInvoiceItems(int $orderId, $nanometerOrders, $nanoOrderPrices, $skuSaleNames): array
    {
        $invoiceItems = [];
        $nanometerOrder = $nanometerOrders->get($orderId);
        if (!$nanometerOrder) {
            return $invoiceItems;
        }

        $matchedPrices = $nanoOrderPrices->where('noid', $nanometerOrder->noid);
        foreach ($matchedPrices as $price) {
            $skuName = $skuSaleNames->get($price->skuid)->name ?? null;
            if ($skuName) {
                $invoiceItems[] = ['dProductName' => $skuName];
            }
        }
        return $invoiceItems;
    }
}