<?php

namespace App\Services;

use App\Entities\PickupEntity;
use App\Models\OrderInfo;
use Illuminate\Database\Eloquent\Collection;

interface OrderService
{
    function getPickup(int $oid, int $providerproductid): PickupEntity;

    function getPmQrcode($account,$addressId,$buffetId,$providerProductIds,$categoryId,$smallCategoryIds,$detailVersion);

    function checkOrderCanCancel($oid,$accountId);

    function cancelOrderData($oid,$accountId,$userIp,$reasons,$remark,$operateType,$operatePlatform);

    function saveQuestionnaireData($oid,$accountId,$userIp,$reasons,$remark,$operateType,$operatePlatform);

    /**
     * @param array $orderIds
     * @return Collection|OrderInfo[]
     */
    function getOrdersTicketInfo(array $orderIds);
}
