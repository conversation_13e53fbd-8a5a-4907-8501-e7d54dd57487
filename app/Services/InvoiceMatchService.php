<?php

namespace App\Services;

use App\Constants\Constant;
use App\Constants\Invoice\NuonuoInvoiceMatchConstant;
use App\Constants\Invoice\OnlineInvoiceConstant;
use App\Constants\InvoiceConstants;
use App\Constants\NuoNuoConstants;
use App\Entities\NuoNuo\MatchErrorMessagePojo;
use App\Exceptions\NuoNuoException;
use App\Models\AccountCouponTicket;
use App\Models\FinanceProvider;
use App\Models\FinanceTicketApply;
use App\Models\NuonuoInvoiceDailyInfo;
use App\Models\OnlineInvoice;
use App\Models\SystemNuonuoInvoiceError;
use App\Utils\CommonUtil;
use App\Utils\WeComRobot;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Yanqu\YanquPhplib\YqLog\YqLog;


class InvoiceMatchService
{
    /**
     * 每日发票对比
     * @param string $batch 批次
     * @throws \Exception
     */
    public function dailyMatch($batch)
    {
        list($batchStartTime, $batchEndTime) = [strtotime($batch . ' 00:00:00'),
            strtotime($batch . " 23:59:59")];
        //判断数电主体同步进度是否大于当前对比的天数
        $flag = $this->checkProviderSynProgress($batch);
        if ($flag) {
            DB::beginTransaction();
            try {
                //清洗当天的蓝票数据
                $this->processDebitVote($batch);
                //订单发票数据对比
                $this->diffFinanceTicket($batch, $batchStartTime, $batchEndTime);
                //获取预存发票数据
                $this->diffCouponTicket($batch, $batchStartTime, $batchEndTime);
                //获取红票数据
                $this->diffRedInvoice($batch);
                //获取未被匹配数据
                $this->checkUnMatchedNuoNuoInvoice($batch);
                //诺诺开票中的发票推送到错误信息中
                $this->addInvoicingToErrorMessage($batch);
                DB::commit();
            } catch (\Exception $exception) {
                DB::rollBack();
                $errorMsg = "对比{$batch} 发票数据发生错误 {$exception->getTraceAsString()}";
                app(WeComRobot::class)->sendTextMessage(
                    $errorMsg,
                    config('invoice.weComRobotInvoiceWebhook'),
                    config('invoice.nuonuoListAnomalyNotifyUserId'));
                YqLog::logger('finance:invoice')->info($errorMsg);
                throw $exception;
            }
            //更新主体对比日期
            $this->updateProviderMatchProgress($batch);

        } else {
            //通知同步没有运行
            $this->notifyMatchNotStart($batch);
        }

    }

    /**
     *  订单发票数据对比
     * @param $batch
     * @param $batchStartTime
     * @param $batchEndTime
     * @return void
     */
    public function diffFinanceTicket($batch, $batchStartTime, $batchEndTime)
    {
        $providerNameMap = $this->getProviderNameMap();
        FinanceTicketApply::query()
            ->whereBetween('invoicetime', [$batchStartTime, $batchEndTime])
            ->where('isvoid', 0)
            ->where('parentickid','>','-1')
            ->where('mergeinvoice',0)
            ->with(['financeTicketInfo:ticketinfoid,title,ticketype'])
            ->select(['applyid', 'applyamount', 'ticketinfoid', 'providerid', 'invoiceno', 'noneedopen', 'invoicetime'])
            ->chunk(500, function ($applies) use ($batch, $providerNameMap) {
                /** @var FinanceTicketApply[]|Collection $applies */
                $invoiceErrorCollection = collect();
                $invoiceNos = $applies->pluck('invoiceno')->all();
                $nuonuoInvoiceInvoiceMap = $this->getNuoNuoInvoiceMapByInvoiceNos($invoiceNos);
                foreach ($applies as $apply) {
                    if ($apply->noneedopen > 0 || $apply->financeTicketInfo->ticketype == InvoiceConstants::INVOICE) {
                        //收据不做校验
                        continue;
                    }
                    if (!$nuonuoInvoiceInvoiceMap->has($apply->invoiceno)) {
                        //不存在 记录内容
                        $invoiceErrorCollection->push([
                            'system_invoice_type' => NuonuoInvoiceMatchConstant::SYSTEM_INVOICE_TYPE_ORDER,
                            'system_invoice_id' => $apply->applyid,
                            'error_type' => NuonuoInvoiceMatchConstant::ERROR_TYPE_NUONUO_LOSE,
                            'error_description' => "诺诺数据缺失 发票号：{$apply->invoiceno}",
                            'invoice_time' => strtotime($batch),
                            'provider_id' => $apply->providerid,
                            'invoice_no' => empty($apply->invoiceno) ? '' : $apply->invoiceno
                        ]);
                    } else {
                        $nuonuoInvoice = $nuonuoInvoiceInvoiceMap[$apply->invoiceno];
                        $errorDescription = $this->diffFianceTicketParam($apply, $nuonuoInvoice, $providerNameMap);
                        if (!empty($errorDescription)) {
                            $invoiceErrorCollection->push([
                                'system_invoice_type' => NuonuoInvoiceMatchConstant::SYSTEM_INVOICE_TYPE_ORDER,
                                'system_invoice_id' => $apply->applyid,
                                'error_type' => NuonuoInvoiceMatchConstant::ERROR_TYPE_HAS_DIFF,
                                'error_description' => $errorDescription,
                                'invoice_time' => strtotime($batch),
                                'provider_id' => $apply->providerid,
                                'invoice_no' => empty($apply->invoiceno) ? '' : $apply->invoiceno
                            ]);
                        }
                        $nuonuoInvoice->matchInvoice(
                            NuonuoInvoiceMatchConstant::SYSTEM_INVOICE_TYPE_ORDER, $apply->applyid
                        );
                    }
                }
                if ($invoiceErrorCollection->isNotEmpty()) {
                    SystemNuonuoInvoiceError::query()->insert($invoiceErrorCollection->toArray());
                }
            });
    }

    /**
     *  发票字段对比 并返回差异信息
     * @param FinanceTicketApply $apply
     * @param NuonuoInvoiceDailyInfo $nuonuoInvoice
     * @param $providerNameMap
     * @return string
     */
    public function diffFianceTicketParam(FinanceTicketApply $apply, NuonuoInvoiceDailyInfo $nuonuoInvoice,
                                                             $providerNameMap): string
    {
        $res = '';
        if (trim($nuonuoInvoice->payer_name) != trim($apply->financeTicketInfo->title)) {
            $res .= "\n抬头不同-诺诺：{$nuonuoInvoice->payer_name} ;" .
                " 系统：{$apply->financeTicketInfo->title}";
        }
        $nuonuoAmount = bcadd($nuonuoInvoice->ex_tax_amount, $nuonuoInvoice->tax_amount, 2);
        if (bccomp($nuonuoAmount, $apply->applyamount) != 0) {
            $res .= "\n金额不同-诺诺：{$nuonuoInvoice->ex_tax_amount} + {$nuonuoInvoice->tax_amount} = {$nuonuoAmount} ;"
                . " 系统：{$apply->applyamount}";
        }
        if ($nuonuoInvoice->provider_id != $apply->providerid) {
            $nuonuoProviderName = $providerNameMap[$nuonuoInvoice->provider_id];
            $systemProviderName = $providerNameMap[$apply->providerid];
            $res .= "\n主体不同-诺诺：{$nuonuoProviderName} ;" .
                " 系统主体：{$systemProviderName}";
        }
        if ($nuonuoInvoice->is_debit) {
            $res .= "\n诺诺显示该发票已红冲";
        }

        $nuonuoDate = Carbon::parse($nuonuoInvoice->invoice_time)->toDateString();
        $financeInvoiceDate = Carbon::createFromTimestamp($apply->invoicetime)->toDateString();
        if ($nuonuoDate != $financeInvoiceDate) {
            $res .= "\n开票时间不同-诺诺：{$nuonuoDate} ; 系统：{$financeInvoiceDate}";
        }
        return $res;
    }

    /**
     *  通过发票号获取匹配的诺诺发票
     * @param $invoiceNos
     * @return NuonuoInvoiceDailyInfo[]|\Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public function getNuoNuoInvoiceMapByInvoiceNos($invoiceNos)
    {
        $nuonuoInvoice = NuonuoInvoiceDailyInfo::query()
            ->whereIn('invoice_no', $invoiceNos)
            ->where('isvoid', 0)
            ->where('is_matched', 0)
            ->whereIn('status', NuonuoInvoiceMatchConstant::INVOICE_INSPECTION_STATUS_ARRAY)
            ->get(['id', 'provider_id', 'status', 'ex_tax_amount', 'tax_amount', 'invoice_time', 'invoice_no'
                , 'is_matched', 'invoice_type', 'system_invoice_id', 'system_invoice_type', 'payer_name', 'is_debit']);
        return $nuonuoInvoice->keyBy('invoice_no');
    }

    public function getNuoNuoSerialNoMapBySerialNo($serialNos)
    {
        $nuonuoInvoice = NuonuoInvoiceDailyInfo::query()
            ->whereIn('serial_no', $serialNos)
            ->where('isvoid', 0)
            ->where('is_matched', 0)
            ->whereIn('status', NuonuoInvoiceMatchConstant::INVOICE_INSPECTION_STATUS_ARRAY)
            ->get(['id', 'provider_id', 'status', 'ex_tax_amount', 'tax_amount', 'invoice_time', 'invoice_no',
                'serial_no', 'is_matched', 'invoice_type', 'system_invoice_id', 'system_invoice_type', 'payer_name',
                'is_debit']);
        return $nuonuoInvoice->keyBy('serial_no');
    }

    /**
     *  匹配对应的预存发票
     * @param $batch
     * @param $batchStartTime
     * @param $batchEndTime
     * @return void
     */
    public function diffCouponTicket($batch, $batchStartTime, $batchEndTime)
    {
        $providerNameMap = $this->getProviderNameMap();
        AccountCouponTicket::query()->whereBetween('invoicetime', [$batchStartTime, $batchEndTime])
            ->where('isvoid', 0)
            ->select(['amount', 'invoiceno', 'providerid', 'cticketid','title', 'ticketype', 'noneedopen',
                'invoicetime'])
            ->chunk(500, function ($tickets) use ($batch, $providerNameMap) {
                /** @var AccountCouponTicket[]|Collection $tickets */
                $invoiceErrorCollection = collect();
                $invoices = $tickets->pluck('invoiceno')->all();
                $nuonuoInvoiceInvoiceMap = $this->getNuoNuoInvoiceMapByInvoiceNos($invoices);
                foreach ($tickets as $ticket) {
                    if ($ticket->noneedopen > 0 || $ticket->ticketype == InvoiceConstants::INVOICE) {
                        //收据不做校验
                        continue;
                    }
                    if (!$nuonuoInvoiceInvoiceMap->has($ticket->invoiceno)) {
                        //不存在 记录内容
                        $invoiceErrorCollection->push([
                            'system_invoice_type' => NuonuoInvoiceMatchConstant::SYSTEM_INVOICE_TYPE_COUPON,
                            'system_invoice_id' => $ticket->cticketid,
                            'error_type' => NuonuoInvoiceMatchConstant::ERROR_TYPE_NUONUO_LOSE,
                            'error_description' => "诺诺数据缺失 发票号：{$ticket->invoiceno}",
                            'invoice_time' => strtotime($batch),
                            'provider_id' => $ticket->providerid,
                            'invoice_no' => empty($ticket->invoiceno) ? '' : $ticket->invoiceno,
                        ]);
                    } else {
                        $nuonuoInvoice = $nuonuoInvoiceInvoiceMap[$ticket->invoiceno];
                        $errorDescription = $this->diffCouponTicketParam($ticket, $nuonuoInvoice, $providerNameMap);
                        if (!empty($errorDescription)) {
                            $invoiceErrorCollection->push([
                                'system_invoice_type' => NuonuoInvoiceMatchConstant::SYSTEM_INVOICE_TYPE_COUPON,
                                'system_invoice_id' => $ticket->cticketid,
                                'error_type' => NuonuoInvoiceMatchConstant::ERROR_TYPE_HAS_DIFF,
                                'error_description' => $errorDescription,
                                'invoice_time' => strtotime($batch),
                                'provider_id' => $ticket->providerid,
                                'invoice_no' => empty($ticket->invoiceno) ? '' : $ticket->invoiceno,
                            ]);
                        }
                        $nuonuoInvoice->matchInvoice(
                            NuonuoInvoiceMatchConstant::SYSTEM_INVOICE_TYPE_COUPON, $ticket->cticketid
                        );
                    }
                }
                if ($invoiceErrorCollection->isNotEmpty()) {
                    SystemNuonuoInvoiceError::query()->insert($invoiceErrorCollection->toArray());
                }
            });
    }

    /**
     *  对比预存发票与诺诺字段差异 并返回差异信息
     * @param AccountCouponTicket $ticket
     * @param NuonuoInvoiceDailyInfo $nuonuoInvoice
     * @param $providerNameMap
     * @return string
     */
    public function diffCouponTicketParam(AccountCouponTicket $ticket, NuonuoInvoiceDailyInfo $nuonuoInvoice,
                                                              $providerNameMap): string
    {
        $res = '';
        if (trim($nuonuoInvoice->payer_name) != trim($ticket->title)) {
            $res .= "\n抬头不同-诺诺：{$nuonuoInvoice->payer_name} ;" .
                " 系统抬头：{$ticket->title}";
        }
        $nuonuoAmount = bcadd($nuonuoInvoice->ex_tax_amount, $nuonuoInvoice->tax_amount, 2);
        if (bccomp($nuonuoAmount, $ticket->amount) != 0) {
            $res .= "\n金额不同-诺诺：{$nuonuoInvoice->ex_tax_amount} + {$nuonuoInvoice->tax_amount} = {$nuonuoAmount} ;"
                . " 系统金额：{$ticket->amount}";
        }
        if ($nuonuoInvoice->provider_id != $ticket->providerid) {
            $nuonuoProviderName = $providerNameMap[$nuonuoInvoice->provider_id];
            $systemProviderName = $providerNameMap[$ticket->providerid];
            $res .= "\n主体不同-诺诺：{$nuonuoProviderName}" .
                " 系统主体：{$systemProviderName}";
        }
        if ($nuonuoInvoice->is_debit) {
            $res .= "\n诺诺显示该发票已红冲";
        }

        $nuonuoDate = Carbon::parse($nuonuoInvoice->invoice_time)->toDateString();
        $financeInvoiceDate = Carbon::createFromTimestamp($ticket->invoicetime)->toDateString();
        if ($nuonuoDate != $financeInvoiceDate) {
            $res .= "\n开票时间不同-诺诺：{$nuonuoDate} ; 系统：{$financeInvoiceDate}";
        }
        return $res;
    }

    /**
     * 对比诺诺发票与红票信息
     * @param $batch
     * @return void
     */
    public function diffRedInvoice($batch)
    {
        $batchStartTime = $batch . " 00:00:00";
        $batchEndTime = $batch . " 23:59:59";

        $providerTaxNumMap = $this->getProviderTaxNumMap();
        OnlineInvoice::query()->whereBetween('order_date', [$batchStartTime, $batchEndTime])
            ->where('is_void', 0)
            ->where('invoice_type', 2)->select(['id', 'invoice_no', 'rep_invoice_num', 'buyer_name'
                , 'type', 'applyid', 'cticketid', 'rep_invoice_num', 'saler_tax_num', 'invoice_serial_num','status'])
            ->chunk(500, function ($onlineInvoices) use ($batch, $providerTaxNumMap) {
                /** @var OnlineInvoice[]|Collection $onlineInvoices */
                $invoiceErrorCollection = collect();
                $invoiceNos = $onlineInvoices->pluck('invoice_no')->all();
                $nuonuoInvoiceMap = $this->getNuoNuoInvoiceMapByInvoiceNos($invoiceNos);
                $serialNums = $onlineInvoices->pluck('invoice_serial_num')->all();
                $nuonuoInvoiceSerialMap = $this->getNuoNuoSerialNoMapBySerialNo($serialNums);
                foreach ($onlineInvoices as $onlineInvoice) {
                    if (!$nuonuoInvoiceMap->has($onlineInvoice->invoice_no)
                        && !$nuonuoInvoiceSerialMap->has($onlineInvoice->invoice_serial_num)) {
                        if ($this->shouldRecordRedInvoiceError($onlineInvoice)) {
                            $invoiceErrorCollection->push([
                                'system_invoice_type' => NuonuoInvoiceMatchConstant::SYSTEM_INVOICE_TYPE_RED,
                                'system_invoice_id' => $onlineInvoice->id,
                                'error_type' => NuonuoInvoiceMatchConstant::ERROR_TYPE_NUONUO_LOSE,
                                'error_description' => "诺诺数据缺失 发票号：{$onlineInvoice->invoice_no} "
                                    . "流水号：{$onlineInvoice->invoice_serial_num}",
                                'invoice_time' => strtotime($batch),
                                'provider_id' => $providerTaxNumMap[$onlineInvoice->saler_tax_num] ?: 0,
                                'invoice_no' => empty($onlineInvoice->invoice_no) ? '' : $onlineInvoice->invoice_no,
                            ]);
                        }
                    } else {
                        if ($nuonuoInvoiceMap->has($onlineInvoice->invoice_no)) {
                            $nuonuoInvoice = $nuonuoInvoiceMap[$onlineInvoice['invoice_no']];
                        } else {
                            $nuonuoInvoice = $nuonuoInvoiceSerialMap[$onlineInvoice['invoice_serial_num']];
                        }
                        $errorDescription = $this->diffRedInvoiceParam($onlineInvoice, $nuonuoInvoice);
                        if (!empty($errorDescription)) {
                            $invoiceErrorCollection->push([
                                'system_invoice_type' => NuonuoInvoiceMatchConstant::SYSTEM_INVOICE_TYPE_RED,
                                'system_invoice_id' => $onlineInvoice->id,
                                'error_type' => NuonuoInvoiceMatchConstant::ERROR_TYPE_HAS_DIFF,
                                'error_description' => $errorDescription,
                                'invoice_time' => strtotime($batch),
                                'provider_id' => $providerTaxNumMap[$onlineInvoice->saler_tax_num] ?? 0,
                                'invoice_no' => empty($onlineInvoice->invoice_no) ? '' : $onlineInvoice->invoice_no
                            ]);
                        }
                        $nuonuoInvoice->matchInvoice(
                            NuonuoInvoiceMatchConstant::SYSTEM_INVOICE_TYPE_RED, $onlineInvoice->id
                        );
                    }
                }
                if ($invoiceErrorCollection->isNotEmpty()) {
                    SystemNuonuoInvoiceError::query()->insert($invoiceErrorCollection->toArray());
                }
            });
    }

    /**
     * 对比诺诺发票与红票字段差异
     * @param OnlineInvoice $onlineInvoice
     * @param NuonuoInvoiceDailyInfo $dailyInfo
     * @return string
     */
    public function diffRedInvoiceParam(OnlineInvoice $onlineInvoice, NuonuoInvoiceDailyInfo $dailyInfo)
    {
        $res = '';
        //红票没有太多系统保存字段 所以只验证抬头
        if ($onlineInvoice->buyer_name != $dailyInfo->payer_name) {
            $res .= "抬头不匹配 诺诺：{$dailyInfo->payer_name}" .
                " 系统：{$onlineInvoice->buyer_name}";
        }
        return $res;
    }

    /**
     * 诺诺剩余未匹配的数据对比
     * @return void
     */
    public function checkUnMatchedNuoNuoInvoice($batch)
    {
        $this->checkUnmatchedNuoNuoBlueInvoice($batch);
        $this->checkUnmatchedNuoNuoRedInvoice($batch);
    }

    /**
     * 获取并处理红票数据 给诺诺表上已红冲的蓝票打标
     * @param string $batch 批次 当不传时则给全部历史数据打标
     * @return void
     */
    public function processDebitVote($batch = '')
    {
        $query = NuonuoInvoiceDailyInfo::query()
            ->where('invoice_type', InvoiceConstants::RED_MARK)
            ->where('status', NuonuoInvoiceMatchConstant::INVOICE_STATUS_OK)
            ->where('isvoid', 0)
            ->select(['id', 'serial_no', 'seller_tax_no', 'provider_id']);

        if (!empty($batch)) {
            $batchStartTime = $batch . " 00:00:00";
            $batchEndTime = $batch . " 23:59:59";
            $query->whereBetween('invoice_time', [$batchStartTime, $batchEndTime]);
        }

        $query->chunk(500, function ($nuonuoInvoiceDailyInfos) {
            /** @var NuonuoInvoiceDailyInfo[]|Collection $nuonuoInvoiceDailyInfos */
            $this->dealDebitNuonuoInvoiceInfosWithNuoNuoResult($nuonuoInvoiceDailyInfos);
        });
    }

    /**
     * 对比处理诺诺未被匹配的蓝票数据
     * @param string $batch
     * @param array $providerTaxNumMap
     * @return void
     */
    public function checkUnmatchedNuoNuoBlueInvoice($batch)
    {
        $batchStartTime = $batch . " 00:00:00";
        $batchEndTime = $batch . " 23:59:59";
        $nuonuoInvoiceDailyInfos = NuonuoInvoiceDailyInfo::query()
            ->whereBetween('invoice_time', [$batchStartTime, $batchEndTime])
            ->where('isvoid', 0)->where('is_matched', 0)
            ->where('invoice_type', InvoiceConstants::BLUE_MARK)
            ->where('is_debit', 0)
            ->select(['id', 'invoice_no', 'is_matched', 'seller_tax_no',
                'serial_no', 'provider_id', 'order_no'])
            ->chunk(500, function ($nuonuoInvoiceDailyInfos) use ($batch) {
                /** @var NuonuoInvoiceDailyInfo[]|Collection $nuonuoInvoiceDailyInfos */
                $unmatchedError = collect();
                /** @var OnlineInvoiceService $onlineInvoiceService */
                $onlineInvoiceService = app(OnlineInvoiceService::class);
                foreach ($nuonuoInvoiceDailyInfos as $dailyInfo) {
                    $orderInfo = $onlineInvoiceService->decodeInvoiceOrderNo($dailyInfo->order_no);
                    //无红冲记录
                    $unmatchedError->push([
                        'nuonuo_invoice_daily_info_id' => $dailyInfo->id,
                        'error_type' => NuonuoInvoiceMatchConstant::ERROR_TYPE_SYSTEM_LOSE,
                        'error_description' => "系统无对应蓝票，且蓝票无对应红冲记录 订单编号{$dailyInfo->order_no} " .
                            "倒推系统类型 {$orderInfo['typeDesc']} 对应id {$orderInfo['id']}",
                        'invoice_time' => strtotime($batch),
                        'provider_id' => $dailyInfo->provider_id,
                        'invoice_no' => $dailyInfo->invoice_no,
                    ]);
                }
                if ($unmatchedError->isNotEmpty()) {
                    SystemNuonuoInvoiceError::query()->insert($unmatchedError->toArray());
                }
            });
    }

    /**
     * 对比处理红票未匹配数据
     * @param $batch
     * @return void
     */
    public function checkUnmatchedNuoNuoRedInvoice($batch)
    {
        $batchStartTime = $batch . " 00:00:00";
        $batchEndTime = $batch . " 23:59:59";
        NuonuoInvoiceDailyInfo::query()->whereBetween('invoice_time', [$batchStartTime, $batchEndTime])
            ->where('isvoid', 0)->where('is_matched', 0)
            ->whereIn('status', NuonuoInvoiceMatchConstant::INVOICE_INSPECTION_STATUS_ARRAY)
            ->where('invoice_type', 2)
            ->select(['id', 'invoice_no', 'is_matched', 'system_invoice_type', 'system_invoice_id', 'seller_tax_no',
                'provider_id', 'serial_no', 'order_no'])
            ->chunk(500, function ($nuonuoInvoiceDailyInfos) use ($batch) {
                /** @var NuonuoInvoiceDailyInfo[]|Collection $nuonuoInvoiceDailyInfos */
                $unmatchedRedInvoiceCollection = collect();
                /** @var OnlineInvoiceService $onlineInvoiceService */
                $onlineInvoiceService = app(OnlineInvoiceService::class);
                $errorRedInvoices = $this->dealUnMatchRedInvoice($nuonuoInvoiceDailyInfos);
                $nuonuoInvoiceDailyInfosMap = $nuonuoInvoiceDailyInfos->keyBy('invoice_no');
                foreach ($errorRedInvoices as $errorRedInvoice) {
                    $decodeInfo = $onlineInvoiceService
                        ->decodeInvoiceOrderNo($nuonuoInvoiceDailyInfosMap[$errorRedInvoice]->order_no);
                    $unmatchedRedInvoiceCollection->push([
                        'nuonuo_invoice_daily_info_id' => $nuonuoInvoiceDailyInfosMap[$errorRedInvoice]->id,
                        'error_type' => NuonuoInvoiceMatchConstant::ERROR_TYPE_SYSTEM_LOSE,
                        'error_description' => "系统无对应红票记录且发票对应蓝票系统未红冲 倒推系统类型 {$decodeInfo['typeDesc']}"
                        ." 对应id {$decodeInfo['id']}",
                        'invoice_time' => strtotime($batch),
                        'provider_id' => $nuonuoInvoiceDailyInfosMap[$errorRedInvoice]->provider_id,
                        'invoice_no' => $nuonuoInvoiceDailyInfosMap[$errorRedInvoice]->invoice_no,
                    ]);
                }
                if ($unmatchedRedInvoiceCollection->isNotEmpty()) {
                    SystemNuonuoInvoiceError::query()->insert($unmatchedRedInvoiceCollection->toArray());
                }
            });
    }

    /**
     * 获取主体id key税号的map
     * @return array<string,string>
     */
    public function getProviderTaxNumMap(): array
    {
        return FinanceProvider::query()->pluck('providerid', 'taxnumber')->all();
    }

    /**
     * 获取主体名称map key id
     * @return array<string,int>
     */
    public function getProviderNameMap(): array
    {
        return FinanceProvider::query()->pluck('companyname', 'providerid')->all();
    }

    /**
     * 整合发送对应批次的错误信息
     * @param $batch
     * @return void
     */
    public function sendErrorMessage($batch)
    {
        $providerNameMap = $this->getProviderNameMap();
        $errors = SystemNuonuoInvoiceError::query()->where('isvoid', 0)
            ->where('invoice_time', strtotime($batch))
            ->get(['error_type', 'error_description', 'provider_id', 'invoice_no']);
        $errorsTypeMap = $errors->groupBy('error_type');
        $matchErrorMessagePojo = new MatchErrorMessagePojo();
        $matchErrorMessagePojo->setMessage($batch." 发票对比\n");
        $keyNumber = 1;
        if ($errors->isEmpty()) {
            $matchErrorMessagePojo->addMessage("当日对比无异常数据");
        } else {
            $errorsTypeMap = $errorsTypeMap->sortByDesc(function ($group, $key) {
                return $key;
            });
            foreach ($errorsTypeMap as $key => $errorList) {
                /** @var SystemNuonuoInvoiceError[]|Collection $errorList */
                $errorDes = NuonuoInvoiceMatchConstant::getErrorTypeDesc($key);
                $errorCount = $errorList->count();
                $keyWord = CommonUtil::number_to_words($keyNumber);
                $keyNumber++;
                $matchErrorMessagePojo->addMessage("{$keyWord}、{$errorDes}{$errorCount}张\n");
                $numberMark = 1;
                foreach ($errorList as $error) {
                    $providerName = $providerNameMap[$error->provider_id] ?? "";
                    $tempMessage = "{$numberMark}. {$error->invoice_no} | {$providerName} ";
                    if ($key == NuonuoInvoiceMatchConstant::ERROR_TYPE_HAS_DIFF
                        || $key == NuonuoInvoiceMatchConstant::ERROR_TYPE_SYSTEM_LOSE) {
                        $tempMessage .= " $error->error_description \n";
                    } else {
                        $tempMessage .= "\n";
                    }
                    $matchErrorMessagePojo->addMessage($tempMessage);
                    $numberMark++;
                }
            }
        }
        $matchErrorMessagePojo->integratedMessagesAndSend();
    }

    /**
     * 判断主要的主体同步进度是否完成
     * @param $batch
     * @return bool
     */
    public function checkProviderSynProgress($batch)
    {
        $res = true;
        $financeProviders = FinanceProvider::query()
            ->whereIn('providerid', InvoiceConstants::SYN_MAIN_PROVIDER_ID)
            ->get(['providerid', 'nuonuo_syn_date', 'nuonuo_match_date']);
        $minSynDate = $financeProviders->pluck('nuonuo_syn_date')->min();
        $minSynDate = Carbon::parse($minSynDate);
        $batch = Carbon::parse($batch);
        if (!$minSynDate->greaterThanOrEqualTo($batch)) {
            $res = false;
        }
        $minMatchDate = $financeProviders->pluck('nuonuo_match_date')->min();
        $minMatchDate = Carbon::parse($minMatchDate);
        if (!$minMatchDate->eq($batch->subDay())) {
            $res = false;
        }

        return $res;
    }

    /**
     * 给在诺诺上显示已红冲的发票打标
     * @param string $batch
     * @return void
     */
    public function updateProviderMatchProgress($batch)
    {
        FinanceProvider::query()->whereIn('providerid',InvoiceConstants::SYN_MAIN_PROVIDER_ID)
            ->update([
                'nuonuo_match_date' => $batch
            ]);
    }

    /**
     * 通知匹配未成功开始
     * @param $batch
     * @return void
     */
    public function notifyMatchNotStart($batch)
    {
        app(WeComRobot::class)
            ->sendTextMessage("{$batch} 数据对比未成功运行，请检查对应主体的发票同步时间以及对比时间",
                config('invoice.weComRobotInvoiceWebhook'),
                config('invoice.nuonuoListAnomalyNotifyUserId'));
    }

    /**
     * 处理整合诺诺queryResult批量获取的红票数据信息,并给数据打标
     * @var list<array{
     *     invoiceNo:string,
     *    allElectronicInvoiceNumbe:string,
     *     oldInvoiceNo:string,
     *     oldEleInvoiceNumber:string,
     *     remark:string,
     *     salerTaxNum:string
     * }> $results
     * @param int $providerId 主体id
     * @return void
     */
    public function dealNuonuoRedInvoiceResult($results, $providerId)
    {
        $taxInvoiceNumberList = collect();
        foreach ($results as $result) {
            $temp = $this->getOldInvoiceNo($result);
            if (!empty($temp)) {
                $taxInvoiceNumberList->push($temp);
            }
        }
        if ($taxInvoiceNumberList->isNotEmpty()) {
            $this->updateNuoNuoInvoiceDailyInfoIsDebit($taxInvoiceNumberList->all(), $providerId);
        }
    }

    /**
     * 获取获取诺诺返回数据中对应红冲的蓝票发票号
     * @return string
     * @var array{
     *     invoiceNo:string,
     *    allElectronicInvoiceNumbe:string,
     *     oldInvoiceNo:string,
     *     oldEleInvoiceNumber:string,
     *     remark:string,
     *     salerTaxNum:string
     * } $results
     */
    public function getOldInvoiceNo($result)
    {
        $temp = '';
        if (!empty($result['oldInvoiceNo'])) {
            $temp = $result['oldInvoiceNo'];
        } elseif (!empty($result['oldEleInvoiceNumber'])) {
            $temp = $result['oldEleInvoiceNumber'];
        } elseif (!empty($result['remark'])) {
            $pattern = '/[:：](\d+)/u';
            if (preg_match($pattern, $result['remark'], $matches)) {
                $temp = $matches[1];
            }
        }

        return $temp;
    }

    /**
     * 给对应的诺诺蓝票打上已红冲标记
     * @param $invoiceNos
     * @param $providerId
     * @return void
     */
    public function updateNuoNuoInvoiceDailyInfoIsDebit($invoiceNos, $providerId)
    {
        NuonuoInvoiceDailyInfo::query()->whereIn('invoice_no', $invoiceNos)
            ->where('provider_id', $providerId)
            ->where('invoice_type', 1)
            ->update([
                'is_debit' => 1
            ]);
    }

    /**
     * 获取
     * @param NuonuoInvoiceDailyInfo[]|Collection $nuonuoInvoiceDailyInfos
     * @return void
     */
    public function dealDebitNuonuoInvoiceInfosWithNuoNuoResult($nuonuoInvoiceDailyInfos)
    {
        /** @var InvoiceService $invoiceService */
        $invoiceService = app(InvoiceService::class);
        $financeInfo = $invoiceService->getFinanceInfos();
        $tokens = $invoiceService->getAppTokens($financeInfo);
        $providerIdMapByTaxNum = $this->getProviderTaxNumMap();
        /** @var NnfpService $nnfpService */
        $nnfpService = app(NnfpService::class);
        $nnfpService->setTokenType(NuoNuoConstants::TOKEN_TYPE_FULL_ELECTRIC);

        $serialNosMap = $this->buildSellerTaxNoToSerialNo($nuonuoInvoiceDailyInfos);

        foreach ($serialNosMap as $key => $serialNos) {
            $serialNosChunk = collect($serialNos)->chunk(50)->toArray();
            foreach ($serialNosChunk as $item) {
                try {
                    $results = $nnfpService->getInvoiceInfo($key,$tokens[$key],array_values($item),"1");
                } catch (NuoNuoException $nuoNuoException) {
                    $serialNosList = implode(',',$item);
                    $errorContent = "拉取诺诺发票详情失败 key = {$key} {$nuoNuoException->getMessage()}".
                        "serialList: {$serialNosList}";
                    app(WeComRobot::class)->sendTextMessage(
                        $errorContent,
                        config('invoice.weComRobotInvoiceWebhook'),
                        config('invoice.nuonuoListAnomalyNotifyUserId'));
                    YqLog::logger('finance:invoice')
                        ->info($errorContent);
                    exit();
                }
                $this->dealNuonuoRedInvoiceResult($results['result'], $providerIdMapByTaxNum[$key]);
            }
        }
    }

    /**
     * 处理未匹配的红票信息
     * 通过诺诺查询红票详情、并查找对应红冲的蓝票是否在系统中存在
     * @param $nuonuoInvoiceDailyInfos
     * @return Collection
     */
    public function dealUnMatchRedInvoice($nuonuoInvoiceDailyInfos)
    {
        /** @var NnfpService $nnfpService */
        $nnfpService = app(NnfpService::class);
        $nnfpService->setTokenType(NuoNuoConstants::TOKEN_TYPE_FULL_ELECTRIC);
        /** @var InvoiceService $invoiceService */
        $invoiceService = app(InvoiceService::class);
        $financeInfo = $invoiceService->getFinanceInfos();
        $tokens = $invoiceService->getAppTokens($financeInfo);
        $providerIdMapByTaxNum = $this->getProviderTaxNumMap();
        $errorInfos = collect();

        $serialNosMap = $this->buildSellerTaxNoToSerialNo($nuonuoInvoiceDailyInfos);

        foreach ($serialNosMap as $key => $serialNos) {
            $serialNosChunk = collect($serialNos)->chunk(50)->toArray();
            foreach ($serialNosChunk as $item) {
                try {
                    $results = $nnfpService->getInvoiceInfo($key,$tokens[$key],array_values($item),"1");
                } catch (NuoNuoException $nuoNuoException) {
                    $serialNosLists = implode(',',$item);
                    $errorContent = "拉取诺诺发票详情失败 key = {$key} {$nuoNuoException->getMessage()}".
                        "serialList: {$serialNosLists}";
                    app(WeComRobot::class)->sendTextMessage(
                        $errorContent,
                        config('invoice.weComRobotInvoiceWebhook'),
                        config('invoice.nuonuoListAnomalyNotifyUserId'));
                    YqLog::logger('finance:invoice')
                        ->info($errorContent);
                    exit();
                }
                $errorInfo = $this->dealUnmatchedResultAndSearch($results['result'], $providerIdMapByTaxNum[$key]);
                if (!empty($errorInfo)) {
                    $errorInfos = $errorInfos->merge($errorInfo);
                }
            }
        }

        return $errorInfos;
    }

    /**
     * 将NuonuoInvoiceDailyInfo[] 处理成 ['税号1' => ['红票流水号1','红票流水号2']]的形式
     * @param NuonuoInvoiceDailyInfo[]|Collection $nuonuoInvoiceDailyInfos
     * @return array
     */
    public function buildSellerTaxNoToSerialNo($nuonuoInvoiceDailyInfos)
    {
        return $nuonuoInvoiceDailyInfos->reduce(function ($carry, $item) {
            /** @var NuonuoInvoiceDailyInfo $item */
            if (!isset($carry[$item->seller_tax_no])) {
                $carry[$item->seller_tax_no] = [];
            }
            $carry[$item->seller_tax_no][] = $item->serial_no;
            return $carry;
        }, []);
    }

    /**
     * 处理未匹配的红票查询结果并在系统上查找对应蓝票是否还存在
     * @var list<array{
     *     invoiceNo:string,
     *    allElectronicInvoiceNumber:string,
     *     oldInvoiceNo:string,
     *     oldEleInvoiceNumber:string,
     *     remark:string,
     *     salerTaxNum:string
     * }> $results
     * @return array
     */
    public function dealUnmatchedResultAndSearch($results, $providerId)
    {
        $taxInvoiceNumberList = collect();
        $invoiceBlueToRedMap = collect();
        $errorInvoice = collect();
        /** @var OnlineInvoiceService $onlineService */
        $onlineService = app(OnlineInvoiceService::class);
        foreach ($results as $result) {
            $temp = $this->getOldInvoiceNo($result);
            $invoiceRedNo = empty($result['invoiceNo']) ? $result['allElectronicInvoiceNumber'] : $result['invoiceNo'];
            if (!empty($temp)) {
                $taxInvoiceNumberList->push($temp);
                $invoiceBlueToRedMap[$temp] = $invoiceRedNo;
            }
            $onlineService->updateOrCreateOnlineInvoiceByOrderNo($result, $temp);
        }

        $applyInvoices = FinanceTicketApply::query()->whereIn('invoiceno',$taxInvoiceNumberList->all())
            ->where('isvoid',0)->where('providerid', $providerId)
            ->pluck('invoiceno');
        if ($applyInvoices->isNotEmpty()) {
            foreach ($applyInvoices as $applyInvoice) {
                if ($invoiceBlueToRedMap->has($applyInvoice)) {
                    $errorInvoice->push($invoiceBlueToRedMap[$applyInvoice]);
                }
            }
        }

        $couponTickets = AccountCouponTicket::query()->whereIn('invoiceno',$taxInvoiceNumberList->all())
            ->where('isvoid',0)->where('providerid', $providerId)
            ->pluck('invoiceno');
        if ($couponTickets->isNotEmpty()) {
            foreach ($couponTickets as $couponTicket) {
                if ($invoiceBlueToRedMap->has($couponTicket)) {
                    $errorInvoice->push($invoiceBlueToRedMap[$couponTicket]);
                }
            }
        }

        return $errorInvoice->all();
    }

    /**
     * 添加正在发票状态的发票到错误信息表中
     * @return void
     */
    public function addInvoicingToErrorMessage($batch)
    {
        list($batchStartTime, $batchEndTime) = [$batch . ' 00:00:00', $batch . " 23:59:59"];
        $invoicing = NuonuoInvoiceDailyInfo::query()
            ->whereBetween('add_time',[$batchStartTime,$batchEndTime])
            ->where('status',NuonuoInvoiceMatchConstant::INVOICE_STATUS_INVOICING)
            ->get();
        if ($invoicing->isNotEmpty()) {
            $invoiceErrorCollection = collect();
            foreach ($invoicing as $item) {
                $invoiceErrorCollection->push([
                    'system_invoice_type' => NuonuoInvoiceMatchConstant::SYSTEM_INVOICE_TYPE_RED,
                    'system_invoice_id' => $item->id,
                    'error_type' => NuonuoInvoiceMatchConstant::ERROR_TYPE_NUONUO_LOSE,
                    'error_description' => "诺诺数据缺失 发票号：{$item->invoice_no} "
                        . "流水号：{$item->serial_no}",
                    'invoice_time' => strtotime($batch),
                    'provider_id' => $item->provider_id ?: 0,
                    'invoice_no' => '',
                ]);
            }
            SystemNuonuoInvoiceError::query()->insert($invoiceErrorCollection->toArray());
        }
    }

    /**
     * @param OnlineInvoice $onlineInvoice
     * @return bool
     */
    private function shouldRecordRedInvoiceError(OnlineInvoice $onlineInvoice):bool
    {
        $res = true;
        if ($onlineInvoice->status == OnlineInvoiceConstant::STATUS_FAIL) {
            $res = false;
        }

        if (empty($onlineInvoice->invoice_serial_num) && empty($onlineInvoice->invoice_no)) {
            $res = false;
        }

        return $res;
    }
}