<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2024/8/28
 */

namespace App\Services\TypicalCaseLibrary;

use App\Exceptions\BusinessException;
use App\Models\Pattern;
use App\Models\TypicalCaseMainInfo;
use App\Models\TypicalCaseTestTemplate;
use App\Models\TypicalCaseTestTemplatePatternRelation;
use App\Traits\Order\TypicalCaseTestTemplateReviewNotify;
use App\Utils\EnvironmentHelper;
use Yanqu\YanquPhplib\Openapi\TypicalCase\Constants\CaseOriginConstants;

class TypicalCaseTestTemplateService
{
    use TypicalCaseTestTemplateReviewNotify;

    public function getList($params)
    {
        $patternIds = [];
        if(isset($params['machine_name']) && strlen($params['machine_name']) > 0){
            $patternIds = Pattern::where('pattern_name','like',"%{$params['machine_name']}%")->pluck('pattern_id')->toArray();
            $patternIds[] = 0;
        }
        $templateIDList = TypicalCaseTestTemplate::query()
            ->when($params['type'] > 0, function ($query) use ($params) {
                $query->where('typical_case_test_template.type', $params['type']);
            })
            ->when(isset($params['name']) && strlen($params['name']) > 0, function ($query) use ($params) {
                $query->where('typical_case_test_template.name', 'like', "%{$params['name']}%");
            })
            ->when(isset($params['content']) && strlen($params['content']) > 0, function ($query) use ($params) {
                $query->where('typical_case_test_template.content', 'like', "%{$params['content']}%");
            })
            ->when(isset($params['operator']) && $params['operator'] > 0, function ($query) use ($params) {
                $query->where('typical_case_test_template.operator', $params['operator']);
            })
            ->when($patternIds || (isset($params['pattern']) && strlen($params['pattern']) > 0), function ($query) use ($patternIds, $params) {
                $query->leftJoin('typical_case_test_template_and_pattern_relation', function ($join) {
                    $join->on('typical_case_test_template_and_pattern_relation.template_id', '=', 'typical_case_test_template.id')
                        ->on('typical_case_test_template_and_pattern_relation.isvoid', '=', \DB::raw(0));
                });
                if ($patternIds) {
                    $query->whereIn('typical_case_test_template_and_pattern_relation.pattern_id', $patternIds);
                }
                if (isset($params['pattern']) && strlen($params['pattern']) > 0) {
                    $query->whereIn('typical_case_test_template_and_pattern_relation.pattern_id', explode(',', $params['pattern']));
                }
            })->when(isset($params['code']) && strlen($params['code']) > 0, function ($query) use ($params) {
                $query->where('typical_case_test_template.id', (int)$params['code']);
            })->when(isset($params['kind']) && $params['kind'] > 0, function ($query) use ($params) {
                $query->where('typical_case_test_template.kind', $params['kind']);
            })
            ->where('typical_case_test_template.review_status', (int)$params['review_status'])
            ->when($params['origin'] == CaseOriginConstants::SUPPLIER && $params['review_status'] != TypicalCaseTestTemplate::REVIEW_STATUS_PASS, function ($query) use ($params) {
                $query->where('typical_case_test_template.operator', $params['account_id']);
            })
            ->where('typical_case_test_template.isvoid', 0)
            ->pluck('typical_case_test_template.id')->toArray();
        if (empty($templateIDList)) {
            $total = 0;
            $list = [];
        } else {
            $templateIDList = array_flip(array_flip($templateIDList));
            $paginate = TypicalCaseTestTemplate::with('pattern:pattern,pattern_name','operatorInfo:accountid,realname','editorInfo:accountid,realname','reviewerInfo:accountid,realname')
                ->whereIn('id', $templateIDList)
                ->orderBy('update_time', 'desc')
                ->orderByDesc('id')
                ->paginate($params['page_size'] ?? 10, [
                    'typical_case_test_template.id',
                    'typical_case_test_template.type',
                    'typical_case_test_template.name',
                    'typical_case_test_template.update_time',
                    'typical_case_test_template.origin',
                    'typical_case_test_template.operator',
                    'typical_case_test_template.editor',
                    'typical_case_test_template.status',
                    'typical_case_test_template.kind',
                    'typical_case_test_template.review_status',
                    'typical_case_test_template.reviewer',
                ], 'page', $params['page'] ?? 1);
            $total = $paginate->total();
            foreach ($paginate->items() as $item) {
                $patternList = $item->pattern->pluck('pattern')->toArray();
                $machineNameList = $item->pattern->pluck('pattern_name')->toArray();
                $item->pattern_name = $patternList ? implode(',',$patternList) : '';
                $item->machine_name = $machineNameList ? implode(',',$machineNameList) : '';
                $item->operator_name = $item->operatorInfo->realname ?? '';
                $item->editor_name = $item->editorInfo->realname ?? '';
                $item->code = (string)$item->id;
                $item->reviewer_name = $item->reviewerInfo->realname ?? '';
                unset($item->pattern);
                unset($item->operatorInfo);
                unset($item->reviewerInfo);
            }
            $list = $paginate->items();
        }
       return ['total' => $total,'list' => $list];
    }

    public function getReferList($params)
    {
        $templateIDList = TypicalCaseTestTemplate::query()
            ->when($params['type'] > 0, function ($query) use ($params) {
                $query->where('typical_case_test_template.type', $params['type']);
            })
            ->when(isset($params['name']) && strlen($params['name']) > 0, function ($query) use ($params) {
                $query->where('typical_case_test_template.name', 'like', "%{$params['name']}%");
            })
            ->when(isset($params['content']) && strlen($params['content']) > 0, function ($query) use ($params) {
                $query->where('typical_case_test_template.content', 'like', "%{$params['content']}%");
            })
            ->when(isset($params['pattern']) && strlen($params['pattern']) > 0, function ($query) use ($params) {
                $query->leftJoin('typical_case_test_template_and_pattern_relation', function ($join) {
                    $join->on('typical_case_test_template_and_pattern_relation.template_id', '=', 'typical_case_test_template.id')
                        ->on('typical_case_test_template_and_pattern_relation.isvoid', '=', \DB::raw(0));
                })->whereIn('typical_case_test_template_and_pattern_relation.pattern_id', explode(',', $params['pattern']));
            })
            ->where('typical_case_test_template.review_status', TypicalCaseTestTemplate::REVIEW_STATUS_PASS)
            ->where('typical_case_test_template.isvoid', 0)
            ->pluck('typical_case_test_template.id')->toArray();
        if (empty($templateIDList)) {
            $total = 0;
            $list = [];
        } else {
            $templateIDList = array_flip(array_flip($templateIDList));
            $paginate = TypicalCaseTestTemplate::with('pattern:pattern,pattern_name')
                ->whereIn('id', $templateIDList)
                ->orderBy('update_time', 'desc')
                ->orderByDesc('id')
                ->paginate($params['page_size'] ?? 10, [
                    'typical_case_test_template.id',
                    'typical_case_test_template.type',
                    'typical_case_test_template.name',
                    'typical_case_test_template.content',
                ], 'page', $params['page'] ?? 1);
            $total = $paginate->total();
            foreach ($paginate->items() as $item) {
                $patternList = $item->pattern->pluck('pattern')->toArray();
                $item->pattern_name = $patternList ? implode(',',$patternList) : '';
                $item->code = (string)$item->id;
                unset($item->pattern);
            }
            $list = $paginate->items();
        }
        return ['total' => $total,'list' => $list];
    }

    /**
     * @param $params
     * @return void
     * @throws \Exception
     * @description 新增或保存模板
     */
    public function save($params)
    {
        $id = $params['id'] ?? 0;
        $patternIDList = explode(',', $params['pattern']);
        $params['operator'] = $this->getOperator($params);
        if ($id) {
            // 更新
            $oldTemplate = TypicalCaseTestTemplate::query()->where('id', $id)->first();
            if ($params['origin'] == CaseOriginConstants::SUPPLIER && $params['operator'] != $oldTemplate->operator) {
                throw new BusinessException('非创建人无法修改模板');
            }
            $update = [
                'name' => $params['name'],
                'content' => $params['content'],
                'editor' => $params['operator'],
                'kind' => $params['kind'],
            ];
            TypicalCaseTestTemplate::query()->where('id', $id)->update($update);
        } else {
            $id = TypicalCaseTestTemplate::query()->insertGetId([
                'type' => $params['type'],
                'name' => $params['name'],
                'content' => $params['content'],
                'origin' => $params['origin'],
                'operator' => $params['operator'],
                'editor' => $params['operator'],
                'kind' => $params['kind'],
                'status' => 1,
                'isvoid' => 0,
            ]);
        }
        TypicalCaseTestTemplatePatternRelation::whereTemplateId($id)->update(['isvoid' => 1]);
        foreach ($patternIDList as $pattern) {
            TypicalCaseTestTemplatePatternRelation::updateOrCreate(
                ['template_id' => $id, 'pattern_id' => $pattern],
                ['template_id' => $id, 'pattern_id' => $pattern,'isvoid' => 0]
            );
        }
    }

    private function getOperator(array $param)
    {
        // 供应商操作，取关联的账号
        if ($param['origin'] == CaseOriginConstants::SUPPLIER) {
            if (empty($param['account_id'])) {
                throw new BusinessException('登陆账号信息有误');
            }
            return $param['account_id'];
        }
        return $param['operator'];
    }

    public function getDetail($id)
    {
        $template = TypicalCaseTestTemplate::query()->with(['pattern:pattern.pattern_id,pattern as pattern_name'])
            ->where('id', $id)
            ->select('typical_case_test_template.*')
            ->first();
        if($template != null){
            $patternList = $template->pattern->pluck('pattern_name')->toArray();
            $patternIDList = $template->pattern->pluck('pattern_id')->toArray();
            $template->pattern_name = $patternList ? implode(',',$patternList) : '';
            $template->pattern_id = $patternIDList;
            $template->code = (string)$template->id;
            unset($template->pattern);
        }
        return $template;
    }

    public function delete($data)
    {
        $template = TypicalCaseTestTemplate::query()->find($data['id']);
        if ($template->isvoid) {
            throw new BusinessException('该方法已被删除，请勿重复操作');
        }
        if ($template->origin != $data['origin']) {
            throw new BusinessException("非此平台添加数据，无权删除");
        }
        $hasCaseRelation = TypicalCaseMainInfo::query()
            ->join('typical_case_step_template', 'typical_case_main_info.id', '=', 'typical_case_step_template.case_id')
            ->where('typical_case_step_template.template_code', (string)$data['id'])
            ->where('typical_case_step_template.isvoid', 0)
            ->where('typical_case_main_info.isvoid', 0)
            ->exists();
        if ($hasCaseRelation) {
            throw new BusinessException('该方法已被案例引用，无法删除');
        }
        $data['operator'] = $this->getOperator($data);
        $updateData = [
            'isvoid' => 1,
            'editor' => $data['operator'],
        ];
        TypicalCaseTestTemplate::query()->where('id', $data['id'])->update($updateData);
    }

    public function submitReview(array $param)
    {
        $template = TypicalCaseTestTemplate::find($param['id']);
        if (!$template || $template->isvoid || $template->review_status != TypicalCaseTestTemplate::REVIEW_STATUS_NOT_SUBMIT) {
            throw new BusinessException('模板不存在或状态有误');
        }
        $template->review_status = TypicalCaseTestTemplate::REVIEW_STATUS_WAIT_REVIEW;
        $template->reviewer = EnvironmentHelper::isProduction() ? 1090 : ($param['account_id'] ?? $param['operator']);
        $template->save();
        $this->notifyReview($template);
        return [];
    }

    public function review(array $param)
    {
        if (!$param['pass'] && empty($param['reason'])) {
            throw new BusinessException('请填写拒绝理由');
        }
        $template = TypicalCaseTestTemplate::find($param['id']);
        if (!$template || $template->isvoid || $template->review_status != TypicalCaseTestTemplate::REVIEW_STATUS_WAIT_REVIEW) {
            throw new BusinessException('模板不存在或状态有误');
        }
        if ($template->reviewer != $this->getOperator($param)) {
            throw new BusinessException('非审核人不能操作审核');
        }
        if ($param['pass']) {
            $template->review_status = TypicalCaseTestTemplate::REVIEW_STATUS_PASS;
            $template->save();
        } else {
            $template->review_status = TypicalCaseTestTemplate::REVIEW_STATUS_NOT_SUBMIT;
            $template->reviewer = 0;
            $template->save();
            $this->notifyReviewReject($template, $param['reason']);
        }
        return [];
    }

    public function switchReviewer(array $param)
    {
        $template = TypicalCaseTestTemplate::find($param['id']);
        if (!$template || $template->isvoid || $template->review_status != TypicalCaseTestTemplate::REVIEW_STATUS_WAIT_REVIEW) {
            throw new BusinessException('模板不存在或状态有误');
        }
        if ($template->reviewer == $param['reviewer']) {
            throw new BusinessException('转交人不能为当前审核人');
        }
        $template->reviewer = $param['reviewer'];
        $template->save();
        $this->notifyReview($template);
        return [];
    }
}