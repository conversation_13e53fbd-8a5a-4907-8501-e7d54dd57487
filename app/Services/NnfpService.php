<?php


namespace App\Services;

use App\Constants\Invoice\NuonuoInvoiceMatchConstant;
use App\Constants\Invoice\NuoNuoInvoiceResultStatusConstant;
use App\Constants\Invoice\OnlineInvoiceConstant;
use App\Constants\InvoiceConstants;
use App\Constants\InvoiceRedConfirmStatus;
use App\Constants\NuoNuoConstants;
use App\Entities\CloseReceiptDTO;
use App\Entities\NuoNuo\NuoNuoRequestDTO;
use App\Entities\NuoNuo\OpenReceiptNuoNuoDTO;
use App\Entities\OpenReceiptDTO;
use App\Models\AccountCouponTicket;
use App\Models\AccountCouponTicketHistory;
use App\Models\FinanceTicketApply;
use App\Models\FinanceTicketHistory;
use App\Models\InvoiceRedConfirm;
use App\Models\OnlineInvoice;
use App\Models\OnlineInvoiceErrorLog;
use App\Utils\EnvironmentHelper;
use App\Utils\LogHelper;
use App\Utils\StatusCode;
use App\Exceptions\NuoNuoException;
use Yanqu\YanquPhplib\YqLog\YqLog;

require_once __DIR__ . "/../Utils/Nnfp/Api.php";


class NnfpService extends BaseService
{
    private $tokenType;

    public function setTokenType($tokenType){
        $this->tokenType = $tokenType;
    }
    /**
     * 请求诺诺的【请求开具发票接口】
    **/
    public function requestBilling($billingInfo){
        if($this->tokenType == NuoNuoConstants::TOKEN_TYPE_FULL_ELECTRIC){
            $method = "nuonuo.OpeMplatform.requestBillingNew";
            $billingInfo['showBankAccountType'] = $this->getShowBankAccountType($billingInfo);
        }else{
            $method = "nuonuo.ElectronInvoice.requestBillingNew";
        }
        if($billingInfo['invoiceType'] == 2){   //开具红票
            $billingInfo['ticketremark'] = "对应正数发票代码:".$billingInfo['invoiceCode']."号码:".$billingInfo['invoiceNum'];
        }
        $order = $billingInfo;
        $body = ['order'=>$order];

        //记录开票信息
        $onlineInvoiceService = new OnlineInvoiceService();
        $onlineInvoiceId = 0;
        $addOnlineInvoice = $onlineInvoiceService->addOnlineInvoice($order,$onlineInvoiceService::TRANS_TYPE_PUBLIC);
        if($addOnlineInvoice['code'] == StatusCode::ADD_ONLINE_INVOICE_SUCCESS){
            $onlineInvoiceId = $addOnlineInvoice['data']['onlineInvoiceId'];
        }else{
            $addOnlineInvoice['describe'] = $addOnlineInvoice['msg'];
            return json_encode($addOnlineInvoice);
        }
        list($applyType,$applyId) = [0,0];
        if(isset($billingInfo['applyid']) && !empty($billingInfo['applyid'])){
            OnlineInvoice::where('id',$onlineInvoiceId)->update(['applyid'=>$billingInfo['applyid'],'type'=>1]);
            $applyType = InvoiceConstants::APPLY_TYPE_FINANCE;
            $applyId = $billingInfo['applyid'];
        }elseif(isset($billingInfo['cticketid']) && !empty($billingInfo['cticketid'])){
            OnlineInvoice::where('id',$onlineInvoiceId)->update(['cticketid'=>$billingInfo['cticketid'],'type'=>2]);
            $applyType = InvoiceConstants::APPLY_TYPE_COUPON;
            $applyId = $billingInfo['cticketid'];
        }

        $originResult = $this->SendRequest($method,$body,$billingInfo['salerTaxNum'],$billingInfo['token']);

        //记录开票流水号
        $result = json_decode($originResult,true);
        if($result['code'] == 'E0000'){
            $queryid = $result['result']['invoiceSerialNum'];
            OnlineInvoice::where('id',$onlineInvoiceId)->update(['invoice_serial_num'=>$queryid]);
        }else{
            $this->logNuoNuoError($applyType,$applyId,$method,$originResult,json_encode(['body' => $body,'tokenType' => $this->tokenType]));
            OnlineInvoice::where('id',$onlineInvoiceId)->update(['is_void'=>1]);
        }
        return $originResult;
    }
    /**
     * 请求诺诺的【开票结果查询接口】
    **/
    public function queryInvoiceResult($serialNos,$taxnum,$token){
        if($this->tokenType == 9){
            $method = "nuonuo.OpeMplatform.queryInvoiceResult";
        }else{
            $method = "nuonuo.ElectronInvoice.queryInvoiceResult";
        }
        $body = ['serialNos'=>[$serialNos]];
        $originResult = $this->SendRequest($method,$body,$taxnum,$token);
        try {
            $onlineInvoiceService = new OnlineInvoiceService();
            $onlineInvoiceService->addOnlineInvoiceResult($serialNos, $originResult);
        } catch (\Exception $e) {
            YqLog::logger('finance:invoice')->error($e, ['originResult' => $originResult]);
        }
        //记录开票信息
        $result = json_decode($originResult,true);
        if($result['code'] != 'E0000'){
        }elseif($result['result'][0]['status'] == '2'){
            $invoiceInfo = $result['result'][0];
            $invoiceLine['invoice_dm'] = $invoiceInfo['invoiceCode'];
            $invoiceLine['invoice_no'] = $invoiceInfo['invoiceNo'];
            $invoiceLine['pdf_url'] = $invoiceInfo['pdfUrl'];
            $invoiceLine['status'] = 2;
            OnlineInvoice::where('invoice_serial_num',$serialNos)->update($invoiceLine);
        }elseif(in_array($result['result'][0]['status'],['22','24'])){
            OnlineInvoice::where('invoice_serial_num',$serialNos)->update(['status'=>2]);
        }

        return $originResult;
    }

    public function SendRequest($method,$body,$taxnum,$token){
        [$appConfig, $url] = [config('invoice.nuonuoAppConfigMap'), config('invoice.invoiceNnfpUrl')];
        if (isset($appConfig[$this->tokenType])) {
            $appKey = $appConfig[$this->tokenType]['appKey'];
            $appSecret = $appConfig[$this->tokenType]['appSecret'];
        } else {
            $appKey = config('invoice.invoiceNnfpAppKey');
            $appSecret = config('invoice.invoiceNnfpAppSecret');
        }

        if (EnvironmentHelper::isProduction()){

        }else{
            //测试的商户税号、销售方税号
            $taxnum = "339901999999142";
            $token=  "12345";
            if ($method == NuoNuoConstants::METHOD_APPLY_INVOICE || $method == NuoNuoConstants::METHOD_APPLY_INVOICE_ELECTRONIC) {
                $body['order']['salerTaxNum'] = "339901999999142";
                if($this->tokenType == 9){
                    $body['order']['extensionNumber'] = "8889";
                }else{
                    $body['order']['extensionNumber'] = "0";
                    $body['order']['terminalNumber'] = "1";
                    $body['order']['machineCode'] = "111111111111";
                }
            } elseif($method == NuoNuoConstants::METHOD_RED_CONFIRM) {
                $body['sellerTaxNo'] = $taxnum;
            } elseif ($method == NuoNuoConstants::METHOD_FAST_INVOICE_RED) {
                $body['taxNum'] = $taxnum;
            } elseif ($method == NuoNuoConstants::METHOD_QUERY_INVOICE_LIST
            || $method == NuoNuoConstants::METHOD_OPEN_RECEIPT || $method ==
                NuoNuoConstants::METHOD_CANCEL_RECEIPT) {
                //请求列表测试接口需要用到诺诺正式环境url
                $appKey = '78607195';
                $appSecret = '26A860C451744A21';
                $token = '77f21f617f87135fcd97ef1ueud3dxdi';
                $taxnum = '339901999999199';
                if ($method == NuoNuoConstants::METHOD_QUERY_INVOICE_LIST) {
                    $body['taxnum'] = '339901999999199';
                } elseif($method == NuoNuoConstants::METHOD_OPEN_RECEIPT) {
                    $body['salerTaxNum'] = '339901999999199';
                }
                $url = 'https://sdk.nuonuo.com/open/v1/services';
            }

        }

        $senid = uniqid();
        $body = json_encode($body);
        LogHelper::doLog('nuonuoRequest',json_encode([
            'body' => $body,
            'url' => $url,
            'senid' => $senid,
            'appKey' => $appKey,
            'token' => $token,
            'taxnum' => $taxnum,
            'method' => $method,
            'tokenType' => $this->tokenType,
        ]),JSON_UNESCAPED_UNICODE);
        $res = \Api::sendPostSyncRequest($url, $senid, $appKey, $appSecret, $token, $taxnum, $method, $body, 60);
        return $res;
    }

	/**
     * 生成发票订单号
     * id：applyid/cticketid
     * type：1订单发票/2预存发票
	**/
	public function createTicketOrderNo($id, $type){
	    $ticketOrderNo = '';
	    if($type == 1){
	        $ticketOrderNo = '10'.str_pad(trim($id),14,'0',STR_PAD_LEFT).rand(1000, 9999);
        }elseif($type == 2){
	        $ticketOrderNo = '11'.str_pad(trim($id),14,'0',STR_PAD_LEFT).rand(1000, 9999);
        }
	    return $ticketOrderNo;
    }

    public function getShowBankAccountType($billingInfo){
        $showBankAccountType = 0;
        if (!empty($billingInfo['buyerAccount']) && !empty($billingInfo['salerAccount'])) {
            $showBankAccountType = 3;
        } elseif (!empty($billingInfo['salerAccount'])) {
            $showBankAccountType = 1;
        } elseif (!empty($billingInfo['buyerAccount'])) {
            $showBankAccountType = 2;
        }
        return $showBankAccountType;
    }


    public function requestRedConfirm($billingInfo,$redReason,$adminId,$blueTicketInfo)
    {
        $nuonuoRequest = new NuoNuoRequestDTO();
        //记录开票信息
        $onlineInvoiceService = new OnlineInvoiceService();
        $addOnlineInvoice = $onlineInvoiceService->addOnlineInvoice($billingInfo,$onlineInvoiceService::TRANS_TYPE_PUBLIC);
        if($addOnlineInvoice['code'] == StatusCode::ADD_ONLINE_INVOICE_SUCCESS){
            $onlineInvoiceId = $addOnlineInvoice['data']['onlineInvoiceId'];
        }else{
            $addOnlineInvoice['describe'] = $addOnlineInvoice['msg'];
            return json_encode($addOnlineInvoice);
        }
        list($applyType,$applyId) = [0,0];
        if(!empty($billingInfo['applyid'])){
            OnlineInvoice::where('id',$onlineInvoiceId)->update(['applyid'=>$billingInfo['applyid'],'type'=>1]);
            $applyType = InvoiceConstants::APPLY_TYPE_FINANCE;
            $applyId = $billingInfo['applyid'];
        }elseif(!empty($billingInfo['cticketid'])){
            OnlineInvoice::where('id',$onlineInvoiceId)->update(['cticketid'=>$billingInfo['cticketid'],'type'=>2]);
            $applyType = InvoiceConstants::APPLY_TYPE_COUPON;
            $applyId = $billingInfo['cticketid'];
        }
        $nuonuoRequest->setApplyType($applyType);
        $nuonuoRequest->setApplyId($applyId);
        $nuonuoRequest->setBody($this->buildRedConfirm($billingInfo,$redReason));
        $nuonuoRequest->setSalerTaxNum($billingInfo['salerTaxNum']);
        $nuonuoRequest->setToken($billingInfo['token']);
        try {
            //申请红字确认单
            $confirmResult = $this->applyRedConfirm($nuonuoRequest);
            $redConfirmApplyNo = $confirmResult['result'];
            $queryRedConfirmBody = [
                'identity' => NuoNuoConstants::APPLY_SOURCE_SALE,
                'billId' =>  $redConfirmApplyNo,
            ];
            $nuonuoRequest->setBody($queryRedConfirmBody);
            $invoiceRedConfirm = $this->saveInvoiceRedConfirm($applyType,$applyId,
                InvoiceRedConfirmStatus::APPLYING,$redConfirmApplyNo,$billingInfo['invoiceNum'],$adminId,
                $blueTicketInfo, $billingInfo['invoiceCode']);
            //查询红字确认单信息
            $confirmInfo = $this->queryInvoiceRedConfirm($nuonuoRequest);
            $confirmInfo['billStatus'] = (int) $confirmInfo['billStatus'];
            $invoiceRedConfirm->red_bill_no = $confirmInfo['billNo'] ?? '';
            if ($confirmInfo['billStatus'] == InvoiceRedConfirmStatus::NOT_NEED_CHECK) {
                //电票会自动红冲 纸票需要通过快捷红冲接口 获取发票流水号
                if (!empty($confirmInfo['invoiceSerialNum'])) {
                    $invoiceSerialNum =  $confirmInfo['invoiceSerialNum'];
                    OnlineInvoice::where('id',$onlineInvoiceId)->update(['invoice_serial_num'=>$invoiceSerialNum]);
                    $invoiceRedConfirm->invoice_serial_num = $invoiceSerialNum;
                    $invoiceRedConfirm->refreshBillStatus(InvoiceRedConfirmStatus::NOT_NEED_CHECK,0);
                } else {
                    //手动红冲开票
                    $nuonuoRequest->setBody($this->buildFastInvoiceRedBody($billingInfo,$confirmInfo));
                    $invoiceSerialNum = $this->getDigitInvoiceSerialNum($nuonuoRequest);
                    OnlineInvoice::where('id',$onlineInvoiceId)->update(['invoice_serial_num'=>$invoiceSerialNum]);
                    $invoiceRedConfirm->invoice_serial_num = $invoiceSerialNum;
                    $invoiceRedConfirm->refreshBillStatus(InvoiceRedConfirmStatus::NOT_NEED_CHECK,0);
                }
                //查询发票状态
                $methodResult = $this->getRedInvoiceResultReturnMethodResult($billingInfo['salerTaxNum'],
                    $billingInfo['token'], $invoiceSerialNum, $invoiceRedConfirm);
            } elseif ($confirmInfo['billStatus'] == InvoiceRedConfirmStatus::APPLYING) {
                $invoiceRedConfirm->save();
                $methodResult = json_encode([
                    'code' => NuoNuoConstants::SERVICE_SUCCESS_WAITING,
                    'result' => ['billId' => $redConfirmApplyNo],
                    'describe' => '红字确认单申请中',
                ]);
            } else {
                //撤销红字申请单
                $nuonuoRequest->setBody([
                    'identity' => NuoNuoConstants::APPLY_SOURCE_SALE,
                    'billId' => $redConfirmApplyNo,
                ]);
                $this->cancelInvoiceRedConfirm($invoiceRedConfirm,$nuonuoRequest,$confirmInfo['billStatus'],
                    $invoiceRedConfirm->bill_status);
                $methodResult = json_encode([
                    'code' => NuoNuoConstants::SERVICE_ERROR_CODE,
                    'result' => [],
                    'describe' => '当前红字确认单需要确认，不可自动红冲',
                ]);
            }
        } catch (NuoNuoException $exception) {
            $this->logNuoNuoError($nuonuoRequest->getApplyType(),$nuonuoRequest->getApplyId(),
                $nuonuoRequest->getMethod(),$exception->getMessage(),json_encode(['body' => $nuonuoRequest->getBody(),'tokenType' => $this->tokenType]));
            $methodResult = $exception->getMessage();
        }
        return $methodResult;
    }


    public function refreshRedConfirm($applyType,$applyId,$taxNumber,$token, $resultTag)
    {
        $nuonuoRequest = new NuoNuoRequestDTO();
        $nuonuoRequest->setSalerTaxNum($taxNumber);
        $nuonuoRequest->setToken($token);
        $nuonuoRequest->setApplyType($applyType);
        $nuonuoRequest->setApplyId($applyId);
        $nuonuoRequest->setMethod(NuoNuoConstants::METHOD_QUERY_RED_CONFIRM);
        try {
            $redConfirmInfo = InvoiceRedConfirm::query()->where('applytype',$applyType)->where('applyid',$applyId)
                ->where('is_finished',0)->where('is_new',1)->firstOr(function () {
                    throw new NuoNuoException(json_encode(
                        [
                            'code' => NuoNuoConstants::SERVICE_NO_PROCESS_REQUIRE,
                            'result' => [],
                            'describe' => '当前无可操作的红字申请单',
                        ]));
                });
            $nuonuoRequest->setBody([
                'identity' => NuoNuoConstants::APPLY_SOURCE_SALE,
                'billId' => $redConfirmInfo->red_bill_id,
            ]);
            $methodResult = json_encode([
                'code' => NuoNuoConstants::METHOD_SUCCESS_CODE,
                'result' => [],
                'describe' => '',
            ]);

            if (empty($redConfirmInfo->invoice_serial_num)) {
                $result = $this->queryInvoiceRedConfirm($nuonuoRequest);
                $oldBillStatus = $redConfirmInfo->bill_status;
                $redConfirmInfo->bill_status = (int) $result['billStatus'];
                $redConfirmInfo->red_bill_no = $result['billNo'] ?? '';
                if ($result['billStatus'] == InvoiceRedConfirmStatus::APPLYING) {
                    throw new NuoNuoException(json_encode([
                        'code' => NuoNuoConstants::SERVICE_SUCCESS_WAITING,
                        'result' => [],
                        'describe' => '申请中状态',
                    ]));
                } elseif ($result['billStatus'] == InvoiceRedConfirmStatus::APPLICATION_FAILED) {
                    $redConfirmInfo->is_finished = 1;
                    $redConfirmInfo->save();
                    $billMessage = $result['billMessage'] ?? '';
                    throw new NuoNuoException(json_encode([
                        'code' => NuoNuoConstants::SERVICE_INVOICE_RED_CONFIRM_APPLY_FAIL,
                        'result' => [],
                        'describe' => "当前红字确认单编号 {$redConfirmInfo->red_bill_no}申请失败".$billMessage,
                    ]));
                }
                //无需确认 有状态
                if ($redConfirmInfo->bill_status == InvoiceRedConfirmStatus::NOT_NEED_CHECK) {
                    if (!empty($result['invoiceSerialNum'])) {
                        $methodResult = json_encode([
                            'code' => NuoNuoConstants::METHOD_SUCCESS_CODE,
                            'result' => ['invoiceSerialNum' => $result['invoiceSerialNum']],
                            'describe' => '开票提交成功',
                        ]);
                        $redConfirmInfo->invoice_serial_num = $result['invoiceSerialNum'];
                        $key = $applyType == 1 ? 'applyid' : 'cticketid';
                        OnlineInvoice::query()->where('type',$applyType)->where($key,$applyId)
                            ->orderBy('id','desc')->limit(1)
                            ->update([
                                'invoice_serial_num' => $result['invoiceSerialNum'],
                            ]);
                        $redConfirmInfo->save();
                    } else {
                        //手动开票
                        $fastInvoiceBody = [
                            'orderNo' => $this->createTicketOrderNo($applyId,$applyType),
                            'taxNum' => $taxNumber,
                            'invoiceCode' => empty($redConfirmInfo['invoice_code']) ? '' : $redConfirmInfo['invoice_code'],
                            'invoiceNumber' => $result['blueInvoiceNumber'],
                            'elecInvoiceNumber' => $result['blueInvoiceNumber'],
                            'billNo' => $result['billNo'],
                            'billUuid' => $result['billUuid'],
                            'callBackUrl' => env('next_shiyanjia_base_url').'/callback/nuonuo',
                        ];
                        $nuonuoAllEleInvoiceType = NuoNuoConstants::ELECTRONIC_VAT_INVOICE_TYPE_LIST;
                        if (isset($result['blueInvoiceLine']) &&
                            !in_array($result['blueInvoiceLine'], $nuonuoAllEleInvoiceType)) {
                            unset($fastInvoiceBody['elecInvoiceNumber']);
                        }
                        $nuonuoRequest->setBody($fastInvoiceBody);
                        $invoiceSerialNum = $this->getDigitInvoiceSerialNum($nuonuoRequest);
                        $methodResult = json_encode([
                            'code' => NuoNuoConstants::METHOD_SUCCESS_CODE,
                            'result' => ['invoiceSerialNum' => $invoiceSerialNum],
                            'describe' => '开票提交成功',
                        ]);
                        $redConfirmInfo->invoice_serial_num = $invoiceSerialNum ?: '';
                        $redConfirmInfo->save();
                        $key = $applyType == 1 ? 'applyid' : 'cticketid';
                        OnlineInvoice::query()->where('type',$applyType)->where($key,$applyId)
                            ->orderBy('id','desc')->limit(1)
                            ->update([
                                'invoice_serial_num' => $result['invoiceSerialNum'],
                            ]);
                    }
                } else {
                    $nuonuoRequest->setBody([
                        'identity' => NuoNuoConstants::APPLY_SOURCE_SALE,
                        'billId' => $redConfirmInfo->red_bill_id,
                    ]);
                    $this->cancelInvoiceRedConfirm($redConfirmInfo,$nuonuoRequest,(int) $result['billStatus']
                        , $oldBillStatus);
                    $methodResult = json_encode([
                        'code' => NuoNuoConstants::SERVICE_INVOICE_RED_CONFIRM_CANCEL,
                        'result' => [],
                        'describe' => '当前红字确认单需要确认，不可自动红冲',
                    ]);
                }
            }

            if ($resultTag && !empty($redConfirmInfo->invoice_serial_num)) {
                $methodResult = $this->getRedInvoiceResultReturnMethodResult($taxNumber, $token,
                    $redConfirmInfo->invoice_serial_num, $redConfirmInfo);
            }
        } catch (NuoNuoException $exception) {
            $this->logNuoNuoError($nuonuoRequest->getApplyType(),$nuonuoRequest->getApplyId(),
                $nuonuoRequest->getMethod(),$exception->getMessage(),
                json_encode(['body' => $nuonuoRequest->getBody(),'tokenType' => $this->tokenType]));
            $methodResult = $exception->getMessage();
        }

        return $methodResult;
    }


    public function cancelRedConfirmWithLog($applyType,$applyId,$taxNumber,$token)
    {
        $nuonuoRequest = new NuoNuoRequestDTO();
        $nuonuoRequest->setSalerTaxNum($taxNumber);
        $nuonuoRequest->setToken($token);
        $nuonuoRequest->setApplyType($applyType);
        $nuonuoRequest->setApplyId($applyId);
        $nuonuoRequest->setMethod(NuoNuoConstants::METHOD_CANCEL_RED_CONFIRM);
        try {
            $redConfirmInfo = InvoiceRedConfirm::query()->where('applytype',$applyType)->where('applyid',$applyId)
                ->where('is_finished',0)->firstOr(function () {
                    throw new NuoNuoException(json_encode(
                        [
                            'code' => NuoNuoConstants::SERVICE_NO_PROCESS_REQUIRE,
                            'result' => [],
                            'describe' => '当前无可撤销红字申请单',
                        ]));
                });
            $nuonuoRequest->setBody([
                'identity' => NuoNuoConstants::APPLY_SOURCE_SALE,
                'billId' => $redConfirmInfo->red_bill_id,
            ]);
            $methodResult = $this->cancelRedConfirm($nuonuoRequest);
            $redConfirmInfo->bill_status = InvoiceRedConfirmStatus::INVALIDATED_BY_INITIATOR;
            $redConfirmInfo->is_finished = 1;
            $redConfirmInfo->save();
            $this->markCancelRedConfirm($applyType,$applyId);
            $this->cancelInvoiceRedConfirmAfter($redConfirmInfo, $applyType, $applyId);
        } catch (NuoNuoException $exception) {
            $this->logNuoNuoError($nuonuoRequest->getApplyType(),$nuonuoRequest->getApplyId(),
                $nuonuoRequest->getMethod(),$exception->getMessage(),json_encode(['body' => $nuonuoRequest->getBody(),'tokenType' => $this->tokenType]));
            $methodResult = $exception->getMessage();
        }

        return $methodResult;
    }


    /**
     * 记录诺诺异常
     * @param $applyType
     * @param $applyId
     * @param $method
     * @param $result
     *
     * @return mixed
     */
    public function logNuoNuoError($applyType,$applyId,$method,$result,$requestBody)
    {
        return OnlineInvoiceErrorLog::insert([
            'apply_type' => $applyType,
            'apply_id' => $applyId,
            'method' => $method,
            'result' => $result,
            'request_body' => $requestBody,
        ]);
    }

    public function markCancelRedConfirm($applyType,$applyId)
    {
        if ($applyType == InvoiceConstants::APPLY_TYPE_FINANCE) {
            FinanceTicketApply::query()->where('applyid',$applyId)->update([
                'red_check_cancel_status' => 1,
            ]);
        } elseif ($applyType == InvoiceConstants::APPLY_TYPE_COUPON) {
            AccountCouponTicket::query()->where('cticketid',$applyId)->update([
                'red_check_cancel_status' => 1,
            ]);
        }
    }

    public function buildRedConfirm($billingInfo,$redReason)
    {
        $body = [
            'blueInvoiceLine' => $billingInfo['blueInvoiceLine'],
            'applySource' => NuoNuoConstants::APPLY_SOURCE_SALE,
            'blueInvoiceCode' => $billingInfo['invoiceCode'],
            'blueInvoiceNumber'=> $billingInfo['invoiceNum'],
            'blueElecInvoiceNumber'=> $billingInfo['invoiceNum'],
            'sellerTaxNo' => $billingInfo['salerTaxNum'],
            'buyerTaxNo' => $billingInfo['buyerTaxNum'],
            'buyerName' => $billingInfo['buyerName'],
            'redReason' => $redReason,
            'orderNo' => $billingInfo['orderNo'],
            'autoInvoice' => 1,
            'callbackUrl' => env('NEXT_SHIYANJIA_BASE_URL').'/callback/nuonuo',
        ];
        if (isset($billingInfo['blueInvoiceLine']) &&
            !in_array($billingInfo['blueInvoiceLine'], NuoNuoConstants::ELECTRONIC_VAT_INVOICE_TYPE_LIST)) {
            unset($body['blueElecInvoiceNumber']);
        }
        return $body;
    }


    public function buildFastInvoiceRedBody($billingInfo, $confirmInfo)
    {
        $body = [
            'orderNo' => $billingInfo['orderNo'],
            'taxNum' => $confirmInfo['sellerTaxNo'],
            'invoiceCode' => $billingInfo['invoiceCode'],
            'invoiceNumber' => $billingInfo['invoiceNum'],
            'elecInvoiceNumber' => $billingInfo['invoiceNum'],
            'billNo' => $confirmInfo['billNo'],
            'billUuid' => $confirmInfo['billUuid'],
            'callBackUrl' => env('next_shiyanjia_base_url') . '/callback/nuonuo',
        ];
        if (isset($confirmInfo['blueInvoiceLine']) &&
            !in_array($confirmInfo['blueInvoiceLine'], NuoNuoConstants::ELECTRONIC_VAT_INVOICE_TYPE_LIST)) {
            unset($body['elecInvoiceNumber']);
        }
        return $body;
    }

    /**
     * @throws NuoNuoException
     */
    public function baseNuoNuoRequest(NuoNuoRequestDTO $nuoRequestDTO)
    {
        $requestResult = $this->SendRequest($nuoRequestDTO->getMethod(),$nuoRequestDTO->getBody(),$nuoRequestDTO->getSalerTaxNum(),$nuoRequestDTO->getToken());
        $result = json_decode($requestResult,true);

        YqLog::logger('finance:nuonuo')->info("诺诺请求数据记录 入参：" . $nuoRequestDTO->toString()
            . "返回：{$requestResult}");

        if ($result['code'] != NuoNuoConstants::METHOD_SUCCESS_CODE) {
            throw new NuoNuoException($requestResult);
        }

        return $result;
    }

    /**
     * @param  NuoNuoRequestDTO  $nuoRequestDTO
     * 诺诺红字申请单查询状态
     * @return array|mixed
     * @throws NuoNuoException
     */
    public function queryInvoiceRedConfirm(NuoNuoRequestDTO $nuoRequestDTO)
    {
        $nuoRequestDTO->setMethod(NuoNuoConstants::METHOD_QUERY_RED_CONFIRM);
        $queryDecodeResult = $this->baseNuoNuoRequest($nuoRequestDTO);
        if ($queryDecodeResult['result']['total'] >= 1) {
            $confirmInfo = $queryDecodeResult['result']['list'][0];
        } else {
            throw new NuoNuoException(json_encode([
                'code' => NuoNuoConstants::SERVICE_ERROR_CODE,
                'describe' => '当前红字确认单编号无查询结果',
                'result' => [],
            ]));
        }

        return $confirmInfo;
    }


    /**
     * 获取数电红冲红票流水号
     *
     * @throws NuoNuoException
     */
    public function getDigitInvoiceSerialNum(NuoNuoRequestDTO $nuoRequestDTO)
    {
        $nuoRequestDTO->setMethod(NuoNuoConstants::METHOD_FAST_INVOICE_RED);
        $result = $this->baseNuoNuoRequest($nuoRequestDTO);

        return $result['result']['invoiceSerialNum'];
    }

    /**
     * 诺诺申请红字确认单
     * @throws NuoNuoException
     */
    public function applyRedConfirm(NuoNuoRequestDTO $nuoRequestDTO)
    {
        $nuoRequestDTO->setMethod(NuoNuoConstants::METHOD_RED_CONFIRM);

        return $this->baseNuoNuoRequest($nuoRequestDTO);
    }


    /**
     * @throws NuoNuoException
     */
    public function cancelRedConfirm(NuoNuoRequestDTO $nuoRequestDTO)
    {
        $nuoRequestDTO->setMethod(NuoNuoConstants::METHOD_CANCEL_RED_CONFIRM);

        return $this->baseNuoNuoRequest($nuoRequestDTO);
    }

    /**
     * @param $applyType
     * @param $applyId
     * @param $billStatus
     * @param $redBillId
     * @param $invoiceNumber
     *
     * @return InvoiceRedConfirm
     */
    public function saveInvoiceRedConfirm($applyType,$applyId,$billStatus,$redBillId,$invoiceNumber,$adminId,
                                          $blueTicketInfo, $invoiceCode)
    {
        InvoiceRedConfirm::query()->where('applytype',$applyType)->where('applyid',$applyId)->update([
            'is_new' => 0,
        ]);
        $redConfirm = new InvoiceRedConfirm();
        $redConfirm->applytype = $applyType;
        $redConfirm->applyid = $applyId;
        $redConfirm->bill_status = (int)$billStatus;
        $redConfirm->red_bill_id = $redBillId;
        $redConfirm->invoice_code = empty($invoiceCode) ? '' : $invoiceCode;
        $redConfirm->invoice_number = $invoiceNumber;
        $redConfirm->is_new = 1;
        $redConfirm->admin_id = $adminId;
        $redConfirm->amount = $blueTicketInfo['amount'];
        $redConfirm->invoicetime = $blueTicketInfo['invoicetime'];
        $redConfirm->providerid = $blueTicketInfo['providerid'];
        $redConfirm->title = $blueTicketInfo['title'];
        $redConfirm->save();

        return $redConfirm;
    }

    /**
     * @return array
     * @throws NuoNuoException
     */
    public function getInvoiceList($startTime,$endTime,$taxNumber,$token,$page,
                                   $pageSize = NuoNuoConstants::QUERY_LIST_PAGE_SIZE)
    {
        $requestBody = new NuoNuoRequestDTO();
        $requestBody->setSalerTaxNum($taxNumber);
        $requestBody->setToken($token);
        $requestBody->setBody([
            'taxnum' => $taxNumber,
            'requesType' => NuoNuoConstants::QUERY_LIST_REQUEST_TYPE_INVOICE_TIME,
            'startTime' => date('Y-m-d H:i:s',$startTime),
            'endTime' => date('Y-m-d H:i:s',$endTime),
            'pageSize' => $pageSize,
            'pageNo' => $page,
        ]);

        return $this->queryInvoiceList($requestBody);
    }
    /**
     * @param  NuoNuoRequestDTO  $nuoRequestDTO
     *
     * @return array
     * @throws NuoNuoException
     */
    public function queryInvoiceList(NuoNuoRequestDTO $nuoRequestDTO)
    {
        $nuoRequestDTO->setMethod(NuoNuoConstants::METHOD_QUERY_INVOICE_LIST);
        return $this->baseNuoNuoRequest($nuoRequestDTO);
    }

    /**
     * @param NuoNuoRequestDTO $requestDTO
     * @return mixed
     * @throws NuoNuoException
     */
    public function speedBillingBaseNuoNuoRequest(NuoNuoRequestDTO $requestDTO)
    {
        $requestResult = $this->SendRequest($requestDTO->getMethod(),
            $requestDTO->getBody(),$requestDTO->getSalerTaxNum(),$requestDTO->getToken());
        $result = json_decode($requestResult,true);

        if ($result['code'] != NuoNuoConstants::METHOD_SPEED_BILLING_SUCCESS_CODE) {
            throw new NuoNuoException("method: {$requestDTO->getMethod()}\n" . $requestResult);
        }

        YqLog::logger('finance:nuonuo')->info("诺诺speedBilling 请求参数{$requestDTO->toString()}".
            "返回：$requestResult");

        return $result;
    }

    /**
     * @param $taxNumber
     * @param $token
     * @param $q
     * @return array
     * @throws NuoNuoException
     */
    public function queryPrefix($taxNumber,$token,$q)
    {
        $nuonuoRequestDTO = new NuoNuoRequestDTO();
        $nuonuoRequestDTO->setSalerTaxNum($taxNumber);
        $nuonuoRequestDTO->setToken($token);
        $nuonuoRequestDTO->setBody([
            'q' => $q,
        ]);
        $nuonuoRequestDTO->setMethod(NuoNuoConstants::METHOD_QUERY_PREFIX);

        return $this->speedBillingBaseNuoNuoRequest($nuonuoRequestDTO);
    }

    public function queryNameAndTaxByCode($taxNumber,$token,$code)
    {
        $nuonuoRequestDTO = new NuoNuoRequestDTO();
        $nuonuoRequestDTO->setSalerTaxNum($taxNumber);
        $nuonuoRequestDTO->setToken($token);
        $nuonuoRequestDTO->setBody([
            'code' => $code,
        ]);
        $nuonuoRequestDTO->setMethod(NuoNuoConstants::METHOD_QUERY_NAME_AND_TAX_BY_CODE);

        return $this->speedBillingBaseNuoNuoRequest($nuonuoRequestDTO);
    }

    public function getInvoiceInfo($taxNumber,$token,$serialNos,$isOfferInvoiceDetail = 0)
    {
        $requestBody = new NuoNuoRequestDTO();
        $requestBody->setSalerTaxNum($taxNumber);
        $requestBody->setToken($token);
        $requestBody->setBody([
            'serialNos' => $serialNos,
            'isOfferInvoiceDetail' => $isOfferInvoiceDetail,
        ]);
        return $this->queryInvoiceResultClassy($requestBody);
    }

    /**
     * @param NuoNuoRequestDTO $nuoNuoRequestDTO
     * @return mixed
     * @throws NuoNuoException
     */
    public function queryInvoiceResultClassy(NuoNuoRequestDTO $nuoNuoRequestDTO)
    {
        $nuoNuoRequestDTO->setMethod(NuoNuoConstants::METHOD_QUERY_INVOICE_RESULT);
        return $this->baseNuoNuoRequest($nuoNuoRequestDTO);
    }

    /**
     * @param $taxNumber string
     * @param $token string
     * @param $invoiceSerialNum string
     * @param $invoiceRedConfirm InvoiceRedConfirm
     * @return false|string
     */
    private function getRedInvoiceResultReturnMethodResult(string $taxNumber, string $token, string $invoiceSerialNum,
                                                           InvoiceRedConfirm $invoiceRedConfirm)
    {
        $redInvoice = $this->getInvoiceInfo($taxNumber, $token,
            [$invoiceSerialNum], 1);
        //查询发票失败
        if ($redInvoice['code'] == NuoNuoConstants::METHOD_FAIL_CODE) {
            YqLog::logger('finance:invoice:refresh')
                ->error("诺诺查询发票信息失败", [
                    'invoiceSerialNum' => $invoiceSerialNum,
                    'taxNumber' => $taxNumber,
                    'token' => $token,
                    'result' => $redInvoice,
                    'param' => app('request')->all(),
                ]);
            return json_encode([
                'code' => NuoNuoConstants::SERVICE_INVOICE_FAIL,
                'result' => [],
                'describe' => '查询发票信息失败' . $redInvoice['describe'],
            ]);

        }
        $redInvoice = $redInvoice['result'][0];

        if ($redInvoice['status'] == NuoNuoInvoiceResultStatusConstant::SUCCESS) {
            $invoiceRedConfirm->red_invoice_number = empty($redInvoice['invoiceNo']) ?
                $redInvoice['allElectronicInvoiceNumber'] : $redInvoice['invoiceNo'];
            $invoiceRedConfirm->is_finished = 1;
            $invoiceRedConfirm->save();
            OnlineInvoice::query()->where('invoice_serial_num', $invoiceRedConfirm->invoice_serial_num)
                ->orderByDesc('id')->limit(1)->update([
                    'invoice_no' => $invoiceRedConfirm->red_invoice_number,
                    'status' => OnlineInvoiceConstant::STATUS_SUCCESS,
                ]);
            $methodResult = json_encode([
                'code' => NuoNuoConstants::METHOD_SUCCESS_CODE,
                'result' => [
                    'invoiceSerialNum' => $invoiceRedConfirm->invoice_serial_num,
                    'redInvoiceNo' => $invoiceRedConfirm->red_invoice_number,
                ],
                'describe' => '开票成功',
            ]);
        } elseif (in_array($redInvoice['status'], [NuoNuoInvoiceResultStatusConstant::FAIL,
            NuoNuoInvoiceResultStatusConstant::SUCCESS_BUT_SIGNATURE_FAIL])) {
            //开票失败
            $describe = $redInvoice['status'] == NuoNuoInvoiceResultStatusConstant::FAIL ? '开票失败' : '开票成功签章失败';
            $methodResult = json_encode([
                'code' => NuoNuoConstants::SERVICE_INVOICE_FAIL,
                'result' => [],
                'describe' => $describe,
            ]);
        } elseif (in_array($redInvoice['status'], [NuoNuoInvoiceResultStatusConstant::INVOICING,
            NuoNuoInvoiceResultStatusConstant::SUCCESS_IN_SIGNATURE])) {
            //开票中状态
            $methodResult = json_encode([
                'code' => NuoNuoConstants::SERVICE_INVOICING,
                'result' => [],
                'describe' => '发票开具中',
            ]);
        } else {
            $methodResult = json_encode([
                'code' => NuoNuoConstants::SERVICE_INVOCIE_NULLIFY,
                'result' => [],
                'describe' => '发票已作废',
            ]);
        }

        return $methodResult;
    }

    /**
     * @param $redConfirmInfo InvoiceRedConfirm
     * @param $applyType int
     * @param $applyId int
     * @return void
     */
    public function cancelInvoiceRedConfirmAfter(InvoiceRedConfirm $redConfirmInfo, int $applyType, int $applyId): void
    {
        $statusDes = InvoiceRedConfirmStatus::getDes($redConfirmInfo->bill_status);
        $description = "红字确认单编号：{$redConfirmInfo->red_bill_no},状态：$statusDes,撤销红字确认单成功";
        if ($applyType == 1) {
            FinanceTicketHistory::query()->insert([
                'applyid' => $applyId,
                'postnode' => $description,
                'postime' => time(),
            ]);
        } else {
            AccountCouponTicketHistory::query()->insert([
                'cticketid' => $applyId,
                'postnode' => $description,
                'postime' => time(),
            ]);
        }
    }


    /**
     * @param InvoiceRedConfirm $redConfirm
     * @param NuoNuoRequestDTO $nuoNuoRequestDTO
     * @param int $billStatus 当前红字确认单状态
     * @param int $oldBillStatus
     * @return InvoiceRedConfirm
     * @throws NuoNuoException
     */
    public function cancelInvoiceRedConfirm(InvoiceRedConfirm $redConfirm,NuoNuoRequestDTO $nuoNuoRequestDTO
        , int $billStatus, int $oldBillStatus): InvoiceRedConfirm
    {
        //当前为申请状态 且查询出来的结果为一方入账且需要确认时 那么需要撤销红字确认单
        if ($oldBillStatus == InvoiceRedConfirmStatus::APPLYING
            && in_array($billStatus, [InvoiceRedConfirmStatus::SELLER_PENDING_CONFIRMATION,
                InvoiceRedConfirmStatus::BUYER_PENDING_CONFIRMATION])) {
            $methodResult = $this->cancelRedConfirm($nuoNuoRequestDTO);
            $redConfirm->refreshBillStatus(InvoiceRedConfirmStatus::INVALIDATED_BY_INITIATOR,1);
            $statusDes = InvoiceRedConfirmStatus::getDes($billStatus);
            $description = "红字确认单编号：{$redConfirm->red_bill_no},状态：$statusDes,撤销红字确认单成功";
            if ($redConfirm->applytype == InvoiceConstants::TICKET_APPLY_TYPE_ORDER) {
                FinanceTicketHistory::query()->insert([
                    'applyid' => $redConfirm->applyid,
                    'postnode' => $description,
                    'postime' => time(),
                ]);
            } elseif($redConfirm->applytype == InvoiceConstants::TICKET_APPLY_TYPE_COUPON) {
                AccountCouponTicketHistory::query()->insert([
                    'cticketid' => $redConfirm->applyid,
                    'postnode' => $description,
                    'postime' => time(),
                ]);
            }
            $this->markCancelRedConfirm($redConfirm->applytype,$redConfirm->applyid);
        }
        return $redConfirm;
    }

    /**
     * @throws NuoNuoException
     */
    public function openReceipt(OpenReceiptNuoNuoDTO $openReceiptNuoNuoDTO, OpenReceiptDTO $openReceiptDTO)
    {
        $nuonuoRequestDTO = new NuoNuoRequestDTO();
        $nuonuoRequestDTO->setMethod(NuoNuoConstants::METHOD_OPEN_RECEIPT);
        $nuonuoRequestDTO->setApplyId($openReceiptDTO->applyId);
        $nuonuoRequestDTO->setToken($openReceiptDTO->token);
        $nuonuoRequestDTO->setApplyType($openReceiptDTO->applyType);
        $nuonuoRequestDTO->setSalerTaxNum($openReceiptDTO->salerTaxNumber);
        $nuonuoRequestDTO->setBody($openReceiptNuoNuoDTO->toMap());
        return $this->baseNuoNuoRequest($nuonuoRequestDTO);
    }

    /**
     * @throws NuoNuoException
     */
    public function cancelReceipt(CloseReceiptDTO $closeReceiptDTO,string $accessToken,string $taxNumber)
    {
        $nuonuoRequestDTO = new NuoNuoRequestDTO();
        $nuonuoRequestDTO->setMethod(NuoNuoConstants::METHOD_CANCEL_RECEIPT);
        $nuonuoRequestDTO->setApplyId($closeReceiptDTO->applyId);
        $nuonuoRequestDTO->setToken($accessToken);
        $nuonuoRequestDTO->setApplyType($closeReceiptDTO->applyType);
        $nuonuoRequestDTO->setSalerTaxNum($taxNumber);
        $body = [
            'orderno' => $closeReceiptDTO->orderno,
            'electronicNo' => $closeReceiptDTO->electronicNo,
            'invalidReasonType' => $closeReceiptDTO->invalidReasonType,
        ];
        if (!empty($closeReceiptDTO->invalidReason)) {
            $body['invalidReason'] = $closeReceiptDTO->invalidReason;
        }
        $nuonuoRequestDTO->setBody($body);
        return $this->baseNuoNuoRequest($nuonuoRequestDTO);
    }

    /**
     * @throws NuoNuoException
     */
    public function queryReceipt(array $body, string $accessToken,string $taxNumber)
    {
        $nuonuoRequestDTO = new NuoNuoRequestDTO();
        $nuonuoRequestDTO->setMethod(NuoNuoConstants::METHOD_QUERY_RECEIPT);
        $nuonuoRequestDTO->setToken($accessToken);
        $nuonuoRequestDTO->setBody($body);
        $nuonuoRequestDTO->setSalerTaxNum($taxNumber);
        return $this->baseNuoNuoRequest($nuonuoRequestDTO);
    }
}
