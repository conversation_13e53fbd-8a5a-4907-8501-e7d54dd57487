<?php

namespace App\Services\AfterSale;

use App\Entities\ProblemTypeListDTO;
use App\Exceptions\BusinessException;
use App\Models\CrmAccount;
use App\Models\Job;
use App\Models\OrderAllot;
use App\Models\OrderInfo;
use App\Models\TicketProblemType;
use App\Models\TicketSolution;
use App\Services\AfterSale\LevelConfirm\ConfirmLevelByAccountTag;
use App\Services\AfterSale\LevelConfirm\ConfirmLevelByOrder;
use App\Services\AfterSale\LevelConfirm\ConfirmLevelByProblem;
use App\Services\AfterSale\LevelConfirm\ConfirmLevelBySource;
use App\Services\AfterSale\LevelConfirm\LevelMediator;
use App\Services\Impl\OrderServiceImpl;
use App\Services\Order\OrderService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pipeline\Pipeline;
use Yanqu\YanquPhplib\Openapi\Account\GetAchievementHeadParam;
use Yanqu\YanquPhplib\Openapi\AfterSale\Constants\ProblemSceneTypeConstants;
use Yanqu\YanquPhplib\Openapi\TicketPm\Constants\ProblemHandlerTypeConstants;
use Yanqu\YanquPhplib\Openapi\Account\AchievementClient;
use Exception;
use Yanqu\YanquPhplib\YqLog\YqLog;

class ProblemTypeService {
    protected $noReasonRetestConfig =  [
        4 => [
            'buffet_address_id' => 4,
            'start_time' => '',
            'end_time'   => '',
            'buffet_id_list' => [29,36],
        ],
        6 => [
            'buffet_address_id' => 6,
            'start_time' => '',
            'end_time'   => '',
            'buffet_id_list' => [32],
        ],
        8 => [
            'buffet_address_id' => 8,
            'start_time' => '',
            'end_time'   => '',
            'buffet_id_list' => [
                36,293,
            ]
        ], //杭州,
        9 => [
            'buffet_address_id' => 9,
            'start_time' => '',
            'end_time'   => '',
            'buffet_id_list' => [
                32,
            ]
        ], //广州
        17 => [
            'buffet_address_id' => 17,
            'start_time' => '',
            'end_time'   => '',
            'buffet_id_list' => [
                29,
            ]
        ], //成都
        44 => [
            'buffet_address_id' => 44,
            'start_time' => '',
            'end_time'   => '',
            'buffet_id_list' => [32],
        ],
        5 => [
            'buffet_address_id' => 5,
            'start_time' => '',
            'end_time'   => '',
            'buffet_id_list' => [36],
        ],
        26 => [
            'buffet_address_id' => 26,
            'start_time' => '',
            'end_time'   => '',
            'buffet_id_list' => [36],
        ],
    ];

    public function getList (ProblemTypeListDTO $problemTypeListDTO) {
        $query = TicketProblemType::query();
        if ($problemTypeListDTO->getWithBank()) {
            $query->with(['ticketBank:problem_type_id,content,title']);
        }
        $enableSolutionMap = TicketSolution::where('isvoid', 0)
            ->where('state',1)
            ->get()->keyBy('solutionid');
        $problemTypeList =  $query
            ->where('isold', 0)
            ->when(!$problemTypeListDTO->getAll(), function (Builder $query) {
                $query->where('scene', 0);
            })
            //业务线查询
            ->when(!empty($problemTypeListDTO->getOid()), function (Builder $query) use ($problemTypeListDTO) {
                $category = app(OrderService::class)->getOrderCategory($problemTypeListDTO->getOid());
                if (!empty($category)) {
                    $query->whereRaw(
                        "(FIND_IN_SET($category,category_id) or category_id is null or category_id = '')"
                    );
                }
            })
            ->when(!is_null($problemTypeListDTO->getState()), function (Builder $query) use ($problemTypeListDTO) {
                $query->where('state', $problemTypeListDTO->getState());
            })
            //是否复测开启
            ->when(!is_null($problemTypeListDTO->getRetestOn()), function (Builder $query) use ($problemTypeListDTO) {
                $query->where('retest_on', $problemTypeListDTO->getRetestOn());
            })
            //是否意见反馈
            ->when(!is_null($problemTypeListDTO->getFeedbackOn()), function (Builder $query) use ($problemTypeListDTO) {
                $query->where('feedback_on', $problemTypeListDTO->getFeedbackOn());
            })
            //是否供应商开启
            ->when(!is_null($problemTypeListDTO->getSupplierOn()), function (Builder $query) use ($problemTypeListDTO) {
                $query->where('supplier_on', $problemTypeListDTO->getSupplierOn());
            })
            //问题类型查询
            ->when(!is_null($problemTypeListDTO->getTypeName()), function (Builder $query) use ($problemTypeListDTO) {
                $query->where('typename', 'like', "%{$problemTypeListDTO->getTypeName()}%");
            })
            ->select(['typeid', 'typename', 'default_handler_type','solutions'])
            ->get();
        $finalProblemTypeList = [];

        if (!empty($problemTypeListDTO->getOid())) {
            $scenes = $this->getProblemScene($problemTypeListDTO->getOid());
            if (!empty($scenes)) {
                $problemTypeSceneList = TicketProblemType::query()
                    ->where('isold', 0)
                    ->whereIn('scene', $scenes)
                    ->select(['typeid', 'typename', 'default_handler_type','solutions'])
                    ->get();
                //合并两个collection
                if ($problemTypeSceneList->isNotEmpty()) {
                    //至少存在一个方案启用时,才进入
                    foreach ($problemTypeSceneList as $problemType) {
                        if (empty($problemType->solutions)) {
                            continue;
                        }
                        $solutions = explode(',', $problemType->solutions);

                        foreach ($solutions as $solutionId) {
                            if (isset($enableSolutionMap[$solutionId])) {
                                $finalProblemTypeList[] = $problemType->toArray();
                                break;
                            }
                        }
                    }
                }
            }
        }

        //至少存在一个方案启用时,才进入
        foreach ($problemTypeList as $problemType) {
            if (empty($problemType->solutions)) {
                continue;
            }
            $solutions = explode(',', $problemType->solutions);

            foreach ($solutions as $solutionId) {
                if (isset($enableSolutionMap[$solutionId])) {
                    $finalProblemTypeList[] = $problemType->toArray();
                    break;
                }
            }
        }

        return $finalProblemTypeList;
    }

    /**
     * 检查问题场景,此处可优化
     *
     * @param $oid
     * @return array
     */
    public function getProblemScene ($oid) {
       $scene = [];
        if ($this->checkNoReasonRetest($oid)) {
            $scene[] = ProblemSceneTypeConstants::NO_REASON_RETEST;
        }
        return $scene;
    }

    private function checkNoReasonRetest ($oid) {
        $order =  OrderInfo::query()->where('oid', $oid)
            ->select(['confirmtime','oid','addressuuid','addtime'])->first();

        if (empty($order->buffetAddress)) {
            return false;
        }

        //相关办事处地址id
        if (empty($order->buffetAddress->addressid)
            || !isset($this->noReasonRetestConfig[$order->buffetAddress->addressid])) {
            return false;
        }

        $config = $this->noReasonRetestConfig[$order->buffetAddress->addressid];

        //生产环境灰度时间
        if (!empty($config['start_time']) && !empty($config['end_time'])) {
            $starTime = Carbon::parse($config['start_time'])->timestamp;
            $endTime  = Carbon::parse($config['end_time'])->timestamp;
            if ($order->addtime < $starTime || $order->addtime > $endTime) {
                return false;
            }
        }
        $enableBuffetList = $config['buffet_id_list'];

        if (empty($enableBuffetList)) {
            return false;
        }
        if (empty($order)) {
            return false;
        }
        $buffetOrder = $order->buffetOrder()->select(['orderid','buffetid'])->first();
        if (empty($buffetOrder)) {
            return false;
        }

        //限制订单完成时间7天以内
        if (empty($order->confirmtime)) {
            return false;
        }

        if (Carbon::parse($order->confirmtime)->isBefore(Carbon::now()->subDays(7))) {
            return false;
        }
        if (!in_array($buffetOrder->buffetid,  $enableBuffetList)) {
            return false;
        }

        return true;
    }

    /**
     * 获取问题处理人
     *
     * @param $problemTypeId
     * @param $oid
     * @param bool $withQRCode
     * @return array
     * @throws BusinessException
     * @throws \Yanqu\YanquPhplib\Exception\CurlException
     */
    public function getProblemHandler ($problemTypeId, $oid, bool $withQRCode = false) {
        $problemType = TicketProblemType::query()
            ->where('typeid', $problemTypeId)
            ->select(['typeid', 'typename', 'default_handler_type'])
            ->first();
        if (empty($problemType)) {
            throw new BusinessException('问题类型不存在');
        }
        $order = OrderInfo::query()->where('oid', $oid)->select(['oid', 'accountid'])->first();
        $handlerId = $this->getProblemHandlerByOrder($problemType, $order);
        $res['handler'] = $handlerId;

        $businessId = 14;
        if ($withQRCode) {
            try {
                $qrCode = app(OrderServiceImpl::class)->createQRCodeWithParams([
                    'bussiness_id' => 14,
                    'state'        => $businessId.'-'.$handlerId,
                    'remark'       => '售后扫码',
                    'crm_id'       => $handlerId,
                ]);
            } catch (Exception $e) {
                YqLog::logger('openapi:get-handler-qrcode')
                    ->error('生成二维码失败', ['error' => $e->getMessage()]);
                $qrCode = '';
            }
            $res['qrcode'] = $qrCode;
        }

        return $res;
    }

    /**
     * 获取问题处理人
     *
     * @param TicketProblemType $problemType
     * @param OrderInfo $orderInfo
     * @param $ip
     * @return int|mixed
     * @throws BusinessException
     * @throws \Yanqu\YanquPhplib\Exception\CurlException
     */
    public function getProblemHandlerByOrder (TicketProblemType $problemType, OrderInfo $orderInfo, $ip = '') {
        if (empty($problemType)) {
            throw new BusinessException('问题类型不存在');
        }

        //品牌经理
        if ($problemType->default_handler_type == ProblemHandlerTypeConstants::BRAND_MANAGER_TYPE) {
            $brandManager = AchievementClient::getAchievementHead((new GetAchievementHeadParam())
                ->setAccountId($orderInfo->accountid)
                ->setPostIp($ip)
            );
            if (!empty($brandManager)) {
                return $brandManager;
            }
        }

        //项目经理
        $allotInfo = OrderAllot::query()
            ->where('oid', $orderInfo->oid)
            ->select(['oid', 'pickup'])->first();

        if (!empty($allotInfo)) {
            $accountId = CrmAccount::where('accountno', $allotInfo->pickup)
                ->where('status', 100)
                ->value('accountid');
        }

        if (!empty($accountId)) {
            return $accountId;
        }

        //区域副经理
        $viceJobId = Job::where('jobname', '区域副经理')->value('jobid');
        $jobId = Job::where('jobname', '区域经理')->value('jobid');
        $area = OrderInfo::where("oid", $orderInfo->oid)->value('useruuid');

        $viceLeader = CrmAccount::where('addressid', $area)
            ->where('addressid', $viceJobId)
            ->where('status', '!=', 0)
            ->where('dismissdate', '')
            ->where('isvoid', 0)
            ->value('accountid');

        if (!empty($viceLeader)) {
            return $viceLeader;
        }
        //没有区域副经理就取区域经理
        $leader = CrmAccount::where('addressid', $area)
            ->where('jobid', $jobId)
            ->where('status', '!=', 0)
            ->where('dismissdate', '')
            ->where('isvoid', 0)
            ->value('accountid');
        if (!empty($leader)) {
            return $leader;
        }

        return 1009;
    }


    /**
     * 获取严重程度等级
     *
     * @param LevelMediator $levelMediator
     * @return int
     */
    public function getLevel (LevelMediator $levelMediator) {
        app(Pipeline::class)->send($levelMediator)
            ->through([
                ConfirmLevelBySource::class, //根据来源判断
                ConfirmLevelByAccountTag::class, //根据客户标签判断
                ConfirmLevelByOrder::class, //根据订单判断
                ConfirmLevelByProblem::class, //根据问题紧急程度判断
            ])->thenReturn();
        return $levelMediator->getLevel();
    }
}