<?php

namespace App\Services\AfterSale\Traits;

use App\Entities\AfterSale\DissentDTO;
use App\Entities\AfterSale\ProblemProcessDTO;
use App\Entities\AfterSale\TicketPmAfterSaleDTO;
use App\Entities\AfterSale\TicketPmDTO;
use App\Exceptions\BusinessException;
use App\Models\OrderDissent;
use App\Models\TicketProblemType;
use App\Services\AfterSale\AfterSaleService;
use App\Services\AfterSale\LevelConfirm\LevelMediator;
use App\Services\AfterSale\ProblemProcessService;
use App\Services\AfterSale\ProblemTypeService;
use App\Services\AfterSale\TicketPmService;
use App\Services\TodoService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\Openapi\AfterSale\Constants\ProblemHandleTypeConstants;
use Yanqu\YanquPhplib\Openapi\AfterSale\Constants\ProblemLevelConstants;
use Yanqu\YanquPhplib\Openapi\AfterSale\Constants\ProblemProcessTypeConstants;
use Yanqu\YanquPhplib\Openapi\AfterSale\Constants\ProblemSourceConstants;
use Yanqu\YanquPhplib\Openapi\TicketPm\Constants\TicketPmTypeConstants;

trait ProblemHandle {


    /**
     * 选择异议处理方式(目前有创建工单, 创建待办)
     * @param TicketProblemType $type
     * @param OrderDissent $dissent
     * @param DissentDTO $dissentDTO
     * @throws BindingResolutionException
     * @throws BusinessException
     * @throws \Throwable
     * @throws CurlException
     */
    public function handleDissentProblem (TicketProblemType $type, OrderDissent $dissent, DissentDTO $dissentDTO) {

        //获取处理人
        if ($dissentDTO->getHandler() > 0) {
            $handlerId = $dissentDTO->getHandler();
        } else {
            $handlerId = app(ProblemTypeService::class)->getProblemHandlerByOrder($type,
                $dissent->order, $dissent->postip);
        }

        //获取紧急程度
        $levelMediator = (new LevelMediator())
            ->setOrder($dissent->order)
            ->setProblemType($type);
        $level = app(ProblemTypeService::class)->getLevel($levelMediator);

        if ($type->handle_type == ProblemHandleTypeConstants::TYPE_TICKET_PM) {
            //工单可以单独创建, 所以目前跟进记录创建内置到了工单内部，这是一个待优化点
            $ticketPm = $this->createTicketPmByDissent(
                $type,
                $dissent,
                $handlerId,
                $dissentDTO->getOperatorId(),
                $level
            );
        }

        if ($type->handle_type == ProblemHandleTypeConstants::TYPE_TODO) {
            $msgUuid = $this->createTodoByDissent($type, $dissent, $handlerId, $level);
            $messageId = app(TodoService::class)->getMessageIdByUuid($msgUuid);

            //合理的逻辑应该是整个所有的售后跟进创建都应该在一个地方，工单跟待办只是其中一种处理方式
            //逐渐把老的异议单整合到新的问题跟进记录中，目前老的OrderDissent异议单与官网的售后模块强耦合，所以这边只是去双写
            //最终目标整合所有问题来源 争议 差评 投诉 意见反馈等到问题跟进记录中
            //争议待办增加问题(售后)跟进记录
            app(ProblemProcessService::class)->createProblemProcess(
                (new ProblemProcessDTO())
                    ->setProblemHandleId($messageId)
                    ->setProblemHandleType(ProblemHandleTypeConstants::TYPE_TODO)
                    ->setContent($dissentDTO->getDesc())
                    ->setHandler($handlerId)
                    ->setLevel($level)
                    ->setProblemTypeId($type->typeid)
                    ->setSource(ProblemSourceConstants::SOURCE_USER_DISSENT)
                    ->setType(ProblemProcessTypeConstants::TYPE_DISSENT)
                    ->setOid($dissent->orderid)
                    ->setAttachments($dissentDTO->getAttachments())
                    ->setIsVisitableToCustomer($dissentDTO->getIsVisibleToCustomer())
            );
        }

        //没有跟进类型时特殊处理(线上不应该有这样的数据)
        if (empty($type->handle_type)) {
            app(ProblemProcessService::class)->createProblemProcess(
                (new ProblemProcessDTO())
                    ->setProblemHandleId(0)
                    ->setProblemHandleType(0)
                    ->setContent($dissentDTO->getDesc())
                    ->setHandler($handlerId)
                    ->setLevel($level)
                    ->setProblemTypeId($type->typeid)
                    ->setSource(ProblemSourceConstants::SOURCE_USER_DISSENT)
                    ->setType(ProblemProcessTypeConstants::TYPE_DISSENT)
                    ->setOid($dissent->orderid)
                    ->setAttachments($dissentDTO->getAttachments())
            );
        }

        /**
         * 创建售后
         */
        if ($dissentDTO->getIsVisibleToCustomer()) {
            app(AfterSaleService::class)->createAfterSale((new TicketPmAfterSaleDTO())
                ->setOid($dissentDTO->getOid())
                ->setTicketPmId(empty($ticketPm) ? 0 : $ticketPm->ticketpmid)
                ->setProblemTypeId($dissentDTO->getProblemTypeId())
                ->setTodoMsgUuid($msgUuid ?? '')
                ->setDesc($dissentDTO->getDesc())
                ->setAttachments($dissentDTO->getAttachments())
            );
        }
    }

    /**
     * @throws \Throwable
     * @throws BusinessException
     * @throws BindingResolutionException
     */
    private function createTicketPmByDissent (TicketProblemType $type, OrderDissent $dissent, $handlerId,
                                                                $operatorId, $level) {
        if ($dissent->post_channel != 0) {
            //官网小程序侧发起
            $source = ProblemSourceConstants::SOURCE_USER_DISSENT;
        } else {
            //crm侧发起
            $source = ProblemSourceConstants::SOURCE_PM;
        }

        if (!empty($operatorId) && $operatorId < 0) {
            $operatorId = 0;
        }

        return app(TicketPmService::class)->createPmTicket(
            (new TicketPmDTO())
                ->setOid($dissent->orderid)
                ->setIsVisibleToCustomer($dissent->is_visible_to_customer)
                ->setTicketType(TicketPmTypeConstants::TYPE_DISSENT)
                ->setPickUp($handlerId)
                ->setSource($source)
                ->setTypeId($type->typeid)
                ->setCreator($operatorId)
                ->setHandler($handlerId)
                ->setFeedback($dissent->content)
                ->setLevel($level)
        );
    }

    private function createTodoByDissent (TicketProblemType $type, OrderDissent $dissent, $handlerId, $level) {

        $levelName = ProblemLevelConstants::MAP[$level];

        $otherId = $dissent->orderid;
        $categoryName = 'order-dissent';
        $url = config('constants.crm_v2_url')."/#/crm/my?categoryname={$categoryName}&otherid={$otherId}";
        $detailUrl = config('constants.crm_url')."/Order/OrderInfo.html?oid={$otherId}";
        $content = "【争议待办】订单编号：{$dissent->order->osn}，下单商品：{$dissent->order->productname}，".
            "用户{$dissent->order->accountInfo->contacter}发起争议需您处理，".
            "问题类型：{$type->typename}，".
            "紧急程度：{$levelName}，请及时跟进处理。".
            "[立即处理]({$url})";

        if ($dissent->is_visible_to_customer) {
            $tip = '处理方案将同步用户，请谨慎填写!';
        } else {
            $tip = '处理方案用户不可见';
        }

        return app(TodoService::class)->sendWechatMessage(
            [$handlerId],
            $content,
            $categoryName,
            [
                'pm_url'            => $detailUrl,
                'dissent_id'        => $dissent->dissentid,
                'todo_record_tip'   => $tip,
                'problem_type_name' => $type->typename,
                'problem_type_id'   => $type->typeid,
                'handler'           => $handlerId,
            ],
            $dissent->order->oid,
            $url,
            $dissent->postime + 172800
        );
    }

}