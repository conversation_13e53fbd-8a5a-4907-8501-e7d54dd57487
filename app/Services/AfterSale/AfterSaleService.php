<?php

namespace App\Services\AfterSale;

use App\Entities\AfterSale\TicketPmAfterSaleDTO;
use App\Entities\AfterSale\TicketPmHandleDTO;
use App\Models\AfterSaleAttachments;
use App\Models\TicketPm;
use App\Models\TicketPmAftersale;
use Carbon\Carbon;

class AfterSaleService {
    public function createAfterSale (TicketPmAfterSaleDTO $afterSaleDTO) {
        $saleId = TicketPmAftersale::insertGetId([
            'oid'             => $afterSaleDTO->getOid(),
            'ticketpmid'      => $afterSaleDTO->getTicketPmId(),
            'problem_type_id' => $afterSaleDTO->getProblemTypeId(),
            'todo_msg_uuid'   => $afterSaleDTO->getTodoMsgUuid(),
            'desc'            => $afterSaleDTO->getDesc(),
            'isdealing'       => 1, // 目前在用户侧默认就是处理中，所以售后单创建时置为处理中
            'dealingtime'     => Carbon::now()->toDateTimeString(),
        ]);
        foreach ($afterSaleDTO->getAttachments() as $url) {
            AfterSaleAttachments::insert([
                'saleid' => $saleId,
                'url'    => $url,
            ]);
        }
    }

    /**
     * 检查工单是否已有工作人员介入售后
     *
     * @param TicketPm $ticketPm
     * @return bool
     */
    public function checkAfterSaleDealing (TicketPm $ticketPm) {
        $afterSale = TicketPmAftersale::query()
            ->where('ticketpmid', $ticketPm->ticketpmid)->first();
        if (empty($afterSale)) {
            return false;
        }
        if (empty($afterSale->dealingtime) || $afterSale->isdealing == 0) {
            return false;
        }
        return true;
    }

    /**
     * 标识工作人员已介入
     *
     * @param TicketPm $ticketPm
     * @param TicketPmHandleDTO $handleDTO
     * @return void
     */
    public function setAfterSaleDealing (TicketPm $ticketPm, TicketPmHandleDTO $handleDTO) {
        $afterSale = TicketPmAftersale::query()
            ->where('ticketpmid', $ticketPm->ticketpmid)->first();
        if (empty($afterSale)) {
            $afterSale = new TicketPmAftersale();
            $afterSale->ticketpmid = $ticketPm->ticketpmid;
            $afterSale->oid = $ticketPm->oid;
            $afterSale->dealingtime = Carbon::now()->toDateTimeString();
            $afterSale->isdealing = 1;
        }
        if (empty($afterSale->dealingtime) || $afterSale->isdealing == 0) {
            $afterSale->dealingtime = Carbon::now()->toDateTimeString();
            $afterSale->isdealing = 1;
        }
        $afterSale->detail = $handleDTO->getContent();
        $afterSale->remark = $handleDTO->getRemark();
        $afterSale->save();
    }
}