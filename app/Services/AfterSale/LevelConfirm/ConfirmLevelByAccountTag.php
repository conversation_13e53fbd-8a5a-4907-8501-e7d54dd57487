<?php

namespace App\Services\AfterSale\LevelConfirm;

use App\Models\TicketPm;
use Closure;
use Yanqu\YanquPhplib\Openapi\Account\Constants\ImportantLevelConstants;
use Yanqu\YanquPhplib\Openapi\TicketPm\Constants\TicketPmLevelConstants;
/**
 * 根据账号标签确定工单紧急程度
 */
class ConfirmLevelByAccountTag {

        public function handle(LevelMediator $levelMediator, Closure $next)  {
            // 如果工单没有关联订单，直接跳过
            $order = $levelMediator->getOrder();

            if (empty($order) || empty($order->account)) {
                return $next($levelMediator);
            }

            //谨慎对接用户工单变为紧急工单
            if ($order->account->importantlevel == ImportantLevelConstants::DISCREET_USER) {
                return $levelMediator->setLevel(TicketPmLevelConstants::LEVEL_EMERGENCY);
            }

            return $next($levelMediator);
        }
}