<?php

namespace App\Services\Order\OrderSampling;

use App\Entities\OrderSampling\UpdateSamplingInfoDTO;
use App\Exceptions\BusinessException;
use App\Models\OrderInfo;
use App\Models\Sampling;
use Yanqu\YanquPhplib\Openapi\Order\Constants\SamplingStatusConstant;
use Yanqu\YanquPhplib\Openapi\OrderSampling\Constants\SamplingWayConstants;

trait SamplingBuild {

    /**
     * @param UpdateSamplingInfoDTO $dto
     * @param Sampling $sampling
     * @param bool $needAllocate
     * @return Sampling
     * @throws BusinessException
     */
    public function cover (UpdateSamplingInfoDTO $dto, Sampling $sampling, $needAllocate = false) {
        $deliverAddress = $dto->getLouzhuang().$dto->getLouceng().$dto->getLoushi();
        $sampling->univscode = $dto->getUniversityCode();
        $sampling->province = $dto->getProvince();
        $sampling->city = $dto->getCity();
        $sampling->louzhuang = $dto->getLouzhuang();
        $sampling->louceng = $dto->getLouceng();
        $sampling->loushi = $dto->getLoushi();
        $sampling->deliveraddress = $deliverAddress;
        $sampling->deliverconsignee = $dto->getDeliverConsignee();
        $sampling->delivermobile = $dto->getDeliverMobile();
        $sampling->campuid = $dto->getCampusId();
        $sampling->building_id = $dto->getBuildingId();
        $sampling->supplement_sample_number = $dto->getNumber();
        $sampling->supplement_reason = $dto->getReason();
        $sampling->paystatus = 1;
        if (!empty($dto->getDeliverDay())) {
            $sampling->deliverday = $dto->getDeliverDay();
            if ($dto->getDeliverDay() != self::NOT_SAMPLING) {
                $tmpArrayDeliverDay = explode('（', $sampling->deliverday);
                date_default_timezone_set('Asia/Shanghai');
                $deliverTime = strtotime($tmpArrayDeliverDay[0]." ".date("H:m:s", time()));
                $sampling->delivertime = $deliverTime;
            } else {
                $sampling->delivertime = 0;
            }
        }
        if (!empty($dto->getRemindDay())) {
            $sampling->remind_day = $dto->getRemindDay();
        }


        if ($needAllocate) {
            $allocateDeliverId = $dto->getDeliverId();
            //只有选择了时间，才分配取样员，暂不取样不分配取样员
            if ($sampling->deliverday != self::NOT_SAMPLING) {
                //获取可以分配的取样人员
                $sampling->deliverid = app(DeliverService::class)->getAllocateDeliver(
                    $sampling->univscode,
                    $sampling->campuid,
                    $sampling->deliverday,
                    $allocateDeliverId,
                    $sampling->accountid,
                    $sampling->building_id
                );
            }
        }

        return $sampling;
    }
    /**
     * 获取上门取样信息
     * @param $oid
     * @param int $samplingId
     * @return Sampling
     * @throws BusinessException
     */
    protected function getSamplingAutoCheck ($oid, $samplingId = 0) {
        //指定了取样id则根据取样id查询
        if (!empty($samplingId)) {
            $sampling = Sampling::query()
                ->where('samplingid', $samplingId)
                ->where('isvoid', 0)
                ->first();
        } else {
            $sampling = Sampling::query()
                ->where('orderid', $oid)
                ->where('type',0)
                ->where('isvoid', 0)
                ->orderBy('samplingid')
                ->first();
        }

        if (empty($sampling)) {
            throw new BusinessException('没有上门取样信息');
        }

        //判断订单已取样
        if ($sampling->status != SamplingStatusConstant::STATUS_WAIT_SAMPLING) {
            throw new BusinessException('该订单已取样，不支持修改上门取样信息');
        }

    if ($sampling->type == 1 && !empty($sampling->deliverday)) {
        throw new BusinessException('补寄信息已完善，请刷新');
    }

        return $sampling;
    }

    /**
     * 判断是否需要重新分派取样员
     * @param UpdateSamplingInfoDTO $dto
     * @param Sampling $sampling
     * @return bool
     */
    protected function checkNeedAllocate (UpdateSamplingInfoDTO $dto, Sampling $sampling): bool {
        if ($dto->getProvince() == $sampling->province
            && $dto->getCity() == $sampling->city
            && $dto->getUniversityCode() == $sampling->univscode
            && $dto->getCampusId() == $sampling->campuid
            && $dto->getBuildingId() == $sampling->building_id
            && $dto->getDeliverDay() == $sampling->deliverday
            && $dto->getDeliverDay() != self::NOT_SAMPLING
        ) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * @param $oid
     * @param $accountId
     * @param $operateFrom
     * @param $operatorId
     * @return OrderInfo|null
     * @throws BusinessException
     */
    protected function checkOrderAuthAndGetOrder ($oid, $accountId, $operateFrom, $operatorId = 0) {
        $order = OrderInfo::query()->where('oid', $oid)
            ->select(['oid', 'orderstate', 'deliverinfo', 'samplingway', 'accountid'])
            ->first();
        if (empty($order)) {
            throw new BusinessException('订单不存在');
        }

        if ($accountId == 1 && $order->accountid != $accountId) {
            throw new BusinessException('不是你的订单无法操作');
        }

        if ($operateFrom == 2 && empty($operatorId)) {
            throw new BusinessException('操作人不能为空');
        }

        return $order;
    }

    /**
     * 根据取样单信息确认订单中的取样信息
     * @param OrderInfo $order
     * @param Sampling $sampling
     * @return OrderInfo $order
     */
    protected function confirmOrderDeliverInfoBySampling (OrderInfo $order, Sampling $sampling) {
        $deliverInfo = json_decode($order->deliverinfo);
        if (empty($deliverInfo)) {
            $deliverInfo = new \stdClass();
        }
        $deliverInfo->delivertime = $sampling->delivertime;
        $deliverInfo->deliverday = $sampling->deliverday;
        $deliverInfo->deliverid = $sampling->deliverid;
        $deliverInfo->deliverconsignee = $sampling->deliverconsignee;
        $deliverInfo->delivermobile = $sampling->delivermobile;
        $deliverInfo->isdeliver = $sampling->isdeliver;
        $order->deliverinfo = json_encode($deliverInfo);
        return $order;
    }
}