<?php

namespace App\Services\Order\OrderSampling;

use App\Exceptions\BusinessException;
use App\Models\AccountDeliveryCampusLog;
use App\Models\DeliverUniversity;
use App\Models\Sampling;
use Illuminate\Support\Carbon;

trait SamplingAddressManagement {

    /**
     * 保存取样地址
     * @param int|null $id        地址ID，0表示新增
     * @param array $position     地址信息
     * @param int|null $accountID 用户ID
     * @return bool
     * @throws BusinessException
     */
    public function saveAddress (?int $id, array $position, ?int $accountID): bool {
//        $this->validateAddressPosition($position);

        $addressData = $this->prepareAddressData($position);

        if (!empty($id)) {
            $this->updateAddress($id, $addressData);
        } else {
            $id = $this->createAddress($addressData, $accountID);
        }

        if ($position['is_default'] == 1) {
            $this->setDefaultSamplingAddress($id);
        }

        return true;
    }

    /**
     * 更新地址
     * @param int $id
     * @param array $addressData
     * @return bool
     */
    protected function updateAddress (int $id, array $addressData): bool {
        return AccountDeliveryCampusLog::where('id', $id)->update($addressData);
    }

    /**
     * 创建新地址
     * @param array $addressData
     * @param int $accountID
     * @return int
     */
    protected function createAddress (array $addressData, int $accountID): int {
        $addressData['account_id'] = $accountID;
        return AccountDeliveryCampusLog::create($addressData)->id;
    }

    /**
     * 获取用户取样地址列表
     * @param int $accountID 用户ID
     * @return array
     */
    public function getSamplingAddressList (int $accountID): array {
        return [
            'position_list' => $this->getPositionList($accountID),
        ];
    }

    /**
     * 格式化地址项
     * @param AccountDeliveryCampusLog $item
     * @return array
     */
    private function formatAddressItem (AccountDeliveryCampusLog $item): array {
        return [
            'id'              => $item->id,
            'account_id'      => $item->account_id,
            'university_code' => $item->university_code,
            'campus_id'       => $item->campus_id,
            'name'            => $item->name,
            'mobile'          => $item->mobile,
            'building'        => $item->building,
            'is_default'      => $item->is_default,
            'floor'           => $item->floor,
            'room'            => $item->room,
            'univsname'       => $item->university->univsname ?? '',
            'province'        => $item->university->provinceData->name ?? '',
            'province_id'        => $item->university->provinceData->regionid ?? '',
            'city'            => $item->university->city->name ?? '',
            'city_id'         => $item->university->city->regionid ?? '',
            'campus_name'     => $item->campus->addressname ?? '',
        ];
    }

    /**
     * 获取最近一次订单地址
     * @param int $accountID
     * @return Sampling|null
     */
    private function getLastOrderPosition (int $accountID): ?Sampling {
        return Sampling::with(['university', 'campus'])
            ->join('order as b', 'sampling.orderid', '=', 'b.oid')
            ->where('b.accountid', $accountID)
            ->where('sampling.deliverday','!=','')
            ->where('sampling.isvoid', 0)
            ->orderBy('sampling.samplingid', 'desc')
            ->select([
                'sampling.*',
                'sampling.deliverconsignee as name',
                'sampling.delivermobile as mobile',
                'sampling.louzhuang as building',
                'sampling.louceng as floor',
                'sampling.loushi as room',
            ])
            ->first();
    }

    /**
     * 删除取样地址
     * @param int $id 地址ID
     * @return bool
     */
    public function deleteSamplingAddress (int $id): bool {
        return AccountDeliveryCampusLog::where('id', $id)
            ->update([
                'isvoid'      => 1,
                'update_time' => Carbon::now()->toDateTimeString(),
            ]);
    }

    /**
     * 设置默认取样地址
     * @param int $id 地址ID
     * @throws BusinessException
     */
    public function setDefaultSamplingAddress (int $id): void {
        $addressInfo = AccountDeliveryCampusLog::find($id);

        if (empty($addressInfo)) {
            throw new BusinessException("地址信息有误");
        }

        $accountID = $addressInfo->account_id;

        // 先将该用户所有地址设为非默认
        AccountDeliveryCampusLog::where('account_id', $accountID)
            ->update(['is_default' => 0]);

        // 将指定地址设为默认
        $addressInfo->update(['is_default' => 1]);
    }

    /**
     * 获取地址列表
     * @param int $accountID
     * @return array
     */
    private function getPositionList (int $accountID) {
        return AccountDeliveryCampusLog::with(['university', 'campus'])
            ->where('account_id', $accountID)
            ->where('isvoid', 0)
            ->orderBy("is_default", "desc")
            ->orderBy("id", "desc")
            ->get()
            ->map(function (AccountDeliveryCampusLog $item) {
                return $this->formatAddressItem($item);
            })->toArray();
    }


    /**
     * 验证地址位置是否有效
     * @param array $position
     * @throws BusinessException
     */
    private function validateAddressPosition (array $position): void {
        $delivererList = DeliverUniversity::query()
            ->where('universitycode', $position['university_code'])
            ->where('campuid', $position['campus_id'] ?: 0)
            ->where('isvoid', 0)
            ->get();
        if ($delivererList->isEmpty()) {
            throw new BusinessException("此地址暂不支持上门取样，请选择其他地址或自行寄样");
        }
    }

    /**
     * 准备地址数据
     * @param array $position
     * @return array
     */
    private function prepareAddressData (array $position): array {
        return [
            'university_code' => $position['university_code'],
            'campus_id'       => $position['campus_id'] ?? 0,
            'name'            => $position['name'],
            'mobile'          => $position['mobile'],
            'building'        => $position['building'],
            'is_default'      => $position['is_default'],
            'floor'           => $position['floor'],
            'room'            => $position['room'],
            'isvoid'          => 0,
            'update_time'     => Carbon::now()->toDateTimeString(),
        ];
    }
}