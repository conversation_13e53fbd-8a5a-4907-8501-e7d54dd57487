<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2024 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/4/16 14:18
 */
namespace App\Services\Order\Report\Handler;

interface ReportUploadedHandler
{
    /**
     * 报告上传后的处理
     * @param int $verifyReportId
     * @return mixed
     */
    public function handle(int $verifyReportId);
}