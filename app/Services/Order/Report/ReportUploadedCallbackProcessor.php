<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2024 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/4/16 14:19
 */
namespace App\Services\Order\Report;
use App\Services\Order\Report\Handler\ReportUploadedHandler;
use Exception;
use Yanqu\YanquPhplib\YqLog\YqLog;

class ReportUploadedCallbackProcessor
{
    /**
     * @var ReportUploadedHandler[]
     */
    private $handlers = [];

    public function addHandler(ReportUploadedHandler $handler)
    {
        $this->handlers[] = $handler;
    }

    public function process(int $verifyReportId)
    {
        foreach ($this->handlers as $handler) {
            try {
                $handler->handle($verifyReportId);
            } catch (Exception $e) {
                YqLog::logger('performance:order:report')->error($e, [
                    'handler' => get_class($handler),
                    'verify_report_id' => $verifyReportId,
                ]);
            }
        }
    }
}