<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2024 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/5/7 14:45
 */
namespace App\Services\Order\Allot\Handler;

use App\Entities\Dts\DtsEvent;
use App\Models\OrderAllot;
use App\Services\Order\OrderTestWithAnalysisService;
use App\Traits\Order\TestWithAnalysisOrderNotify;

class TestWithAnalysisOrderAllotNotifyHandler extends OrderAllotDtsCallback
{
    use TestWithAnalysisOrderNotify;

    public function hasTriggered(DtsEvent $dtsEvent)
    {
        return $dtsEvent->getTable() == 'sci_order_allot'
            && (
                $dtsEvent->getOperation() == DtsEvent::OPERATION_INSERT
                || (
                    $dtsEvent->getOperation() == DtsEvent::OPERATION_UPDATE
                    && in_array('sendsupplierid', $dtsEvent->getColumns())
                )
            );
    }

    /**
     * 测试分析一体化订单：分析订单分派通知
     * @param OrderAllot $allotInfo
     * @return bool
     */
    public function execute(OrderAllot $allotInfo)
    {
        $mainOrderId = $allotInfo->orderInfo->mainorderid;
        if (empty($mainOrderId)) {
            // 主单无需处理
            return true;
        }

        // 非测试分析一体化订单不做处理
        $isTestWithAnalysisOrder = app(OrderTestWithAnalysisService::class)->isTestWithAnalysisOrder($mainOrderId);
        if (!$isTestWithAnalysisOrder) {
            return true;
        }

        $orderDetailUrl = config('constants.crm_url') . "/Order/OrderInfo/oid/{$allotInfo->oid}.html";
        $analysisOrderNotifyContent = "【分析订单分派提醒】订单编号：**{$allotInfo->orderInfo->osn}**，"
            . "测试项目：**{$allotInfo->orderInfo->productname}**，已分派给您分析，"
            . "请及时处理。[查看订单]({$orderDetailUrl}) {$orderDetailUrl}";

        return $this->sendAnalysisOrderMessage(
            $allotInfo->sendproductid, $allotInfo->sendsupplierid,
            $analysisOrderNotifyContent
        );
    }
}