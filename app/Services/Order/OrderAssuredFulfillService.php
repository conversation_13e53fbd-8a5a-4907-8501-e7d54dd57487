<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2024 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/4/1 15:36
 */
namespace App\Services\Order;

use App\Repositories\Order\OrderAssuredFulfillRepository;
use App\Repositories\Order\OrderItsRepository;
use App\Repositories\Order\OrderRepository;
use Carbon\Carbon;
use Yanqu\YanquPhplib\MessageCenter\SendAliSMSClient;
use Yanqu\YanquPhplib\YqLog\YqLog;

class OrderAssuredFulfillService
{
    /**
     * 设置确定交付出结果日期
     * @param $oid
     * @param $arriveTime
     * @param $isSampleShipping
     * @return void
     * @throws \Exception
     */
    public function setResultTime($oid, $arriveTime, $isSampleShipping)
    {
        $assuredFulfillOrderInfo = app(OrderAssuredFulfillRepository::class)
            ->getAssuredFulfillOrderInfo($oid);
        if (empty($assuredFulfillOrderInfo) || !empty($assuredFulfillOrderInfo->arrive_time)) {
            // 非确定性交付订单或者已经设置了样品到达办事处时间，则无需再做处理
            return;
        }

        $fulfillDays = $assuredFulfillOrderInfo->fulfill_days;
        $resultDateTime = Carbon::parse($assuredFulfillOrderInfo->arrive_time)
            ->addSeconds($fulfillDays * 24 * 60 * 60);
        $resultTime = $resultDateTime->format('Y-m-d H:i:s');
        // 更新确定性交付相关时间
        app(OrderAssuredFulfillRepository::class)
            ->updateAssuredFulfillTime($oid, $arriveTime, $resultTime);
        $smsDetail = $this->sendFulfillResultSms($oid, $resultDateTime->format('Y-m-d'));
        // 记录一条日志
        YqLog::logger('performance:order:assuredFulfill')
            ->info('确定性交付订单出结果日期确认', [
                'oid' => $oid,
                'arrive_time' => $arriveTime,
                'fullfill_days' => $fulfillDays,
                'result_time' => $resultTime,
                'is_sample_shipping' => $isSampleShipping,
                'sms_detail' => $smsDetail,
            ]);
    }

    /**
     * 发送确定交付出结果日期短信通知
     * @param $oid
     * @param $resultDate
     * @return array
     * @throws \Exception
     */
    public function sendFulfillResultSms($oid, $resultDate)
    {
        $orderInfo = app(OrderRepository::class)
            ->getOrderInfoByFields($oid, ['osn', 'accountid', 'productname']);
        $orderItsInfo = app(OrderItsRepository::class)
            ->getOrderItsInfo($oid);

        $smsClient = new SendAliSMSClient();
        $smsClient->setBiz('assuredFulfillOrder.resultDateNotify');
        $smsClient->setOid($oid);
        $smsClient->setAccountId($orderInfo->accountid);
        $smsExtraInfo = [
            'osn' => $orderInfo->osn,
            'commodity' => $orderInfo->productname,
            'resultDate' => $resultDate
        ];
        if (!empty($orderItsInfo) && !empty($orderItsInfo->itmobile)) {
            // 如果填写了实验联系人，则给实验联系人发送短信
            $smsExtraInfo['phone'] = $orderItsInfo->itmobile;
        }
        $smsClient->setExt($smsExtraInfo);
        $smsClient->send();

        return $smsExtraInfo;
    }
}