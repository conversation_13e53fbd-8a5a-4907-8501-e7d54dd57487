<?php

namespace App\Services\Order;

use App\Entities\GroupOrder\PaymentAggregationDetailsFilterBo;
use App\Entities\GroupOrder\PaymentAggregationDetailVo;
use App\Models\OrderInfo;
use App\Repositories\Order\GroupOrderRepository;
use Yanqu\YanquPhplib\Constants\Order\OrderPayModuleConstant;
use Yanqu\YanquPhplib\Constants\Order\OrderPayStateConstants;
use Yanqu\YanquPhplib\Constants\Order\OrderStateConstant;

class GroupOrderService
{
    public function paymentAggregationDetails(PaymentAggregationDetailsFilterBo $aggregationDetailsFilterBo
    ): PaymentAggregationDetailVo {
        //筛选待审核订单
        if ($aggregationDetailsFilterBo->getOrderState() == '-1') {
            $aggregationDetailsFilterBo->setOrderState(null);
            $aggregationDetailsFilterBo->setType(2);
        }
        $groupOrderWaitPaidAmountList = app(GroupOrderRepository::class)->getGroupWaitOrderAmountList(
            $aggregationDetailsFilterBo
        );
        $getGroupCanceledOrderAmountList = app(GroupOrderRepository::class)->getGroupCanceledOrderAmountList(
            $aggregationDetailsFilterBo
        );
        $getGroupPaidOrderAmountList = app(GroupOrderRepository::class)->getGroupPaidOrderAmountList(
            $aggregationDetailsFilterBo
        );
        $paymentAggregationDetailsVo = new PaymentAggregationDetailVo();
        $paymentAggregationDetailsVo->setCanceledOrderCount($getGroupCanceledOrderAmountList->count());
        $paymentAggregationDetailsVo->setCanceledOrderTotalAmount(
            $getGroupCanceledOrderAmountList->reduce(
                function ($carry, $item) {
                    /** @var OrderInfo $item */
                    if ($item->orderstate == OrderStateConstant::CANCEL) {
                        $carry = bcadd($carry, $item->order_price ?? 0, 2);
                        $carry = bcadd($carry, $item->express_price ?? 0, 2);
                        $carry = bcadd($carry, $item->timely_price ?? 0, 2);
                        $carry = bcadd($carry, $item->course_price ?? 0, 2);
                    } elseif ($item->orderstate == OrderStateConstant::BACK) {
                        $carry = bcadd($carry, $item->onlinepayamount ?? 0, 2);
                        $carry = bcadd($carry, $item->grouppayamount ?? 0, 2);
                        $carry = bcadd($carry, $item->creditpayamount ?? 0, 2);
                        $carry = bcadd($carry, $item->couponpayamount ?? 0, 2);
                    }

                    return $carry;
                },
                '0'
            )
        );
        $paymentAggregationDetailsVo->setWaitPaidOrderCount($groupOrderWaitPaidAmountList->count());

        $unPaidOrderAmount = "0";
        foreach ($groupOrderWaitPaidAmountList as $groupWaitPaidOrder) {
            /** @var OrderInfo $groupWaitPaidOrder */
            if ($groupWaitPaidOrder->paymodule == OrderPayModuleConstant::QUOTE_MODE
                && $groupWaitPaidOrder->pricestatus == 0) {
                //待报价状态
                continue;
            }

            $unPaidOrderAmount = bcadd($unPaidOrderAmount, $groupWaitPaidOrder->order_price ?? 0, 2);
            $unPaidOrderAmount = bcadd($unPaidOrderAmount, $groupWaitPaidOrder->express_price ?? 0, 2);
            $unPaidOrderAmount = bcadd($unPaidOrderAmount, $groupWaitPaidOrder->timely_price ?? 0, 2);
            $unPaidOrderAmount = bcadd($unPaidOrderAmount, $groupWaitPaidOrder->course_price ?? 0, 2);
        }
        $paymentAggregationDetailsVo->setWaitPaidOrderTotalAmount($unPaidOrderAmount);

        $paidCount = 0;
        /** @var OrderInfo $order */
        foreach ($getGroupPaidOrderAmountList as $order) {
            $paidCount++;
            if ($order->couponpayamount > 0) {
                $paymentAggregationDetailsVo->setPersonPrepaymentPaidAmount(
                    bcadd($paymentAggregationDetailsVo->getPersonPrepaymentPaidAmount(), $order->couponpayamount, 2)
                );
                $paymentAggregationDetailsVo->setPaidOrderTotalAmount(
                    bcadd($paymentAggregationDetailsVo->getPaidOrderTotalAmount(), $order->couponpayamount, 2)
                );
                $paymentAggregationDetailsVo->setNoNeedTicketAmount(
                    bcadd($paymentAggregationDetailsVo->getNoNeedTicketAmount(), $order->couponpayamount, 2)
                );
            }

            if ($order->grouppayamount > 0) {
                $paymentAggregationDetailsVo->setGroupPrepaymentPaidAmount(
                    bcadd($paymentAggregationDetailsVo->getGroupPrepaymentPaidAmount(), $order->grouppayamount, 2)
                );
                $paymentAggregationDetailsVo->setPaidOrderTotalAmount(
                    bcadd($paymentAggregationDetailsVo->getPaidOrderTotalAmount(), $order->grouppayamount, 2)
                );
                $paymentAggregationDetailsVo->setNoNeedTicketAmount(
                    bcadd($paymentAggregationDetailsVo->getNoNeedTicketAmount(), $order->grouppayamount, 2)
                );
            }

            if ($order->onlinepayamount > 0) {
                $paymentAggregationDetailsVo->setOnlinePaidAmount(
                    bcadd($paymentAggregationDetailsVo->getOnlinePaidAmount(), $order->onlinepayamount, 2)
                );
                $paymentAggregationDetailsVo->setPaidOrderTotalAmount(
                    bcadd($paymentAggregationDetailsVo->getPaidOrderTotalAmount(), $order->onlinepayamount, 2)
                );
                if ($order->ticketid > 0) {
                    $paymentAggregationDetailsVo->setAppliedTicketAmount(
                        bcadd($paymentAggregationDetailsVo->getAppliedTicketAmount(), $order->onlinepayamount, 2)
                    );
                } else {
                    $paymentAggregationDetailsVo->setUnApplyTicketAmount(
                        bcadd($paymentAggregationDetailsVo->getUnApplyTicketAmount(), $order->onlinepayamount, 2)
                    );
                }
            }

            if (
                !empty($order->accountCreditHistory) && $order->accountCreditHistory->group_id > 0
                && $order->creditpayamount > 0
            ) {
                $paymentAggregationDetailsVo->setGroupCreditPaidAmount(
                    bcadd(
                        $paymentAggregationDetailsVo->getGroupCreditPaidAmount(),
                        $order->creditpayamount,
                        2
                    )
                );
                $paymentAggregationDetailsVo->setPaidOrderTotalAmount(
                    bcadd($paymentAggregationDetailsVo->getPaidOrderTotalAmount(), $order->creditpayamount, 2)
                );
            } elseif ($order->creditpayamount > 0) {
                $paymentAggregationDetailsVo->setPersonCreditPaidAmount(
                    bcadd($paymentAggregationDetailsVo->getPersonCreditPaidAmount(), $order->creditpayamount, 2)
                );
                $paymentAggregationDetailsVo->setPaidOrderTotalAmount(
                    bcadd($paymentAggregationDetailsVo->getPaidOrderTotalAmount(), $order->creditpayamount, 2)
                );
            }

            if ($order->creditpayamount > 0) {
                if ($order->ticketid > 0) {
                    $paymentAggregationDetailsVo->setAppliedTicketAmount(
                        bcadd($paymentAggregationDetailsVo->getAppliedTicketAmount(), $order->creditpayamount, 2)
                    );
                } else {
                    $paymentAggregationDetailsVo->setUnApplyTicketAmount(
                        bcadd($paymentAggregationDetailsVo->getUnApplyTicketAmount(), $order->creditpayamount, 2)
                    );
                }
            }
        }
        $paymentAggregationDetailsVo->setPaidOrderCount($paidCount);

        $paymentAllCount = $paymentAggregationDetailsVo->getPaidOrderCount()
                           + $paymentAggregationDetailsVo->getWaitPaidOrderCount()
                           + $paymentAggregationDetailsVo->getCanceledOrderCount();

        $paymentAllAmount = bcadd(
            $paymentAggregationDetailsVo->getPaidOrderTotalAmount(),
            $paymentAggregationDetailsVo->getWaitPaidOrderTotalAmount(),
            2
        );
        $paymentAllAmount = bcadd($paymentAllAmount, $paymentAggregationDetailsVo->getCanceledOrderTotalAmount(), 2);

        $paymentAggregationDetailsVo->setAllPaidOrderCount($paymentAllCount);
        $paymentAggregationDetailsVo->setAllPaidAmount($paymentAllAmount);
        return $paymentAggregationDetailsVo;
    }
}