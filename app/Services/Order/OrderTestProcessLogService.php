<?php

namespace App\Services\Order;

use App\Constants\Order\OrderTestProcessConstant;
use App\Constants\Order\PauseTestSituationConstants;
use Illuminate\Support\Facades\DB;
use Yanqu\YanquPhplib\OrderTestProcess\OrderTestProcessConstants;

class OrderTestProcessLogService
{

    public function writeOrderPauseTestApplyLog($oid, $desc, $creator)
    {
        $testProcessLog = [
            'oid' => $oid,
            'desc' => $desc,
            'type' => OrderTestProcessConstants::POINT_ORDER_PAUSE_TEST,
            'creator' => $creator,
            'creator_type' => 4,
            'show_level' => OrderTestProcessConstant::SHOW_LEVEL_STAFF_AND_PROVIDER
        ];
        Db::table('order_test_process_log')->insert($testProcessLog);
    }

    public function writeOrderResumeTestLog($oid, $resumeRemark, $creator, $creatorType)
    {
        $testProcessLog = [
            'oid' => $oid,
            'desc' => "样品恢复测试：" . $resumeRemark,
            'type' => OrderTestProcessConstants::POINT_ORDER_RESUME_TEST,
            'creator' => $creator,
            'creator_type' => $creatorType,
            'show_level' => OrderTestProcessConstant::SHOW_LEVEL_STAFF_AND_PROVIDER
        ];
        Db::table('order_test_process_log')->insert($testProcessLog);
    }
}