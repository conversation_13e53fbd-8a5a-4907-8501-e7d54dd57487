<?php


namespace App\Services\FinanceProvider;


use App\Constants\AccountTypeConstants;
use App\Constants\FinanceProvider\OrderProviderConstants;
use Illuminate\Support\Facades\DB;

class OrderFinanceProviderService extends BaseFinanceProviderService
{
    /**
     * 根据订单id获取多个订单的默认主体
     *
     */
    public function getProviderList($orderIdList)
    {
        $orderList = $this->getOrderList($orderIdList);
        if (count($orderIdList) != count($orderList)) {
            throw new \Exception("存在无效的订单");
        }
        $returnData = [];
        foreach ($orderList as $key => $value) {
            $defaultProviderId = $this->getDefaultProviderIdList($value);
            $acceptableProviderList = $this->getAcceptableProviderList($value->categoryid);
            $returnData[$key]['defaultProviderId'] = $defaultProviderId;
            $returnData[$key]['acceptableProviderList'] = !empty($acceptableProviderList) ? $acceptableProviderList : [$defaultProviderId];
            $returnData[$key]['orderId'] = $value->oid;
            $returnData[$key]['orderNo'] = $value->osn;
        }
        $returnData = array_column($returnData, null, 'orderId');
        return $returnData;
    }

    private function getOrderList($orderIdList)
    {
        return DB::table('order')
            ->leftJoin('buffet_address', 'order.useruuid', '=', 'buffet_address.addressid')
            ->whereIn('oid', $orderIdList)
            ->select('order.oid', 'order.osn', 'order.sendsupplierid', 'order.categoryid', 'order.accountype', 'buffet_address.province')
            ->get();
    }

    public function getDefaultProviderIdList($orderInfo)
    {
        if (isset($orderInfo->sendsupplierid) == false || isset($orderInfo->categoryid) == false || isset($orderInfo->accountype) == false) {
            throw new \Exception(self::LACK_NECESSARY_MESSAGE);
        }
        $orderInfo->province = !empty($orderInfo->province) ? $orderInfo->province : 0;
        if (in_array($orderInfo->categoryid, OrderProviderConstants::CATEGORY_SEPARATED_BY_PROVINCE)) {
            $providerId = $this->getValueInRelationship(OrderProviderConstants::SUPPLIER_TO_PROVIDER,
                'supplierid', $orderInfo->sendsupplierid, 'providerId');
            if (empty($providerId)) {
                $providerId = $this->getValueInRelationship(OrderProviderConstants::PROVINCE_TO_PROVIDER,
                    'provinceId', $orderInfo->province, 'providerId');
            }
        } elseif (in_array($orderInfo->categoryid, OrderProviderConstants::CATEGORY_LIST_SEPARATED_BY_ACCOUNT_TYPE) &&
            !in_array($orderInfo->accountype, AccountTypeConstants::SCHOOL_LIST)) {
            $providerId = OrderProviderConstants::DEFAULT_PROVIDER;
        } else {
            $providerId = $this->getValueInRelationship(OrderProviderConstants::CATEGORY_TO_PROVIDER,
                'categoryId', $orderInfo->categoryid, 'providerId');
        }
        return !empty($providerId) ? $providerId : OrderProviderConstants::DEFAULT_PROVIDER;
    }

    public function getAcceptableProviderList($categoryId)
    {
        return $this->getValueInRelationship(OrderProviderConstants::CATEGORY_TO_ACCEPTABLE_PROVIDER,
            'categoryId', $categoryId, 'providerIdList');
    }
}