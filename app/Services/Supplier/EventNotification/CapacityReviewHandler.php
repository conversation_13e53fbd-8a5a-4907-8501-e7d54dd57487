<?php
/**
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2021 - 2021 (http://www.shiyanjia.com)
 * <AUTHOR> 2025/5/6
 */

namespace App\Services\Supplier\EventNotification;

use App\Models\SupplierEventNotifications;
use App\Repositories\Supplier\CapacityReviewRepository;
use App\Repositories\Supplier\SupplierFileReviewRepository;
use App\Services\Supplier\EventNotification\IEventNotificationInterface;
use Yanqu\YanquPhplib\Openapi\SupplierEventNotification\Constants\EventNotificationTypeConstant;
use Yanqu\YanquPhplib\YqLog\YqLog;

class CapacityReviewHandler implements IEventNotificationInterface
{

    private $type = EventNotificationTypeConstant::CAPACITY_REVIEW;
    private $typeName = EventNotificationTypeConstant::MAP[EventNotificationTypeConstant::CAPACITY_REVIEW];
    private $msg = "%s 【%s】您于%s提交的容量审核已通过。";

    public function handle(array $extraParams): void
    {
        // TODO: Implement handle() method.
        $id = $extraParams['id'] ?? 0;
        if (!$id) {
            // 记录报错
//            YqLog::logger('supplier_event_notice:capacity_review')->error('不存在审核记录', $extraParams);
            return;
        }
        $reviewLog = app(CapacityReviewRepository::class)->getReviewById($id, ['providermerchantid', 'create_time','status']);
        if ($reviewLog == null) {
            // 记录报错
//            YqLog::logger('supplier_event_notice:capacity_review')->error('不存在的审核记录', $extraParams);
            return;
        }
        if($reviewLog->status != 1){
            // 记录报错
//            YqLog::logger('supplier_event_notice:capacity_review')->info('未审核通过，不发送通知', $extraParams);
            return;
        }
        $msg = sprintf($this->msg, date('Y-m-d H:i:s'), $this->typeName,
            $reviewLog->create_time);
        $data = [
            'type' => $this->type,
            'content' => $msg,
            'key_data' => json_encode($extraParams, JSON_UNESCAPED_UNICODE),
            'supplier_id' => $reviewLog->providermerchantid,
        ];
        SupplierEventNotifications::insert($data);
    }
}