<?php
/**
 * 银行卡号相关
 */

namespace App\Services;

use Illuminate\Support\Facades\DB;

class BankService extends BaseService
{

    /**
     * 根据图片获取银行卡号
     */
    public function OcrBankCard($bankImg, $accountid)
    {
        $host = "https://dfbankcard.market.alicloudapi.com";
        $path = "/ocr/bankcard";
        $method = "POST";
        $bodys = "image_url=" . urlencode($bankImg);
        $querys = "";
        $data = $this->aliyunCurl($host, $path, $method, $bodys, $querys);
        DB::table("bank_request_history")->insert([
            "type" => 1,
            "accountid" => $accountid,
            "request" => json_encode(["bankImg" => $bankImg, "accountid" => $accountid]),
            "response" => json_encode($data),
            "create_time" => time()
        ]);
        return $data;
    }

    /**
     * 查询银行卡3要素
     */
    public function BankCard3c($bankcode, $username, $accountid)
    {
        //获取用户证件号
        $CardService = new CardService();
        $idcardInfo = $CardService->getUserIdCard($accountid);
        $idcard = $idcardInfo['idcard'];
        $idcardType = $idcardInfo['idcardType'];

        if (empty($username)) {
            return ["status" => 99, "msg" => "用户姓名缺失"];
        }
        $host = "https://bankcard3c.shumaidata.com";
        $path = "/bankcard3c";
        $method = "GET";
        $querys = "bankcard=" . $bankcode . "&idcard=" . $idcard . "&name=" . urlencode($username);
        $bodys = "";
        $data = $this->aliyunCurl($host, $path, $method, $bodys, $querys);
        //证件号加星存入记录
        $EncrypService = new EncrypService();
        $EncrypService->conetntAddStars($idcard, 1, $idcardType);
        $original = $EncrypService->getData();

        //增加记录
        DB::table("bank_request_history")->insert([
            "type" => 2,
            "accountid" => $accountid,
            "request" => json_encode(["bankcode" => $bankcode, "idcard" => $original, "name" => $username]),
            "response" => json_encode($data),
            "create_time" => time()
        ]);
        return ["status" => 100, "data" => $data, "msg" => "请求成功"];
    }

    /**
     * 查询银行卡2要素
     */
    public function BankCard2c($bankcode, $username, $accountid)
    {
        if (empty($username)) {
            return ["status" => 99, "msg" => "用户姓名缺失"];
        }
        $host = "https://bankcardc.shumaidata.com";
        $path = "/bankcard2c";
        $method = "GET";
        $querys = "bankcard=" . $bankcode . "&name=" . urlencode($username);
        $bodys = "";
        $data = $this->aliyunCurl($host, $path, $method, $bodys, $querys);
        DB::table("bank_request_history")->insert([
            "type" => 3,
            "accountid" => $accountid,
            "request" => json_encode(["bankcode" => $bankcode, "name" => $username]),
            "response" => json_encode($data),
            "create_time" => time()
        ]);
        return ["status" => 100, "data" => $data, "msg" => "请求成功"];
    }

    /**
     * 获取银行卡校验姓名
     */
    public function getUserRealname($accountid)
    {
        $name = "";
        $realname = DB::table("account_card")->where("accountid", $accountid)->value("realname");
        if (!empty($realname)) {
            $name = $realname;
        } else {
            $contacter = DB::table("account_info")->where("accountid", $accountid)->value("contacter");
            if (!empty($contacter)) {
                $name = $contacter;
            }
        }
        return $name;
    }

    /**
     * 阿里云curl请求
     */
    public function aliyunCurl($host, $path, $method, $bodys, $querys)
    {
        $appcode = config("constants.AppCode");
        $headers = array();
        array_push($headers, "Authorization:APPCODE " . $appcode);
        //根据API的要求，定义相对应的Content-Type
        array_push($headers, "Content-Type" . ":" . "application/x-www-form-urlencoded; charset=UTF-8");
        if (!empty($querys)) {
            $url = $host . $path . "?" . $querys;
        } else {
            $url = $host . $path;
        }
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HEADER, false);
        if (1 == strpos("$" . $host, "https://")) {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        }
        curl_setopt($curl, CURLOPT_POSTFIELDS, $bodys);
        $data = curl_exec($curl);
        curl_close($curl);
        $data = json_decode($data, true);
        return $data;
    }
}
