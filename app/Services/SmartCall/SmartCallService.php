<?php

namespace App\Services\SmartCall;

use App\Utils\EnvironmentHelper;
use App\Utils\CommonWhiteListUtil;
use Yanqu\YanquPhplib\SmartCall\SmartCallClient;
use Yanqu\YanquPhplib\YqLog\YqLog;
use Yanqu\YanquPhplib\CheckIp\CheckIpUtil;
use Yanqu\YanquPhplib\Configuration;

/**
 * 智能外呼业务服务类
 * 处理IP校验、白名单限制和发布任务功能
 */
class SmartCallService
{
    /**
     * 智能外呼客户端
     * @var SmartCallClient
     */
    private $client;

    /**
     * 白名单工具
     * @var CommonWhiteListUtil
     */
    private $whiteListUtil;

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 优先从.config.ini读取配置，如果没有则使用constants配置
        $config = Configuration::getInstance();

        $appKey = $config->get('smart_call_app_key') ?: config('constants.smart_call_app_key');
        $appSecret = $config->get('smart_call_app_secret') ?: config('constants.smart_call_app_secret');
        $baseUrl = $config->get('smart_call_base_url') ?: config('constants.smart_call_url');
        $appType = $config->get('smart_call_app_type') ?: config('constants.smart_call_app_type', '');

        $this->client = new SmartCallClient($appKey, $appSecret, $baseUrl);
        $this->whiteListUtil = new CommonWhiteListUtil();
    }

    /**
     * 环境和权限校验
     *
     * @param int|null $accountId 用户ID（测试环境需要）
     * @return bool
     * @throws \Exception
     */
    private function checkEnvironmentAndPermission($accountId = null)
    {
        // 检查IP是否合法
        $isLegalIp = CheckIpUtil::checkIp();
        if (!$isLegalIp) {
            throw new \Exception('IP地址不在白名单中');
        }

        // 生产环境直接通过
        if (EnvironmentHelper::isProduction()) {
            return true;
        }

        // 测试环境需要校验用户白名单
        if (EnvironmentHelper::isTest()) {
            if (empty($accountId)) {
                throw new \Exception('测试环境需要提供用户ID进行白名单校验');
            }

            // 检查用户是否在白名单中
            $whiteList = $this->whiteListUtil->getWhiteList(1, $accountId);
            if (empty($whiteList)) {
                throw new \Exception('用户不在测试环境白名单中');
            }

            YqLog::logger('smart_call:test_env')->info('测试环境白名单校验通过', [
                'account_id' => $accountId,
                'ip' => CheckIpUtil::getIP()
            ]);

            return true;
        }

        // 其他环境不允许访问
        throw new \Exception('当前环境不支持智能外呼功能');
    }

    /**
     * 创建/更新外呼任务
     *
     * @param array $taskData 任务数据
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function saveTask(array $taskData, $accountId = null)
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);

        try {
            // 记录业务日志
            YqLog::logger('smart_call:save_task')->info('开始创建/更新外呼任务', [
                'task_data' => $taskData,
                'account_id' => $accountId,
                'environment' => app()->environment(),
                'ip' => CheckIpUtil::getIP()
            ]);

            // 调用网易七鱼API
            $response = $this->client->post('/v1/task/save', $taskData);

            $data = $response->getData();
            YqLog::logger('smart_call:save_task')->info('创建/更新外呼任务成功', [
                'task_id' => isset($data['taskId']) ? $data['taskId'] : null,
                'account_id' => $accountId,
                'request_id' => $response->getRequestId()
            ]);

            return $data ?: [];

        } catch (\Exception $e) {
            YqLog::logger('smart_call:save_task')->error('创建/更新外呼任务失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId,
                'task_data' => $taskData
            ]);
            throw $e;
        }
    }

    /**
     * 导入客户信息
     *
     * @param array $customers 客户列表
     * @param int|null $accountId 操作用户ID
     * @param string|null $encryptKey 加密密钥
     * @return array
     * @throws \Exception
     */
    public function importCustomers(array $customers, $accountId = null, $encryptKey = null)
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);

        try {
            $originalCount = count($customers);

            // 测试环境需要过滤白名单手机号
            if (EnvironmentHelper::isTest()) {
                $customers = $this->whiteListUtil->filterCustomersByPhoneWhiteList($customers, 1);

                YqLog::logger('smart_call:import_customers')->info('测试环境白名单过滤', [
                    'original_count' => $originalCount,
                    'filtered_count' => count($customers),
                    'account_id' => $accountId
                ]);

                if (empty($customers)) {
                    throw new \Exception('测试环境下没有合法的白名单手机号，无法导入客户');
                }
            }

            // 记录业务日志
            YqLog::logger('smart_call:import_customers')->info('开始导入客户', [
                'customer_count' => count($customers),
                'original_count' => $originalCount,
                'account_id' => $accountId,
                'environment' => EnvironmentHelper::getCurrentEnvironment(),
                'ip' => CheckIpUtil::getIP()
            ]);

            // 如果提供了加密密钥，对敏感字段进行加密
            if ($encryptKey) {
                $customers = $this->encryptSensitiveFields($customers, $encryptKey);
            }

            // 调用通用客户端
            $response = $this->client->post('/customer/import', ['customers' => $customers]);

            $data = $response->getData();
            YqLog::logger('smart_call:import_customers')->info('导入客户成功', [
                'customer_count' => count($customers),
                'account_id' => $accountId,
                'request_id' => $response->getRequestId()
            ]);

            return $data ?: [];

        } catch (\Exception $e) {
            YqLog::logger('smart_call:import_customers')->error('导入客户失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId,
                'customer_count' => count($customers)
            ]);
            throw $e;
        }
    }

    /**
     * 加密敏感字段
     *
     * @param array $customers 客户数据
     * @param string $encryptKey 加密密钥
     * @return array
     */
    private function encryptSensitiveFields(array $customers, $encryptKey)
    {
        foreach ($customers as &$customer) {
            if (isset($customer['phone'])) {
                $customer['phone'] = $this->client->encryptSensitiveField($customer['phone'], $encryptKey);
            }
        }
        return $customers;
    }

    /**
     * 获取当前环境信息
     *
     * @return array
     */
    public function getEnvironmentInfo()
    {
        return [
            'environment' => EnvironmentHelper::getCurrentEnvironment(),
            'laravel_env' => app()->environment(),
            'is_production' => EnvironmentHelper::isProduction(),
            'is_test' => EnvironmentHelper::isTest(),
            'is_dev' => EnvironmentHelper::isDev(),
            'ip' => CheckIpUtil::getIP(),
            'timestamp' => time(),
            'config_source' => 'config.ini + constants.php'
        ];
    }

    /**
     * 获取话术列表
     *
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function getBotList($accountId = null)
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);

        try {
            $response = $this->client->get('/v1/bot/list');
            return $response->getData() ?: [];
        } catch (\Exception $e) {
            YqLog::logger('smart_call:get_bot_list')->error('获取话术列表失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId
            ]);
            throw $e;
        }
    }

    /**
     * 获取可用线路列表
     *
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function getDidList($accountId = null)
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);

        try {
            $response = $this->client->get('/v1/did/list');
            return $response->getData() ?: [];
        } catch (\Exception $e) {
            YqLog::logger('smart_call:get_did_list')->error('获取线路列表失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId
            ]);
            throw $e;
        }
    }

    /**
     * 获取任务列表
     *
     * @param array $params 查询参数
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function getTaskList(array $params = [], $accountId = null)
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);

        try {
            $response = $this->client->get('/v1/task/list', $params);
            return $response->getData() ?: [];
        } catch (\Exception $e) {
            YqLog::logger('smart_call:get_task_list')->error('获取任务列表失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId,
                'params' => $params
            ]);
            throw $e;
        }
    }

    /**
     * 获取任务详情
     *
     * @param int $taskId 任务ID
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function getTaskDetail($taskId, $accountId = null)
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);

        try {
            $response = $this->client->get('/v1/task/detail', ['taskId' => $taskId]);
            return $response->getData() ?: [];
        } catch (\Exception $e) {
            YqLog::logger('smart_call:get_task_detail')->error('获取任务详情失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId,
                'task_id' => $taskId
            ]);
            throw $e;
        }
    }

    /**
     * 启动/停止任务
     *
     * @param int $taskId 任务ID
     * @param string $action 操作类型：start|停止
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function controlTask($taskId, $action, $accountId = null)
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);

        if (!in_array($action, ['start', 'stop'])) {
            throw new \Exception('不支持的操作类型');
        }

        try {
            $endpoint = $action === 'start' ? '/v1/task/start' : '/v1/task/stop';
            $response = $this->client->post($endpoint, ['taskId' => $taskId]);

            YqLog::logger('smart_call:control_task')->info('任务控制成功', [
                'task_id' => $taskId,
                'action' => $action,
                'account_id' => $accountId
            ]);

            return $response->getData() ?: [];
        } catch (\Exception $e) {
            YqLog::logger('smart_call:control_task')->error('任务控制失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId,
                'task_id' => $taskId,
                'action' => $action
            ]);
            throw $e;
        }
    }

    /**
     * 删除任务
     *
     * @param int $taskId 任务ID
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function deleteTask($taskId, $accountId = null)
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);

        try {
            $response = $this->client->post('/v1/task/delete', ['taskId' => $taskId]);

            YqLog::logger('smart_call:delete_task')->info('删除任务成功', [
                'task_id' => $taskId,
                'account_id' => $accountId
            ]);

            return $response->getData() ?: [];
        } catch (\Exception $e) {
            YqLog::logger('smart_call:delete_task')->error('删除任务失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId,
                'task_id' => $taskId
            ]);
            throw $e;
        }
    }
}
