<?php

namespace App\Services\SmartCall;

use App\Utils\EnvironmentHelper;
use App\Utils\CommonWhiteListUtil;
use Yanqu\YanquPhplib\SmartCall\SmartCallClient;
use Yanqu\YanquPhplib\YqLog\YqLog;
use Yanqu\YanquPhplib\CheckIp\CheckIpUtil;

/**
 * 智能外呼业务服务类
 * 处理IP校验、白名单限制和发布任务功能
 */
class SmartCallService
{
    /**
     * 智能外呼客户端
     * @var SmartCallClient
     */
    private $client;
    
    /**
     * 白名单工具
     * @var CommonWhiteListUtil
     */
    private $whiteListUtil;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 使用 yanqu-phplib 中的通用客户端
        $this->client = new SmartCallClient(
            config('constants.smart_call_url'),
            config('constants.smart_call_app_key'),
            config('constants.smart_call_app_secret'),
            config('constants.smart_call_app_type', '')
        );
        
        $this->whiteListUtil = new CommonWhiteListUtil();
    }
    
    /**
     * 环境和权限校验
     * 
     * @param int|null $accountId 用户ID（测试环境需要）
     * @return bool
     * @throws \Exception
     */
    private function checkEnvironmentAndPermission($accountId = null)
    {
        // 检查IP是否合法
        $isLegalIp = CheckIpUtil::checkIp();
        if (!$isLegalIp) {
            throw new \Exception('IP地址不在白名单中');
        }
        
        // 生产环境直接通过
        if (EnvironmentHelper::isProduction()) {
            return true;
        }
        
        // 测试环境需要校验用户白名单
        if (EnvironmentHelper::isTest()) {
            if (empty($accountId)) {
                throw new \Exception('测试环境需要提供用户ID进行白名单校验');
            }
            
            // 检查用户是否在白名单中
            $whiteList = $this->whiteListUtil->getWhiteList(1, $accountId);
            if (empty($whiteList)) {
                throw new \Exception('用户不在测试环境白名单中');
            }
            
            YqLog::logger('smart_call:test_env')->info('测试环境白名单校验通过', [
                'account_id' => $accountId,
                'ip' => CheckIpUtil::getIP()
            ]);
            
            return true;
        }
        
        // 其他环境不允许访问
        throw new \Exception('当前环境不支持智能外呼功能');
    }
    
    /**
     * 创建/更新外呼任务
     * 
     * @param array $taskData 任务数据
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function saveTask(array $taskData, $accountId = null)
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);
        
        try {
            // 记录业务日志
            YqLog::logger('smart_call:save_task')->info('开始创建/更新外呼任务', [
                'task_data' => $taskData,
                'account_id' => $accountId,
                'environment' => app()->environment(),
                'ip' => CheckIpUtil::getIP()
            ]);
            
            // 调用网易七鱼API
            $response = $this->client->post('/wecall/v1/task/save', $taskData);
            
            YqLog::logger('smart_call:save_task')->info('创建/更新外呼任务成功', [
                'task_id' => isset($response['data']['taskId']) ? $response['data']['taskId'] : null,
                'account_id' => $accountId,
                'request_id' => isset($response['requestId']) ? $response['requestId'] : null
            ]);
            
            return isset($response['data']) ? $response['data'] : [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:save_task')->error('创建/更新外呼任务失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId,
                'task_data' => $taskData
            ]);
            throw $e;
        }
    }
    
    /**
     * 导入客户信息
     * 
     * @param array $customers 客户列表
     * @param int|null $accountId 操作用户ID
     * @param string|null $encryptKey 加密密钥
     * @return array
     * @throws \Exception
     */
    public function importCustomers(array $customers, $accountId = null, $encryptKey = null)
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);
        
        try {
            // 记录业务日志
            YqLog::logger('smart_call:import_customers')->info('开始导入客户', [
                'customer_count' => count($customers),
                'account_id' => $accountId,
                'environment' => app()->environment(),
                'ip' => CheckIpUtil::getIP()
            ]);
            
            // 如果提供了加密密钥，对敏感字段进行加密
            if ($encryptKey) {
                $customers = $this->encryptSensitiveFields($customers, $encryptKey);
            }
            
            // 调用通用客户端
            $response = $this->client->post('/customer/import', ['customers' => $customers]);
            
            YqLog::logger('smart_call:import_customers')->info('导入客户成功', [
                'customer_count' => count($customers),
                'account_id' => $accountId,
                'request_id' => isset($response['requestId']) ? $response['requestId'] : null
            ]);
            
            return isset($response['data']) ? $response['data'] : [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:import_customers')->error('导入客户失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId,
                'customer_count' => count($customers)
            ]);
            throw $e;
        }
    }
    
    /**
     * 加密敏感字段
     * 
     * @param array $customers 客户数据
     * @param string $encryptKey 加密密钥
     * @return array
     */
    private function encryptSensitiveFields(array $customers, $encryptKey)
    {
        foreach ($customers as &$customer) {
            if (isset($customer['phone'])) {
                $customer['phone'] = $this->client->encryptSensitiveField($customer['phone'], $encryptKey);
            }
        }
        return $customers;
    }
    
    /**
     * 获取当前环境信息
     * 
     * @return array
     */
    public function getEnvironmentInfo()
    {
        return [
            'environment' => app()->environment(),
            'is_production' => EnvironmentHelper::isProduction(),
            'is_test' => EnvironmentHelper::isTest(),
            'ip' => CheckIpUtil::getIP(),
            'timestamp' => time()
        ];
    }
}
