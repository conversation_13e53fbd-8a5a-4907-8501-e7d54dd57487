<?php

namespace App\Services\SmartCall;

use App\Utils\EnvironmentHelper;
use App\Utils\CommonWhiteListUtil;
use Yanqu\YanquPhplib\SmartCall\SmartCallClient;
use Yanqu\YanquPhplib\YqLog\YqLog;
use Yanqu\YanquPhplib\CheckIp\CheckIpUtil;

/**
 * 网易七鱼智能外呼业务服务类
 * 处理业务逻辑和环境校验
 */
class NetEaseSmartCallService
{
    /**
     * 智能外呼客户端
     */
    private SmartCallClient $client;
    
    /**
     * 白名单工具
     */
    private CommonWhiteListUtil $whiteListUtil;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 使用 yanqu-phplib 中的通用客户端
        $this->client = new SmartCallClient(
            config('constants.smart_call_url'),
            config('constants.smart_call_app_key'),
            config('constants.smart_call_app_secret'),
            config('constants.smart_call_app_type', '')
        );
        
        $this->whiteListUtil = new CommonWhiteListUtil();
    }
    
    /**
     * 环境和权限校验
     * 
     * @param int|null $accountId 用户ID（测试环境需要）
     * @return bool
     * @throws \Exception
     */
    private function checkEnvironmentAndPermission(?int $accountId = null): bool
    {
        // 检查IP是否合法
        $isLegalIp = CheckIpUtil::checkIp();
        if (!$isLegalIp) {
            throw new \Exception('IP地址不在白名单中');
        }
        
        // 生产环境直接通过
        if (EnvironmentHelper::isProduction()) {
            return true;
        }
        
        // 测试环境需要校验用户白名单
        if (EnvironmentHelper::isTest()) {
            if (empty($accountId)) {
                throw new \Exception('测试环境需要提供用户ID进行白名单校验');
            }
            
            // 检查用户是否在白名单中
            $whiteList = $this->whiteListUtil->getWhiteList(1, $accountId);
            if (empty($whiteList)) {
                throw new \Exception('用户不在测试环境白名单中');
            }
            
            YqLog::logger('smart_call:test_env')->info('测试环境白名单校验通过', [
                'account_id' => $accountId,
                'ip' => CheckIpUtil::getIP()
            ]);
            
            return true;
        }
        
        // 其他环境不允许访问
        throw new \Exception('当前环境不支持智能外呼功能');
    }
    
    /**
     * 导入客户信息
     * 
     * @param array $customers 客户列表
     * @param int|null $accountId 操作用户ID
     * @param string|null $encryptKey 加密密钥
     * @return array
     * @throws \Exception
     */
    public function importCustomers(array $customers, ?int $accountId = null, ?string $encryptKey = null): array
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);
        
        try {
            // 记录业务日志
            YqLog::logger('smart_call:import_customers')->info('开始导入客户', [
                'customer_count' => count($customers),
                'account_id' => $accountId,
                'environment' => app()->environment(),
                'ip' => CheckIpUtil::getIP()
            ]);
            
            // 如果提供了加密密钥，对敏感字段进行加密
            if ($encryptKey) {
                $customers = $this->encryptSensitiveFields($customers, $encryptKey);
            }
            
            // 调用通用客户端
            $response = $this->client->post('/customer/import', ['customers' => $customers]);
            
            YqLog::logger('smart_call:import_customers')->info('导入客户成功', [
                'customer_count' => count($customers),
                'account_id' => $accountId,
                'request_id' => $response['requestId'] ?? null
            ]);
            
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:import_customers')->error('导入客户失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId,
                'customer_count' => count($customers)
            ]);
            throw $e;
        }
    }
    
    /**
     * 创建/更新外呼任务
     * 
     * @param array $taskData 任务数据
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function saveTask(array $taskData, ?int $accountId = null): array
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);
        
        try {
            // 记录业务日志
            YqLog::logger('smart_call:save_task')->info('开始创建/更新外呼任务', [
                'task_data' => $taskData,
                'account_id' => $accountId,
                'environment' => app()->environment(),
                'ip' => CheckIpUtil::getIP()
            ]);
            
            // 调用网易七鱼API
            $response = $this->client->post('/wecall/v1/task/save', $taskData);
            
            YqLog::logger('smart_call:save_task')->info('创建/更新外呼任务成功', [
                'task_id' => $response['data']['taskId'] ?? null,
                'account_id' => $accountId,
                'request_id' => $response['requestId'] ?? null
            ]);
            
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:save_task')->error('创建/更新外呼任务失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId,
                'task_data' => $taskData
            ]);
            throw $e;
        }
    }
    
    /**
     * 获取话术列表
     * 
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function getBotList(?int $accountId = null): array
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);
        
        try {
            $response = $this->client->get('/wecall/v1/bot/list');
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:get_bot_list')->error('获取话术列表失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId
            ]);
            throw $e;
        }
    }
    
    /**
     * 获取可用线路列表
     * 
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function getDidList(?int $accountId = null): array
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);
        
        try {
            $response = $this->client->get('/wecall/v1/did/list');
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:get_did_list')->error('获取线路列表失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId
            ]);
            throw $e;
        }
    }
    
    /**
     * 获取意向标签组详情
     * 
     * @param array $groupIds 标签组ID列表
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function getIntentGroupsByIds(array $groupIds, ?int $accountId = null): array
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);
        
        try {
            $response = $this->client->post('/wecall/v1/intent/getGroupsByIds', ['groupIds' => $groupIds]);
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:get_intent_groups')->error('获取意向标签组失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId,
                'group_ids' => $groupIds
            ]);
            throw $e;
        }
    }
    
    /**
     * 获取已审核短信模版列表
     * 
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function getValidSmsTemplateList(?int $accountId = null): array
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);
        
        try {
            $response = $this->client->get('/wecall/v1/sms/getValidTemplateList');
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:get_sms_templates')->error('获取短信模版列表失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId
            ]);
            throw $e;
        }
    }
    
    /**
     * 获取拦截规则列表
     * 
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function getFilterRuleList(?int $accountId = null): array
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);
        
        try {
            $response = $this->client->get('/wecall/v1/filter/list');
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:get_filter_rules')->error('获取拦截规则列表失败', [
                'error' => $e->getMessage(),
                'account_id' => $accountId
            ]);
            throw $e;
        }
    }
    
    /**
     * 查询任务状态
     * 
     * @param int $taskId 任务ID
     * @param int|null $accountId 操作用户ID
     * @return array
     * @throws \Exception
     */
    public function getTaskStatus(int $taskId, ?int $accountId = null): array
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);
        
        try {
            $response = $this->client->get('/wecall/v1/task/status', ['taskId' => $taskId]);
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:get_task_status')->error('查询任务状态失败', [
                'error' => $e->getMessage(),
                'task_id' => $taskId,
                'account_id' => $accountId
            ]);
            throw $e;
        }
    }
    
    /**
     * 批量导入客户（支持大批量数据）
     * 
     * @param array $customers 客户列表
     * @param int|null $accountId 操作用户ID
     * @param string|null $encryptKey 加密密钥
     * @param int $batchSize 批次大小
     * @return array 导入结果统计
     * @throws \Exception
     */
    public function batchImportCustomers(array $customers, ?int $accountId = null, ?string $encryptKey = null, int $batchSize = 100): array
    {
        // 环境和权限校验
        $this->checkEnvironmentAndPermission($accountId);
        
        $totalCount = count($customers);
        $successCount = 0;
        $failedCount = 0;
        $errors = [];
        
        // 分批处理
        $batches = array_chunk($customers, $batchSize);
        
        YqLog::logger('smart_call:batch_import')->info('开始批量导入客户', [
            'total_count' => $totalCount,
            'batch_count' => count($batches),
            'batch_size' => $batchSize,
            'account_id' => $accountId
        ]);
        
        foreach ($batches as $batchIndex => $batch) {
            try {
                // 添加延迟以避免频率限制
                if ($batchIndex > 0) {
                    usleep(100000); // 延迟100毫秒
                }
                
                $this->importCustomers($batch, $accountId, $encryptKey);
                $successCount += count($batch);
                
            } catch (\Exception $e) {
                $failedCount += count($batch);
                $errors[] = [
                    'batch' => $batchIndex + 1,
                    'error' => $e->getMessage(),
                    'count' => count($batch)
                ];
                
                YqLog::logger('smart_call:batch_import')->warning('批次导入失败', [
                    'batch_index' => $batchIndex + 1,
                    'batch_size' => count($batch),
                    'error' => $e->getMessage(),
                    'account_id' => $accountId
                ]);
            }
        }
        
        $result = [
            'total_count' => $totalCount,
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'errors' => $errors
        ];
        
        YqLog::logger('smart_call:batch_import')->info('批量导入完成', array_merge($result, [
            'account_id' => $accountId
        ]));
        
        return $result;
    }
    
    /**
     * 加密敏感字段
     * 
     * @param array $customers 客户数据
     * @param string $encryptKey 加密密钥
     * @return array
     */
    private function encryptSensitiveFields(array $customers, string $encryptKey): array
    {
        foreach ($customers as &$customer) {
            if (isset($customer['phone'])) {
                $customer['phone'] = $this->client->encryptSensitiveField($customer['phone'], $encryptKey);
            }
        }
        return $customers;
    }
    
    /**
     * 获取当前环境信息
     * 
     * @return array
     */
    public function getEnvironmentInfo(): array
    {
        return [
            'environment' => app()->environment(),
            'is_production' => EnvironmentHelper::isProduction(),
            'is_test' => EnvironmentHelper::isTest(),
            'ip' => CheckIpUtil::getIP(),
            'timestamp' => time()
        ];
    }
}
