<?php

namespace App\Services\Nanometer;

use App\Models\NanometerAppliedHazardous;
use App\Models\NanometerHazardousCas;
use App\Models\NanometerOrder;

class NanoHazardousService
{
    public static function judgeNanoOrderIsHaveHazardous($orderids)
    {
        $osn = [];
        $isHazardous = false;
        if (empty($orderids)) {
            return ['osn' => '', 'isHazardous' => false, 'hazardousAmount' => 0];
        }
        $nanometerOrder = NanometerOrder::query()
            ->from('nanometer_order as a')
            ->leftJoin('nanometer_order_price as b', 'a.noid', '=', 'b.noid')
            ->leftJoin('nanometer_product_skusale as c', 'c.skuid', '=', 'b.skuid')
            ->leftJoin('nanometer_category as d', 'c.categoryid', '=', 'd.categoryid')
            ->whereIn('a.oid', $orderids)
            ->where('a.isvoid', 0)
            ->where('b.isvoid', 0)
            ->whereNotNull('c.cas')
            ->select("c.cas", "a.osn", 'c.b_skuname', 'd.category_type', 'c.price')
            ->selectRaw('order_price2 as amount')
            ->get()
            ->toArray();
        $casArray = array_column($nanometerOrder, 'cas');
        $cas = '';
        //判断是否是贵重金属
        $preciousMetalSkuName = [
            'Au115995',
            'Au13503',
            'FMHC-001',
            'Pt001',
            'Au001',
        ];
        $hazardousCasList = [];
        if (!empty($casArray)) {
            $nanometerAppliedHazardous = NanometerAppliedHazardous::query()->whereIn('cas', $casArray)->where('isvoid', 0)->get();
            if ($nanometerAppliedHazardous->isNotEmpty()) {
                $cas = $nanometerAppliedHazardous->pluck('cas')->toArray();
                $hazardousCasList = array_merge($hazardousCasList, $cas);
            }
            $hazardousCas = NanometerHazardousCas::query()->whereIn('cas', $casArray)->get();
            if ($hazardousCas->isNotEmpty()) {
                $cas = $hazardousCas->pluck('cas')->toArray();
                $hazardousCasList = array_merge($hazardousCasList, $cas);
            }
        }

        $hazardousAmount = 0;
        foreach ($nanometerOrder as $value) {
            if (in_array($value['b_skuname'], ['DH7000D', 'DH7003B', 'DH7002A', 'DH7001B', 'DH7003D'])) {
                continue;
            }
            if (
                // 危化品
                (!empty($value['cas']) && in_array($value['cas'], $hazardousCasList))
                // 贵金属
                || in_array($value['b_skuname'], $preciousMetalSkuName)
                // 仪器类 且单价大于1000
                || ($value['category_type'] == 1 && $value['price'] > 1000)
            ) {
                $isHazardous = true;
                $osn[] = $value['osn'];
                $hazardousAmount += $value['amount'];
            }
        }

        return ['osn' => implode(',', array_unique($osn)), 'isHazardous' => $isHazardous, 'hazardousAmount' => $hazardousAmount];
    }
}