<?php

namespace App\Services\InvoiceTool;

use App\Constants\InvoiceTool\InvoiceVerificationConstants;
use App\Entities\InvoiceTool\InvoiceVerificationDTO;
use App\Repositories\InvoiceTool\InvoiceVerificationRepository;

class InvoiceVerificationService
{
    /**
     * @param $params
     * @return array{verification_passed: bool, verification_message: string}
     */
    public function verifyInvoiceAndSave($params)
    {
        $invoiceVerificationDTO = new InvoiceVerificationDTO($params);
        $invoiceVerificationDTO = $this->verifyInvoice($invoiceVerificationDTO,
            InvoiceVerificationConstants::API_PROVIDER_BAIDU);
        $this->insertInvoiceVerification($invoiceVerificationDTO);
        return [
            'verification_passed' => $invoiceVerificationDTO->getVerificationPassed(),
            'verification_message' => $invoiceVerificationDTO->getVerificationResultMessage()];
    }

    private function verifyInvoice(InvoiceVerificationDTO $invoiceVerificationDTO,
                                                          $apiProvider): InvoiceVerificationDTO
    {
        $invoiceVerificationService = InvoiceVerificationFactory::create($apiProvider);
        $params = $invoiceVerificationService->buildRequestParams($invoiceVerificationDTO);
        $response = $invoiceVerificationService->request($params);
        $invoiceVerificationDTO->setVerificationResult($response);
        return $invoiceVerificationService->parseResponse($invoiceVerificationDTO);
    }

    private function insertInvoiceVerification(InvoiceVerificationDTO $invoiceVerificationDTO)
    {
        $invoiceVerificationRepository = new InvoiceVerificationRepository();
        $insertedInvoiceVerification = [
            'invoice_type' => !empty($invoiceVerificationDTO->getInvoiceType()) ?
                $invoiceVerificationDTO->getInvoiceType() : 0,
            'invoice_number' => $invoiceVerificationDTO->getInvoiceNumber(),
            'invoice_code' => $invoiceVerificationDTO->getInvoiceCode(),
            'invoice_validation_code' => $invoiceVerificationDTO->getInvoiceValidationCode(),
            'verification_result' => $invoiceVerificationDTO->getVerificationResult(),
        ];
        if(!empty($invoiceVerificationDTO->getInvoiceTime())){
            $insertedInvoiceVerification['invoice_time'] = $invoiceVerificationDTO->getInvoiceTime();
        }
        if(!empty($invoiceVerificationDTO->getTaxInclusiveAmount())){
            $insertedInvoiceVerification['tax_inclusive_amount'] = $invoiceVerificationDTO->getTaxInclusiveAmount();
        }
        if(!empty($invoiceVerificationDTO->getTaxExclusiveAmount())){
            $insertedInvoiceVerification['tax_exclusive_amount'] = $invoiceVerificationDTO->getTaxExclusiveAmount();
        }

        $invoiceVerificationRepository->insertInvoiceVerification($insertedInvoiceVerification);
    }
}