<?php

namespace App\Services\Ekuaibao\EkuaibaoBusiness;

use App\Entities\ProviderMerchantPayment\ProviderMerchantInvoiceDTO;
use App\Exceptions\BusinessException;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoAttachmentService;
use Yanqu\YanquPhplib\Exception\CurlException;

class EkuaibaoAttachmentBusinessService
{
    /**
     * @param ProviderMerchantInvoiceDTO[] $providerMerchantInvoiceDTOs
     * @return list<array{key: string, fileId: string, fileName: string}>
     * @throws CurlException|BusinessException
     */
    public function uploadAndGetAttachmentsByProviderMerchantInvoiceDTOs(array $providerMerchantInvoiceDTOs = []): array
    {
        $attachments = [];
        $ekuaibaoAttachmentService = app(EkuaibaoAttachmentService::class);
        foreach ($providerMerchantInvoiceDTOs as $invoice) {
            $fileUrl = $invoice->getFileUrl();
            //名字从url中获取
            $name = basename(parse_url($fileUrl, PHP_URL_PATH));
            //上传文件
            $uploadResult = $ekuaibaoAttachmentService->uploadAttachment($name, $fileUrl);
            $attachments[] = $uploadResult['value'] ?? [];
        }
        return $attachments;
    }
}