<?php

namespace App\Services\Ekuaibao\EkuaibaoBusiness;

use App\Constants\Ekuaibao\EkuaibaoCallbackConstants;
use App\Constants\ProviderMerchantPayment\MessageConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantPrepaymentConstants;
use App\Entities\Ekuaibao\Callback\FlowPaidCallbackDTO;
use App\Exceptions\BusinessException;
use App\Models\ProviderMerchantInvoiceReconciliationApplication;
use App\Models\ProviderPrestore;
use App\Repositories\Ekuaibao\EkuaibaoCallBackRepository;
use App\Repositories\ProviderMerchantPayment\InvoiceReconciliationApplicationRepository;
use App\Repositories\ProviderMerchantPayment\OrderSettleRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantPrepaymentRepository;
use App\Services\ProviderMerchantPayment\Prepayment\ProviderMerchantPrepaymentService;
use App\Services\ProviderMerchantPayment\Reconciliation\ProviderMerchantInvoiceReconciliationService;
use Yanqu\YanquPhplib\Curl\CurlUtil;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\OrderSettle\OrderSettleDealStatusConstants;
use Yanqu\YanquPhplib\Utils\MoneyCalculatorUtil;
use Yanqu\YanquPhplib\WeChatWorkRobot\SendWeChatWorkRobotNotice;
use Yanqu\YanquPhplib\WeChatWorkRobot\WebhookConstants;
use Yanqu\YanquPhplib\YqLog\YqLog;

class EkuaibaoCallbackService
{
    /**
     * 单据已支付/审批完成回调
     * @param $params
     * @return void
     * @throws BusinessException
     * @throws CurlException
     */
    public function flowPaidCallback($params)
    {
        //回调记录保存到数据库中
        $ekuaibaoCallBack = [
            'content' => json_encode($params, JSON_UNESCAPED_UNICODE),
        ];
        $ekuaibaoCallBackRepository = app(EkuaibaoCallBackRepository::class);
        $callbackId = $ekuaibaoCallBackRepository->addCallbackAndGetId($ekuaibaoCallBack);
        $flowPaidCallBackDTO = new FlowPaidCallbackDTO($params);

        //判断单据状态和支付币种
        if ($flowPaidCallBackDTO->getState() != 'paid' ||
            $flowPaidCallBackDTO->getPayMoney()->getStandardStrCode() != 'CNY') {
            return;
        }

        $isUpdated = false;
        //先匹配结算申请
        $settleId = $this->updateOrderSettleToPaid($flowPaidCallBackDTO);
        if ($settleId > 0) {
            $isUpdated = true;
            $ekuaibaoCallBackRepository->updateCallback($callbackId, [
                'match_type' => EkuaibaoCallbackConstants::MATCH_TYPE_PROVIDER_MERCHANT_SETTLE,
                'match_id' => $settleId
            ]);
        }

        //先匹配预存申请
        if (!$isUpdated) {
            $prepaymentId = $this->updatePrepaymentToPaid($flowPaidCallBackDTO);
            if ($prepaymentId > 0) {
                $isUpdated = true;
                $ekuaibaoCallBackRepository->updateCallback($callbackId, [
                    'match_type' => EkuaibaoCallbackConstants::MATCH_TYPE_PROVIDER_MERCHANT_PREPAYMENT,
                    'match_id' => $prepaymentId
                ]);
            }
        }

        //没匹配到预存申请，再匹配发票核销申请
        if (!$isUpdated) {
            $reconciliationId = $this->updateReconciliationToPaid($flowPaidCallBackDTO);
            if ($reconciliationId > 0) {
                $ekuaibaoCallBackRepository->updateCallback($callbackId, [
                    'match_type' => EkuaibaoCallbackConstants::MATCH_TYPE_PROVIDER_MERCHANT_INVOICE_RECONCILIATION,
                    'match_id' => $reconciliationId
                ]);
            }
        }
    }

    /**
     * 根据回调数据将预存申请置为已打款
     *
     * @param FlowPaidCallbackDTO $flowPaidCallBackDTO
     * @return false|int
     * @throws CurlException|BusinessException
     */
    private function updatePrepaymentToPaid(FlowPaidCallbackDTO $flowPaidCallBackDTO)
    {
        $prepaymentRepository = app(ProviderMerchantPrepaymentRepository::class);
        /** @var $prepayment ProviderPrestore */
        $prepayment = $prepaymentRepository->getPrepaymentByFlowId($flowPaidCallBackDTO->getFlowId());
        if (empty($prepayment)) {
            return false;
        }
        if ($prepayment->status != ProviderMerchantPrepaymentConstants::STATUS_APPLIED) {
            $errorMessage = MessageConstants::PREPAYMENT_STATUS_NOT_ALLOW_TO_UPDATE_TO_PAID;
            $context = [
                'message' => $errorMessage,
                'flowPaidCallBackDTO' => $flowPaidCallBackDTO->toArray(),
                'prepayment' => $prepayment->toArray()];
            $this->handleUpdateToPaidError($errorMessage, $context);
        }
        if (bccomp($flowPaidCallBackDTO->getPayMoney()->getStandard(), $prepayment->amount, 2) != 0) {
            $errorMessage = MessageConstants::PREPAYMENT_AMOUNT_AND_CALLBACK_NOT_EQUAL;
            $context = [
                'message' => $errorMessage,
                'flowPaidCallBackDTO' => $flowPaidCallBackDTO->toArray(),
                'prepayment' => $prepayment->toArray()];
            $this->handleUpdateToPaidError($errorMessage, $context);
        }

        //更新预存申请状态
        $prepaymentService = new ProviderMerchantPrepaymentService();
        $response = $prepaymentService->setPrepaymentStatus($prepayment->prestoreid, 'complete');
        if ($response === false) {
            $errorMessage = MessageConstants::UPDATE_PREPAYMENT_STATUS_TO_PAID_FAILED;
            $context = [
                'message' => $errorMessage,
                'flowPaidCallBackDTO' => $flowPaidCallBackDTO->toArray(),
                'prepayment' => $prepayment->toArray()];
            $this->handleUpdateToPaidError($errorMessage, $context);
        } else {
            return $prepayment->prestoreid;
        }
    }

    /**
     * @param string $errorMessage
     * @param array $context
     * @return mixed
     * @throws BusinessException
     * @throws CurlException
     */
    private function handleUpdateToPaidError(string $errorMessage, array $context)
    {
        //记log
        YqLog::logger("finance:providerMerchantPayment:ekuaibao")->warning($errorMessage, $context);
        //机器人报警
        $messagePrefix = !empty($context['prepayment']) ? "【供应商预存】" : "";
        $messagePrefix = !empty($context['reconciliation']) ? "【供应商发票核销】" : $messagePrefix;
        if (!empty($context['orderSettle'])) {
            $messagePrefix = "【供应商结算】";
        }
        SendWeChatWorkRobotNotice::senMarkdownNotice(WebhookConstants::FINANCE_MONITOR,
            "$messagePrefix" . json_encode($context, 320) . "<@***********>");
        //抛异常
        throw new BusinessException($errorMessage);
    }

    /**
     * @param FlowPaidCallbackDTO $flowPaidCallBackDTO
     * @return false|int
     * @throws BusinessException|CurlException
     */
    public function updateReconciliationToPaid(FlowPaidCallbackDTO $flowPaidCallBackDTO)
    {
        $reconciliationRepository = app(InvoiceReconciliationApplicationRepository::class);
        $reconciliation = $reconciliationRepository->getReconciliationApplicationByFlowId(
            $flowPaidCallBackDTO->getFlowId());
        if (empty($reconciliation)) {
            return false;
        }
        /** @var $reconciliation ProviderMerchantInvoiceReconciliationApplication */
        if ($reconciliation->process_status !=
            ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_TO_BE_REVIEWED) {
            $errorMessage = MessageConstants::RECONCILIATION_STATUS_NOT_ALLOW_TO_UPDATE_TO_PAID;
            $context = [
                'message' => $errorMessage,
                'flowPaidCallBackDTO' => $flowPaidCallBackDTO->toArray(),
                'reconciliation' => $reconciliation->toArray()];
            $this->handleUpdateToPaidError($errorMessage, $context);
        }

        if (bccomp($flowPaidCallBackDTO->getWrittenOffMoney()->getStandard(), $reconciliation->reconciliation_amount, 2)
            != 0) {
            $errorMessage = MessageConstants::RECONCILIATION_AND_CALLBACK_AMOUNT_NOT_EQUAL;
            $context = [
                'message' => $errorMessage,
                'flowPaidCallBackDTO' => $flowPaidCallBackDTO->toArray(),
                'reconciliation' => $reconciliation->toArray()];
            $this->handleUpdateToPaidError($errorMessage, $context);
        }

        $providerMerchantInvoiceReconciliationService = new ProviderMerchantInvoiceReconciliationService();
        $providerMerchantInvoiceReconciliationService->confirmReconcile([
            'reconciliation_application_id' => $reconciliation->id,
            'admin_account_id' => env('ADMIN_ACCOUNT_ID_SYSTEM')
        ]);
        return $reconciliation->id;
    }

    /**
     * @throws BusinessException
     * @throws CurlException
     */
    public function updateOrderSettleToPaid(FlowPaidCallbackDTO $flowPaidCallBackDTO)
    {
        $orderSettleRepository = app(OrderSettleRepository::class);
        $orderSettle = $orderSettleRepository->getOrderSettleByFlowId($flowPaidCallBackDTO->getFlowId());
        if (empty($orderSettle)) {
            return 0;
        }

        //判断状态是否正确
        if ($orderSettle->dealstatus != OrderSettleDealStatusConstants::WAIT_SETTLE) {
            $errorMessage = MessageConstants::SETTLE_STATUS_NOT_ALLOW_TO_UPDATE_TO_PAID;
            $context = [
                'message' => $errorMessage,
                'flowPaidCallBackDTO' => $flowPaidCallBackDTO->toArray(),
                'orderSettle' => $orderSettle->toArray()];
            $this->handleUpdateToPaidError($errorMessage, $context);
        }

        //比较金额是否相等
        $settleAmount = MoneyCalculatorUtil::amountSub($orderSettle->applyamt, $orderSettle->prestore);
        if (!MoneyCalculatorUtil::isEqual($flowPaidCallBackDTO->getPayMoney()->getStandard(), $settleAmount)) {
            $errorMessage = MessageConstants::SETTLE_PAYABLE_AMOUNT_AND_CALLBACK_AMOUNT_NOT_EQUAL;
            $context = [
                'message' => $errorMessage,
                'flowPaidCallBackDTO' => $flowPaidCallBackDTO->toArray(),
                'orderSettle' => $orderSettle->toArray()];
            $this->handleUpdateToPaidError($errorMessage, $context);
        }

        //获取结算明细总金额
        $settleItemAmount = $orderSettleRepository->getSettleItemAmountSumBySettleId($orderSettle->settleid);
        //请求确认结算接口
        $requestParams = [
            'sattleids' => $orderSettle->settleid,
            'amount' => $settleItemAmount,
            'dealdesc' => '易快报已支付，将此申请自动置为已结算',
            'newamount' => '',
        ];
        $url = config("constants.admin_url") . "/Sattle/sattle.html";
        $originRes = CurlUtil::postForm($url, $requestParams);

        // 处理返回值
        $res = trim($originRes, "\xEF\xBB\xBF");
        $res = json_decode($res, true);
        if (isset($res['status']) && $res['status'] == "1") {
            return $orderSettle->settleid;
        } else {
            $errorMessage = MessageConstants::UPDATE_SETTLE_STATUS_TO_PAID_FAILED;
            $context = [
                'message' => $errorMessage,
                'flowPaidCallBackDTO' => $flowPaidCallBackDTO->toArray(),
                'orderSettle' => $orderSettle->toArray(),
                'requestParams' => $requestParams,
                'originRes' => $originRes
            ];
            $this->handleUpdateToPaidError($errorMessage, $context);
        }
    }
}