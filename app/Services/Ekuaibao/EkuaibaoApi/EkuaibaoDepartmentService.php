<?php

namespace App\Services\Ekuaibao\EkuaibaoApi;

use App\Constants\Ekuaibao\EkuaibaoUrlConstants;
use Yanqu\YanquPhplib\Curl\CurlUtil;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\YqLog\YqLog;

class EkuaibaoDepartmentService
{
    /**
     * @param $idOrCode
     * @param $departmentBy
     * @return mixed
     * @throws CurlException
     */
    public function getDepartmentByIdOrCode($idOrCode, $departmentBy = 'id')
    {
        $accessToken = app(EkuaibaoAuthorityService::class)->getAccessToken();
        $url = env('EKUAIBAO_URL_PREFIX') . EkuaibaoUrlConstants::GET_DEPARTMENT_BY_ID_OR_CODE . "$" . $idOrCode .
            '?accessToken=' . $accessToken;
        $response = CurlUtil::get($url);
        YqLog::logger('finance:ekuaibao')->info('getDepartmentByIdOrCode', [
            'idOrCode' => $idOrCode,
            'response' => $response,
            'departmentBy' => $departmentBy,
        ]);
        return json_decode($response, true);
    }
}