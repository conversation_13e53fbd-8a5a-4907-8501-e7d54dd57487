<?php

namespace App\Services\Ekuaibao\EkuaibaoApi;

use App\Constants\Ekuaibao\EkuaibaoUrlConstants;
use Yanqu\YanquPhplib\Curl\CurlUtil;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\YqLog\YqLog;

class EkuaibaoPayeeService
{
    /**
     * 获取收款人信息
     * @param string $cardNo
     * @param string $ids
     * @param string $names
     * @return mixed
     * @throws CurlException
     */
    public function getPayeeInfo(string $cardNo = '', string $ids = '', string $names = '')
    {
        $accessToken = app(EkuaibaoAuthorityService::class)->getAccessToken();
        $url = env('EKUAIBAO_URL_PREFIX') . EkuaibaoUrlConstants::GET_PAYEE_INFOS . '?accessToken=' . $accessToken;
        $params = [
            'ids' => $ids,
            'names' => $names,
            'cardNos' => $cardNo,
            'start' => 0,
            'count' => 1,
            //get方法中http_build_query会把bool类型的参数转为1或0，所以这里用字符串
            'active' => "true",
        ];
        $response = CurlUtil::get($url, $params,[],[CURLOPT_TIMEOUT => 10]);
        YqLog::logger('finance:ekuaibao')->info('getPayeeInfo', [
            'params' => $params,
            'response' => $response,
        ]);
        $payeeInfos = json_decode($response, true);
        return $payeeInfos['items'][0] ?? [];
    }
}