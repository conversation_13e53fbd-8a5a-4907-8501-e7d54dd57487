<?php

namespace App\Services\Ekuaibao\EkuaibaoApi;

use App\Constants\Ekuaibao\EkuaibaoPropertyConstants;
use App\Utils\EnvironmentHelper;

class EkuaibaoPropertyService
{
    public function getLegalEntityPropertyName(): string
    {
        if(EnvironmentHelper::isProduction()){
            return EkuaibaoPropertyConstants::PRODUCTION_LEGAL_ENTITY_NAME;
        }else{
            return EkuaibaoPropertyConstants::TEST_LEGAL_ENTITY_NAME;
        }
    }

    public function getSubmitterDepartmentPropertyName(): string
    {
        if(EnvironmentHelper::isProduction()){
            return EkuaibaoPropertyConstants::PRODUCTION_SUBMITTER_DEPARTMENT_NAME;
        }else{
            return EkuaibaoPropertyConstants::TEST_SUBMITTER_DEPARTMENT_NAME;
        }
    }
    public function getDepartmentAttributePropertyName(): string
    {
        if(EnvironmentHelper::isProduction()){
            return EkuaibaoPropertyConstants::PRODUCTION_DEPARTMENT_ATTRIBUTE_NAME;
        }else{
            return EkuaibaoPropertyConstants::TEST_DEPARTMENT_ATTRIBUTE_NAME;
        }
    }

    public function getPaymentTypePropertyName(): string
    {
        if(EnvironmentHelper::isProduction()){
            return EkuaibaoPropertyConstants::PRODUCTION_PAYMENT_TYPE_NAME;
        }else{
            return EkuaibaoPropertyConstants::TEST_PAYMENT_TYPE_NAME;
        }
    }

    public function getIsFinalPaymentPropertyName(): string
    {
        if(EnvironmentHelper::isProduction()){
            return EkuaibaoPropertyConstants::PRODUCTION_IS_FINAL_PAYMENT_NAME;
        }else{
            return EkuaibaoPropertyConstants::TEST_IS_FINAL_PAYMENT_NAME;
        }
    }

    public function getParticipantPropertyName(): string
    {
        if(EnvironmentHelper::isProduction()){
            return EkuaibaoPropertyConstants::PRODUCTION_PARTICIPANT_NAME;
        }else{
            return EkuaibaoPropertyConstants::TEST_PARTICIPANT_NAME;
        }
    }

    public function getArchiveFlagPropertyName(): string
    {
        if(EnvironmentHelper::isProduction()){
            return EkuaibaoPropertyConstants::PRODUCTION_ARCHIVE_FLAG_NAME;
        }else{
            return EkuaibaoPropertyConstants::TEST_ARCHIVE_FLAG_NAME;
        }
    }

    public function getCallbackFlagPropertyName(): string
    {
        if(EnvironmentHelper::isProduction()){
            return EkuaibaoPropertyConstants::PRODUCTION_CALLBACK_FLAG_NAME;
        }else{
            return EkuaibaoPropertyConstants::TEST_CALLBACK_FLAG_NAME;
        }
    }

    public function getExpectedInvoiceDatePropertyName(): string
    {
        if(EnvironmentHelper::isProduction()){
            return EkuaibaoPropertyConstants::PRODUCTION_EXPECTED_INVOICE_DATE_NAME;
        }else{
            return EkuaibaoPropertyConstants::TEST_EXPECTED_INVOICE_DATE_NAME;
        }
    }

    public function getContractQuantity(): string
    {
        if(EnvironmentHelper::isProduction()){
            return EkuaibaoPropertyConstants::PRODUCTION_CONTRACT_QUANTITY_NAME;
        }else{
            return EkuaibaoPropertyConstants::TEST_CONTRACT_QUANTITY_NAME;
        }
    }
}