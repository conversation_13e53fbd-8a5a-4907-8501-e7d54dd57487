<?php

namespace App\Services\Prepayment;

use App\Models\PrepaymentAmount;
use App\Models\PrepaymentAmountLog;
use App\Services\BaseService;
use Illuminate\Support\Facades\DB;

class CancelPrepaymentService extends BaseService
{

    public function cancel($spendData)
    {
        try {
            DB::beginTransaction();
            $res = PrepaymentAmountLog::getCancelledData($spendData['spendid']);
            if (empty($res)) {
                throw new \Exception('取消记录spendid：' . $spendData['spendid'] . '该记录已取消或未同步' . PHP_EOL);
            }
            // 对发票、预存表加锁
            PrepaymentAmount::getBalanceAndAddLock(0, $spendData['supplierid'], time());
            // 作废消费记录，并退回发票金额
            foreach ($res as $item) {
                $paymentUpdate = [
                    'consume_amount' => DB::raw('consume_amount - ' . $item['amount']),
                    'remain_amount' => DB::raw('remain_amount + ' . $item['amount']),
                ];
                PrepaymentAmount::updatePrepaymentByPrepaymentId($item['prepayment_id'], $paymentUpdate);
                PrepaymentAmountLog::cancelPrepayment($item['id']);

            }
            DB::commit();
            return true;

        }catch (\Exception $e) {
            DB::rollBack();
            echo $e->getMessage();
            return false;
        }
    }
}