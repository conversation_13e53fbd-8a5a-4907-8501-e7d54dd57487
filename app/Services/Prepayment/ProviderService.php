<?php

namespace App\Services\Prepayment;

use App\Services\BaseService;

class ProviderService extends BaseService
{
    // 业务线
    const PRODUCT_TO_PROVIDER = [
        // 材料测试
//        11=>1,
        // 生物测试 杭州研选
        15 => 19,
        // 环境测试  南京研科
        17 => 12,
        // 成分分析 广州研拓
//        22 => 12,
        // 纳米材料 郑州费曼
        20 => 13,

    ];

    // 高端测试与材料测试
    const MATERIAL_HIGH = [
        11,
        19
    ];

    // 实验室
    const PROVIDER_TO_NAME = [
        1 => '杭州研趣',
        19 => '杭州研选',
        12 => '南京研科',
        13 => '郑州费曼',
        18 => '南京研友',
        5 => '长沙研昊',
        11 => '杭州研友',
    ];

    const LAB_TO_PROVIDER = [
        // 南京材料
        2522=>18,
        3176=>18,
        // 长沙材料
        2290=>5,
        2317=>5,
        // 杭州材料
        2084=>11,
    ];

    /**
     * 获取订单业务线
     * @return void
     */
    public function getOrderProvider($orderInfo) :int
    {
        // 默认研趣
        $providerId = 1;
        // 生物、环境、纳米材料等
        if (in_array($orderInfo['categoryid'], array_keys(self::PRODUCT_TO_PROVIDER))) {
            $providerId = self::PRODUCT_TO_PROVIDER[$orderInfo['categoryid']];
        }
        // 材料测试、高端测试
        if (in_array($orderInfo['categoryid'], self::MATERIAL_HIGH)) {
            // 地区
            // 南京
            if (in_array($orderInfo['city'], [108,109,110,111,112,113,114,115,116,117,118,119,120])) {
                $providerId = 18;
            }
            // 长沙
            if (in_array($orderInfo['city'], [230,217,218,219,220,221,222,223,224,225,226,227,228,229])) {
                $providerId = 5;
            }
            // 杭州
            if (in_array($orderInfo['city'], [121,123,124,125,126,127,128,129,130,3289,131])) {
                $providerId = 11;
            }
            // 实验室
            if (in_array($orderInfo['sendsupplierid'], array_keys(self::LAB_TO_PROVIDER))) {
                $providerId = self::LAB_TO_PROVIDER[$orderInfo['sendsupplierid']];
            }
        }
        return $providerId;
    }



}