<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Yanqu\YanquPhplib\Invoice\Constants\InvoiceOperateReasonConstant;
use App\Constants\Invoice\OrderInvoiceApplyConstant;
use App\Constants\InvoiceConstants;
use App\Entities\FinanceBackTicketDTO;
use App\Exceptions\InvoiceException;
use App\Models\AccountInfo;
use App\Models\Admin;
use App\Models\FinanceTicketApply;
use App\Models\FinanceTicketHistory;
use App\Models\FinanceTicketInfo;
use App\Models\OrderExtend;
use App\Models\OrderInfo;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Yanqu\YanquPhplib\YqLog\YqLog;

class FinanceService
{
    /**
     * @throws InvoiceException
     * @throws \Exception
     */
    public function backTicket(FinanceBackTicketDTO $financeBackTicketDTO)
    {
        $apply = $this->findBackTicketApplyInfoByApplyId($financeBackTicketDTO->applyId);
        if (empty($apply)) {
            throw new InvoiceException('发票申请不存在');
        }

        $accountInfo = AccountInfo::query()->where('accountid', $apply->applyaccount)->value('contacter');
        $orderIds = explode(',', $apply->orderids);
        $orderInfos = OrderInfo::query()->whereIn('oid', $orderIds)
            ->where('isvoid', 0)->select(['oid', 'creditpayamount', 'osn'])->get();
        if ($orderInfos->isEmpty()) {
            throw new InvoiceException('退票失败');
        }

        $ticketInfo = FinanceTicketInfo::where('ticketinfoid', $apply->ticketinfoid)
            ->select('ticketype')->first();
        $isAdminOperate = !empty($financeBackTicketDTO->adminId);
        if ($isAdminOperate) {
            $adminRealName = Admin::query()->where('adminid', $financeBackTicketDTO->adminId)
                ->value('realname');
        }

        $this->checkBackTicketApplyPreConditions($apply, $orderInfos, $financeBackTicketDTO, $ticketInfo);

        DB::beginTransaction();
        try {
            if (!empty($financeBackTicketDTO->backReason)) {
                $reason = InvoiceOperateReasonConstant::getDesc($financeBackTicketDTO->backReason) .
                    ",备注: {$financeBackTicketDTO->remark}";
            } else {
                $reason = $financeBackTicketDTO->remark;
            }

            if ($apply->parentickid != '-1') {
                $updateRes = FinanceTicketApply::where('applyid', $apply->applyid)
                    ->update(['ticketstatus' => 98, 'backticketremark' => $reason]);
            } else {
                $updateRes = FinanceTicketApply::where('applyid', $apply->applyid)
                    ->update(['backticketremark' => $reason]);
            }

            if (empty($updateRes)) {
                throw new InvoiceException('操作失败,请重试');
            }

            $postNode = '';
            if ($isAdminOperate) {
                $postNode .= "{$adminRealName}";
            } else {
                $postNode .= "用户({$accountInfo})";
            }
            $postNode .= "申请退票，原因：{$reason}";

            if ("-1" == $apply->parentickid) {
                $applyIds = FinanceTicketApply::where('parentickid', $apply->applyid)
                    ->pluck('applyid')->all();
                $historyData = [];
                foreach ($applyIds as $applyId) {
                    $historyData[] = [
                        'applyid' => $applyId,
                        'postnode' => $postNode,
                        'postime' => time(),
                    ];
                }
                FinanceTicketHistory::query()->insert($historyData);
                $updateRes = FinanceTicketApply::where('parentickid', $apply->applyid)
                    ->update([
                        'ticketstatus' => OrderInvoiceApplyConstant::TICKET_STATUS_WAIT_RED,
                        'backticketremark' => $financeBackTicketDTO->remark,
                    ]);
                if (empty($updateRes)) {
                    throw new InvoiceException('操作失败,请重试');
                }
            }

            $orderExtendMap = OrderExtend::query()->whereIn('oid', $orderIds)
                ->pluck('backticket', 'oid');
            $orderSnMap = $orderInfos->pluck('osn', 'oid');

            [$orderExtendBackTicketIncList, $orderExtendInsertList] = [[], []];
            foreach ($orderIds as $v) {
                if ($v > 0) {
                    if ($orderExtendMap->has($v)) {
                        $orderExtendBackTicketIncList[] = $v;
                    } else {
                        $osn = $orderSnMap->get($v);
                        $orderExtendInsertList[] = [
                            'oid' => $v,
                            'osn' => $osn,
                            'backticket' => 1,
                        ];
                    }
                }
            }
            if (!empty($orderExtendBackTicketIncList)) {
                OrderExtend::query()->whereIn('oid', $orderExtendBackTicketIncList)->increment('backticket');
            }

            if (!empty($orderExtendInsertList)) {
                OrderExtend::query()->insert($orderExtendInsertList);
            }
            //日志记录
            FinanceTicketHistory::create([
                'applyid' => $apply->applyid,
                'postnode' => $postNode,
                'postime' => time()]);
            if ($apply->parentickid == -1) {
                $apply->invoiceno = '';
            }
            if (!empty($financeBackTicketDTO->backReason)) {
                app(InvoiceOperateReasonService::class)->insertByBackTicket($apply, $financeBackTicketDTO);
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            if ($exception instanceof InvoiceException) {
                throw $exception;
            } else {
                YqLog::logger('finance:invoice')->error($exception);
                throw new InvoiceException('申请退票失败,请稍后再试');
            }
        }
        return true;
    }

    /**
     * @param int $applyId
     * @return FinanceTicketApply|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    public function findBackTicketApplyInfoByApplyId(int $applyId)
    {
        return FinanceTicketApply::where("applyid", $applyId)->select(['autoopen', 'applyid',
            'ticketinfoid', 'orderids', 'ticketstatus', 'parentickid', 'receivestatus', 'orderids', 'applyaccount'
        ,'invoiceno'])
            ->first();
    }

    /**
     * 判断当前发票是否可退票
     * @param FinanceTicketApply $apply
     * @param Collection|OrderInfo[] $orderInfos
     * @param FinanceBackTicketDTO $financeBackTicketDTO
     * @param FinanceTicketInfo $financeTicketInfo
     * @return void
     * @throws InvoiceException
     */
    public function checkBackTicketApplyPreConditions(FinanceTicketApply   $apply,
                                                      Collection           $orderInfos,
                                                      FinanceBackTicketDTO $financeBackTicketDTO,
                                                      FinanceTicketInfo    $financeTicketInfo
    )
    {
        //用户申请且操作人与申请人id不一致
        if ($financeBackTicketDTO->accountId > 0
            && $apply->applyaccount != $financeBackTicketDTO->accountId) {
            throw new InvoiceException('退票失败');
        }
        if (in_array((int)$apply->ticketstatus,
            [
                OrderInvoiceApplyConstant::TICKET_STATUS_ALREADY_RED,
                OrderInvoiceApplyConstant::TICKET_STATUS_WAIT
            ])) {
            throw new InvoiceException('该发票已红冲或退票中');
        }

        $onlinePayFlag = true;
        foreach ($orderInfos as $orderInfo) {
            if ($orderInfo->creditpayamount > 0) {
                $onlinePayFlag = false;
                break;
            }
        }

        if (100 == (int)$apply->receivestatus && !$onlinePayFlag) {
            throw new InvoiceException('发票已还款，无法退票');
        }

        if ("-1" == $apply->parentickid) {
            $applyChildren = FinanceTicketApply::where("parentickid", $apply->applyid)
                ->select('applyid', 'orderids', 'ticketstatus', 'parentickid', 'receivestatus')->get();
            /** @var Collection|FinanceTicketApply[] $applyChildren */
            foreach ($applyChildren as $v) {
                if (OrderInvoiceApplyConstant::TICKET_STATUS_INVOICED != (int)$v->ticketstatus) {
                    throw new InvoiceException('有不可退的子票');
                }
                if (OrderInvoiceApplyConstant::TICKET_STATUS_INVOICED == (int)$v->receivestatus && !$onlinePayFlag) {
                    throw new InvoiceException('有子发票已回款，无法退票');
                }
            }
        }

        if ($apply->parentickid > 0) {
            $childrenTicketApplyResult = FinanceTicketApply::where('parentickid', $apply->parentickid)
                ->where('receivestatus', 100)
                ->where('applyid', '!=',$apply->applyid)
                ->first();
            if ($childrenTicketApplyResult) {
                throw new InvoiceException('有其他子发票已回款，无法退票');
            }
        }

        if (in_array($financeTicketInfo->ticketype, [
            InvoiceConstants::VAT_GENERAL_INVOICES,
            InvoiceConstants::VAT_SPECIAL_INVOICES,
        ])) {
            throw new InvoiceException('纸质票退票请联系客服电话');
        }
    }

    /**
     * @param  int  $applyId
     *
     * @return FinanceTicketApply|object|null
     */
    public function findOpenReceiptTicketInfo(int $applyId)
    {
        return FinanceTicketApply::query()->where('applyid', $applyId)
            ->select([
                'applyid',
                'providerid',
                'ticketstatus',
                'ticketvals',
                'ticketinfoid',
                'applyaccount',
                'applyamount',
                'ticketremark',
            ])
            ->with([
                'financeTicketInfo:ticketinfoid,ticketype,title,registrationo,registphone,registaddress,depositbank,banksn',
                'financeTicketApplyItem' => function (HasMany $query) {
                    return $query->where('isvoid', 0)->select([
                        'applyid',
                        'dproductname',
                        'dproductnums',
                        'dproductunit',
                        'confirmamount',
                        'orderid',
                        'dproductspec',
                        'tax_service_code',
                    ]);
                },
                'financeProvider:providerid,taxnumber,phone,address,bank,companyaccount'
            ])
            ->first();
    }
}