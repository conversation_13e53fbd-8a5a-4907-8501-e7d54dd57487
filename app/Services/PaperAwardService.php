<?php

namespace App\Services;

use App\Constants\PaperAwardConstants;
use App\Exceptions\PaperAwardException;
use App\Repositories\UserThank\UserThankRepository;
use App\Utils\EmailUtil;

class PaperAwardService
{

    public const BADGES = [
        'meteor'    => [
            'name'   => '流星徽章',
            'url'    =>
                "https://cdn0.shiyanjia.com/c/front-end/activity/assets/paper-acknowledgments/badges/meteor-badge_70f77f07.png",
            'letter' => '你就像划破夜空的流星，瞬间点亮了科研的天际。' .
                '你的努力和热情如流星般燃烧，预示着辉煌成就的开始。继续追逐，你的光芒将愈发耀眼！',
        ],
        'comet'     => [
            'name'   => '彗星徽章',
            'url'    =>
                "https://cdn0.shiyanjia.com/c/front-end/activity/assets/paper-acknowledgments/badges/comet-badge_cefc9795.png",
            'letter' => '你的科研之旅犹如一颗彗星，横跨知识的宇宙，留下了灿烂的尾迹。' .
                '你的每次出现都带来新的惊喜和启示。继续探索，让你的发现点亮科研的未来！',
        ],
        'satellite' => [
            'name'   => '卫星徽章',
            'url'    =>
                "https://cdn0.shiyanjia.com/c/front-end/activity/assets/paper-acknowledgments/badges/satellite-badge_d8c14360.png",
            'letter' => '你像宇宙中稳定的卫星，绕着知识的星球旋转，观察和记录下每一个重要发现。' .
                '你的坚持和专注为科学研究提供了宝贵的新视野。继续环绕，新知识的地平线在等待你！',
        ],
        'planet'    => [
            'name'   => '行星徽章',
            'url'    =>
                "https://cdn0.shiyanjia.com/c/front-end/activity/assets/paper-acknowledgments/badges/planet-badge_d0d6bd7b.png",
            'letter' => '你在科研的宇宙中稳如行星，按照知识的轨道孜孜不倦地运行。' .
                '你的每一圈旅程都积累了丰富的经验。继续旋转，让你的智慧成为引领进步的力量！',
        ],
        'stellar'   => [
            'name'   => '恒星徽章',
            'url'    =>
                "https://cdn0.shiyanjia.com/c/front-end/activity/assets/paper-acknowledgments/badges/stellar-badge_4e8170b5.png",
            'letter' => '经过日夜求索，你已经在所属领域小有所成，将人类的知识领域向前推进了一小步，' .
                '如同一颗耀眼的恒星闪耀在科研星海中。继续前进，未来将属于你！',
        ],
        'nebula'    => [
            'name'   => '星云徽章',
            'url'    =>
                "https://cdn0.shiyanjia.com/c/front-end/activity/assets/paper-acknowledgments/badges/nebula-badge_2db41f64.png",
            'letter' => '你就如同璀璨的星云，孕育着宇宙间最初的元素和最伟大的想法。'
                . '你的创造力和想象力是无限的源泉。继续扩散，让你的创新催生新的知识星系！',
        ],
    ];

    public const NEBULA_JOURNALS = [
        "nature", "cell", "science"
    ];

    /**
     * @throws PaperAwardException
     * @throws \Swift_RfcComplianceException
     * @throws \Exception
     */
    public function sendSuccessEmail($thankId): void
    {
        $thinkInfo = UserThankRepository::getUserThankInfoById($thankId);
        if (empty($thinkInfo)) {
            throw new PaperAwardException("获取论文致谢信息失败");
        }
        if ($thinkInfo->success_email_sent === 1) {
            throw new PaperAwardException("邮件已经发过了");
        }
        if ($thinkInfo->status !== PaperAwardConstants::STATUS_PAYED
            &&
            $thinkInfo->status !== PaperAwardConstants::STATUS_WAIT_PAY
        ) {
            throw new PaperAwardException("论文致谢信息还未通过");
        }
        $badge = $this->getBadge($thinkInfo->ifvalue, $thinkInfo->journalname);
        $awardData = base64_encode(rawurlencode(json_encode([
            "badge"        => $badge,
            "journalName"  => $thinkInfo->journalname,
            "ifValue"      => $thinkInfo->ifvalue,
            "sciPartition" => PaperAwardConstants::SCI_PARTITION_REF[$thinkInfo->sci_partition],
            "title"        => $thinkInfo->substance1,
        ])));
        $buttonUrl = "https://activity.shiyanjia.com/paper-acknowledgments/capture.html?" .
            http_build_query(
                [
                    "data"  => $awardData,
                    "token" => md5($awardData . "shiyanjia@2024")
                ]
            );
        if(strpos($thinkInfo->email,"@") !== false) {
            try {
                EmailUtil::sendEmail('请查收你的科研徽章｜科学指南针', [
                    "thanker"       => $thinkInfo->thanker,
                    "badgeImageUrl" => self::BADGES[$badge]["url"],
                    "badgeName"     => self::BADGES[$badge]["name"],
                    "journalName"   => $thinkInfo->journalname,
                    "ifValue"       => $thinkInfo->ifvalue,
                    "sciPartition"  => PaperAwardConstants::SCI_PARTITION_REF[$thinkInfo->sci_partition],
                    "title"         => $thinkInfo->substance1,
                    "tribute"       => self::BADGES[$badge]["letter"],
                    "buttonUrl"     => $buttonUrl,
                ],
                    $thinkInfo->email,
                    "emails.award-success"
                );
            } catch (\Exception $e) {
                //把发送状态置为发送失败
                UserThankRepository::updateUserThankValueById($thankId, "success_email_sent", 2);
                throw $e;
            }
            //把发送状态置为已发送
            UserThankRepository::updateUserThankValueById($thankId, "success_email_sent", 1);
        } else {
            //把发送状态置为发送失败
            UserThankRepository::updateUserThankValueById($thankId, "success_email_sent", 2);
        }
    }

    /**
     * @throws PaperAwardException
     */
    public function getBadge($ifValue, $journalName): string
    {
        $badgeType = "";
        if (in_array(strtolower($journalName), self::NEBULA_JOURNALS)) {
            $badgeType = "nebula";
        } elseif ($ifValue < 8) {
            $badgeType = "meteor";
        } elseif ($ifValue >= 8 && $ifValue < 16) {
            $badgeType = "comet";
        } elseif ($ifValue >= 16 && $ifValue < 24) {
            $badgeType = "satellite";
        } elseif ($ifValue >= 24 && $ifValue < 32) {
            $badgeType = "planet";
        } elseif ($ifValue >= 32) {
            $badgeType = "stellar";
        }
        if (empty($badgeType)) {
            throw new PaperAwardException("异常的徽章类型");
        }
        return $badgeType;
    }
}
