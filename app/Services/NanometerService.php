<?php

namespace App\Services;

use App\Models\NanometerOrder;
use App\Models\OrderInfo;
use App\Models\CouponItem;
use Illuminate\Support\Facades\DB;

class NanometerService
{
    public function getOrderInfo($orderId)
    {
        return (new OrderInfo())->getInfoByOid($orderId);
    }

    /**
     * 根据订单分摊订单明细的优惠金额
     * @param $orderInfo
     * @return bool
     * @throws \Exception
     */
    public function updatePriceDiscountAmount($orderInfo) :bool
    {
        $couponNo = $orderInfo['couponno'];
        $couponInfo = CouponItem::query()
            ->leftJoin('coupon', 'coupon.couponid', '=', 'coupon_item.couponid')
            ->where('couponno', $couponNo)
            ->select('coupon.categorys', 'coupon.skuids', 'coupon.brandids', 'coupon.excludebrandids')
            ->first();
        if (empty($couponInfo)) {
            throw new \Exception('优惠券未找到');
        }

        $couponBrandIds = $couponInfo['brandids'];
        $couponExcludeBrandIds = $couponInfo['excludebrandids'];
        $couponSkuIds = $couponInfo['skuids'];
        if ($couponBrandIds == -1 && empty($couponSkuIds)) {
            throw new \Exception('优惠券不对纳米商城开放');
        }
        $brandIds = $excludeBrandIds = $skuIds = [];
        if ($couponSkuIds != 0 && !empty($couponSkuIds)) {
            $skuIds = explode(',', $couponSkuIds);
        } else {
            // 可用品牌id/排除品牌id
            $brandIds = ($couponBrandIds != 0 && !empty($couponBrandIds)) ? explode(',', $couponBrandIds) : [];
            $excludeBrandIds = ($couponExcludeBrandIds != 0 && !empty($couponExcludeBrandIds)) ?
                explode(',', $couponExcludeBrandIds) : [];
        }

        $orderId = $orderInfo['oid'];
        $orderDiscountAmount = $orderInfo['discntmoney'];
        $nanometerOrderId = NanometerOrder::query()->where('oid', $orderId)->value('noid');
        if (empty($nanometerOrderId)) {
            throw new \Exception('纳米商城订单未找到');
        }
        // 明细单信息
        $priceList = NanometerOrder::query()
            ->from('nanometer_order_price as order_price')
            ->leftJoin('nanometer_product_skusale as skusale', 'skusale.skuid', '=', 'order_price.skuid')
            ->where('order_price.isvoid', 0)
            ->where('order_price.noid', $nanometerOrderId)
            ->when(!empty($skuIds), function ($query) use ($skuIds) {
                $query->whereIn('order_price.skuid', $skuIds);
            })
            ->when(!empty($brandIds), function ($query) use ($brandIds) {
                $query->whereIn('skusale.brandid', $brandIds);
            })->when(!empty($excludeBrandIds), function ($query) use ($excludeBrandIds) {
                $query->whereNotIn('skusale.brandid', $excludeBrandIds);
            })
            ->select('order_price.priceid', 'order_price.order_price2')
            ->get()
            ->toArray();
        $priceCount = sizeof($priceList);
        if (empty($priceCount)) {
            throw new \Exception('无符合优惠券对应品牌的订单');
        }
        // 计算使用了优惠券部分的总确认价格
        $detailConfirmPrice = array_column($priceList, 'order_price2');
        $totalConfirmPrice = 0;
        foreach ($detailConfirmPrice as $price) {
            $totalConfirmPrice = bcadd($totalConfirmPrice, $price, 2);
        }

        if (empty($totalConfirmPrice)) {
            throw new \Exception('订单为报价单');
        }
        $totalDiscountAmount = 0; // 已计算的优惠金额部分
        foreach ($priceList as $index => $priceInfo) {
            $orderPriceId = $priceInfo['priceid'];
            $confirmPrice = $priceInfo['order_price2'];
            if ($index + 1 == $priceCount) {
                // 最后一个明细优惠金额 = 订单优惠金额 - 已优惠总金额
                $priceDiscountAmount = bcsub($orderDiscountAmount, $totalDiscountAmount, 2);
            } else {
                // 明细最终优惠金额金额=（明细实际支付金额/使用了优惠券的明细金额的总额）*实际主单优惠金额
                $priceDiscountAmount = round(($confirmPrice/$totalConfirmPrice) * $orderDiscountAmount,2);
            }
            $totalDiscountAmount = bcadd($totalDiscountAmount, $priceDiscountAmount, 2);
            DB::table("nanometer_order_price")->where("priceid", $orderPriceId)
                ->update(["discount_amount" => $priceDiscountAmount]);
        }
        return true;
    }
}