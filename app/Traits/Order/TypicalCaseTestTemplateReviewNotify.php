<?php

namespace App\Traits\Order;

use App\Models\TypicalCaseTestTemplate;
use App\Utils\MessageUtil;

trait TypicalCaseTestTemplateReviewNotify
{
    public function notifyReview(TypicalCaseTestTemplate $template)
    {
        $template->load('operatorInfo:accountid,realname');
        $msg = "{$template->operatorInfo->realname}提交了一条“{$template->name}”的测试方法，方法编号：{$template->id}，请注意及时审核，避免影响其录入案例";
        MessageUtil::sendWechatMessage($template->reviewer, $msg);
    }

    public function notifyReviewReject(TypicalCaseTestTemplate $template, string $rejectReason)
    {
        $msg = "您提交的“{$template->name}”被驳回，方法编号：{$template->id}，原因：{$rejectReason}。请知悉";
        MessageUtil::sendWechatMessage($template->operator, $msg);
    }
}