<?php

namespace App\Entities\ProviderMerchantPayment;

class ProviderMerchantPrepaymentDTO
{
    private $prepaymentId;
    private $providerMerchantId;
    private $prepaymentType;
    private $amount;
    private $boundOrderIds;
    private $deductedOrderIds;
    private $remainingAmount;
    private $isReleased;
    private $settleInfoId;
    private $settleInfoJson;
    private $orderNos;
    private $remark;
    private $applyTime;
    private $applyCrmAccountAccountId;
    private $status;
    private $payTime;
    private $delAccount;
    private $adminId;
    private $companyId;
    private $invoiceStatus;
    private $ekuaibaoFlowCode;
    private $ekuaibaoFlowOperatorCrmAccountId;
    /**
     * @var ProviderMerchantDTO
     */
    private $providerMerchantDTO;
    private $invoicePaymentOrder;

    public function __construct($data)
    {
        $this->prepaymentId = $data['prepayment_id'] ?? 0;
        $this->providerMerchantId = $data['provider_merchant_id'] ?? 0;
        $this->prepaymentType = $data['prepayment_type'] ?? 0;
        $this->amount = $data['amount'] ?? "0.00";
        $this->boundOrderIds = $data['bound_order_ids'] ?? '';
        $this->deductedOrderIds = $data['deducted_order_ids'] ?? '';
        $this->remainingAmount = $data['remaining_amount'] ?? '0.00';
        $this->isReleased = $data['is_released'] ?? 0;
        $this->settleInfoId = $data['settle_info_id'] ?? 0;
        $this->settleInfoJson = $data['settle_info_json'] ?? '';
        $this->orderNos = $data['order_nos'] ?? '';
        $this->remark = $data['remark'] ?? '';
        $this->applyTime = $data['apply_time'] ?? '';
        $this->applyCrmAccountAccountId = $data['apply_crm_account_account_id'] ?? 0;
        $this->status = $data['status'] ?? 0;
        $this->payTime = $data['pay_time'] ?? '';
        $this->delAccount = $data['del_account'] ?? 0;
        $this->adminId = $data['admin_id'] ?? 0;
        $this->companyId = $data['company_id'] ?? '';
        $this->invoiceStatus = $data['invoice_status'] ?? 0;
        $this->ekuaibaoFlowCode = $data['ekuaibao_flow_code'] ?? '';
        $this->ekuaibaoFlowOperatorCrmAccountId = $data['ekuaibao_flow_operator_crm_account_id'] ?? 0;
        $this->providerMerchantDTO = $data['provider_merchant'] ?? new ProviderMerchantDTO([]);
        $this->invoicePaymentOrder = $data['invoice_payment_order'] ?? [];
    }

    /**
     * @return mixed
     */
    public function getPrepaymentId()
    {
        return $this->prepaymentId;
    }

    /**
     * @param mixed $prepaymentId
     */
    public function setPrepaymentId($prepaymentId): void
    {
        $this->prepaymentId = $prepaymentId;
    }

    /**
     * @return mixed
     */
    public function getProviderMerchantId()
    {
        return $this->providerMerchantId;
    }

    /**
     * @param mixed $providerMerchantId
     */
    public function setProviderMerchantId($providerMerchantId): void
    {
        $this->providerMerchantId = $providerMerchantId;
    }

    /**
     * @return mixed
     */
    public function getPrepaymentType()
    {
        return $this->prepaymentType;
    }

    /**
     * @param mixed $prepaymentType
     */
    public function setPrepaymentType($prepaymentType): void
    {
        $this->prepaymentType = $prepaymentType;
    }

    /**
     * @return mixed
     */
    public function getAmount()
    {
        return $this->amount;
    }

    /**
     * @param mixed $amount
     */
    public function setAmount($amount): void
    {
        $this->amount = $amount;
    }

    /**
     * @return mixed
     */
    public function getBoundOrderIds()
    {
        return $this->boundOrderIds;
    }

    /**
     * @param mixed $boundOrderIds
     */
    public function setBoundOrderIds($boundOrderIds): void
    {
        $this->boundOrderIds = $boundOrderIds;
    }

    /**
     * @return mixed
     */
    public function getDeductedOrderIds()
    {
        return $this->deductedOrderIds;
    }

    /**
     * @param mixed $deductedOrderIds
     */
    public function setDeductedOrderIds($deductedOrderIds): void
    {
        $this->deductedOrderIds = $deductedOrderIds;
    }

    /**
     * @return mixed
     */
    public function getRemainingAmount()
    {
        return $this->remainingAmount;
    }

    /**
     * @param mixed $remainingAmount
     */
    public function setRemainingAmount($remainingAmount): void
    {
        $this->remainingAmount = $remainingAmount;
    }

    /**
     * @return mixed
     */
    public function getIsReleased()
    {
        return $this->isReleased;
    }

    /**
     * @param mixed $isReleased
     */
    public function setIsReleased($isReleased): void
    {
        $this->isReleased = $isReleased;
    }

    /**
     * @return mixed
     */
    public function getSettleInfoId()
    {
        return $this->settleInfoId;
    }

    /**
     * @param mixed $settleInfoId
     */
    public function setSettleInfoId($settleInfoId): void
    {
        $this->settleInfoId = $settleInfoId;
    }

    /**
     * @return mixed
     */
    public function getSettleInfoJson()
    {
        return $this->settleInfoJson;
    }

    /**
     * @param mixed $settleInfoJson
     */
    public function setSettleInfoJson($settleInfoJson): void
    {
        $this->settleInfoJson = $settleInfoJson;
    }

    /**
     * @return mixed
     */
    public function getOrderNos()
    {
        return $this->orderNos;
    }

    /**
     * @param mixed $orderNos
     */
    public function setOrderNos($orderNos): void
    {
        $this->orderNos = $orderNos;
    }

    /**
     * @return mixed
     */
    public function getRemark()
    {
        return $this->remark;
    }

    /**
     * @param mixed $remark
     */
    public function setRemark($remark): void
    {
        $this->remark = $remark;
    }

    /**
     * @return mixed
     */
    public function getApplyTime()
    {
        return $this->applyTime;
    }

    /**
     * @param mixed $applyTime
     */
    public function setApplyTime($applyTime): void
    {
        $this->applyTime = $applyTime;
    }

    /**
     * @return mixed
     */
    public function getApplyCrmAccountAccountId()
    {
        return $this->applyCrmAccountAccountId;
    }

    /**
     * @param mixed $applyCrmAccountAccountId
     */
    public function setApplyCrmAccountAccountId($applyCrmAccountAccountId): void
    {
        $this->applyCrmAccountAccountId = $applyCrmAccountAccountId;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     */
    public function setStatus($status): void
    {
        $this->status = $status;
    }

    /**
     * @return mixed
     */
    public function getPayTime()
    {
        return $this->payTime;
    }

    /**
     * @param mixed $payTime
     */
    public function setPayTime($payTime): void
    {
        $this->payTime = $payTime;
    }

    /**
     * @return mixed
     */
    public function getDelAccount()
    {
        return $this->delAccount;
    }

    /**
     * @param mixed $delAccount
     */
    public function setDelAccount($delAccount): void
    {
        $this->delAccount = $delAccount;
    }

    /**
     * @return mixed
     */
    public function getAdminId()
    {
        return $this->adminId;
    }

    /**
     * @param mixed $adminId
     */
    public function setAdminId($adminId): void
    {
        $this->adminId = $adminId;
    }

    /**
     * @return mixed
     */
    public function getCompanyId()
    {
        return $this->companyId;
    }

    /**
     * @param mixed $companyId
     */
    public function setCompanyId($companyId): void
    {
        $this->companyId = $companyId;
    }

    /**
     * @return mixed|null
     */
    public function getInvoiceStatus()
    {
        return $this->invoiceStatus;
    }

    /**
     * @param mixed|null $invoiceStatus
     */
    public function setInvoiceStatus($invoiceStatus): void
    {
        $this->invoiceStatus = $invoiceStatus;
    }

    /**
     * @return mixed
     */
    public function getEkuaibaoFlowCode()
    {
        return $this->ekuaibaoFlowCode;
    }

    /**
     * @param mixed $ekuaibaoFlowCode
     */
    public function setEkuaibaoFlowCode($ekuaibaoFlowCode): void
    {
        $this->ekuaibaoFlowCode = $ekuaibaoFlowCode;
    }

    /**
     * @return int|mixed
     */
    public function getEkuaibaoFlowOperatorCrmAccountId()
    {
        return $this->ekuaibaoFlowOperatorCrmAccountId;
    }

    /**
     * @param int|mixed $ekuaibaoFlowOperatorCrmAccountId
     */
    public function setEkuaibaoFlowOperatorCrmAccountId($ekuaibaoFlowOperatorCrmAccountId): void
    {
        $this->ekuaibaoFlowOperatorCrmAccountId = $ekuaibaoFlowOperatorCrmAccountId;
    }

    public function getProviderMerchantDTO(): ProviderMerchantDTO
    {
        return $this->providerMerchantDTO;
    }

    public function setProviderMerchantDTO(ProviderMerchantDTO $providerMerchantDTO): void
    {
        $this->providerMerchantDTO = $providerMerchantDTO;
    }

    /**
     * @return array|mixed
     */
    public function getInvoicePaymentOrder()
    {
        return $this->invoicePaymentOrder;
    }

    /**
     * @param array|mixed $invoicePaymentOrder
     */
    public function setInvoicePaymentOrder($invoicePaymentOrder): void
    {
        $this->invoicePaymentOrder = $invoicePaymentOrder;
    }
}