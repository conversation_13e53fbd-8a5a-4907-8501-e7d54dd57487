<?php

namespace App\Entities\ProviderMerchantPayment;

class CheckUploadInvoiceFieldsEntity
{
    /**
     * @var string|null 供应商主体
     */
    private $providerMerchantName;
    /**
     * @var string|null 签约主体名称
     */
    private $companyName;
    /**
     * @var string|null
     */
    private $companyTaxNumber;

    public function getProviderMerchantName(): ?string
    {
        return $this->providerMerchantName;
    }

    public function setProviderMerchantName(?string $providerMerchantName): void
    {
        $this->providerMerchantName = $providerMerchantName;
    }

    public function getCompanyName(): ?string
    {
        return $this->companyName;
    }

    public function setCompanyName(?string $companyName): void
    {
        $this->companyName = $companyName;
    }

    public function getCompanyTaxNumber(): ?string
    {
        return $this->companyTaxNumber;
    }

    public function setCompanyTaxNumber(?string $companyTaxNumber): void
    {
        $this->companyTaxNumber = $companyTaxNumber;
    }
}