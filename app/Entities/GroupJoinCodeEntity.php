<?php

namespace App\Entities;

class GroupJoinCodeEntity
{
    /**
     * 图片的base64信息
     * @var string $img
     */
    private $img;

    /**
     * 团体名称
     * @var string $groupName
     */
    private $groupName;

    /**
     * @return mixed
     */
    public function getImg()
    {
        return $this->img;
    }

    /**
     * @param mixed $img
     * @return GroupJoinCodeEntity
     */
    public function setImg($img)
    {
        $this->img = $img;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getGroupname()
    {
        return $this->groupName;
    }

    /**
     * @param mixed $groupname
     * @return GroupJoinCodeEntity
     */
    public function setGroupname($groupname)
    {
        $this->groupName = $groupname;
        return $this;
    }
}
