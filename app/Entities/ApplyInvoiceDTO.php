<?php

namespace App\Entities;

use App\Http\Requests\Invoice\InvoiceApplyRequest;

class ApplyInvoiceDTO
{
    /**
     * @var string
     * 订单id
     */
    public $orderIds;

    /**
     * @var int
     * 用户id
     */
    public $accountId;

    /**
     * @var string
     * 转换后的开票金额
     */
    public $invoiceAmount;

    /**
     * @var string
     * 货币代码
     */
    public $currencyCode;

    /**
     * @var string
     * invoice上的备注
     */
    public $remarks;

    /**
     * @var string
     * 收件人邮箱
     */
    public $email;

    /**
     * @var string
     * 收件人地址
     */
    public $address;

    /**
     * @var string
     * 收件人姓名
     */
    public $consignee;

    /**
     * @var string
     * 收件人电话
     */
    public $mobile;

    /**
     * @var string
     * 高校/单位 作为抬头
     */
    public $company;

    /**
     * @var string
     * 负责人姓名
     */
    public $crmName;

    /**
     * @var string
     * 负责人电话
     */
    public $crmMobile;

    /**
     * @var string
     * 负责人邮箱
     */
    public $crmEmail;

    /**
     * @var string
     * 申请备注
     */
    public $applyNote;

    /**
     * @var string
     * 一般写死测试非
     */
    public $vals;

    /**
     * @var string
     * 汇率
     */
    public $exchangeRate;

    /**
     * @var string
     * 具体的发票项目
     */
    public $itemJson;

    /**
     * @var int
     * 申请人crm account id
     */
    public $crmAccountId;

    /**
     * @var int
     * 申请来源
     */
    public $applyFrom;

    /**
     * @var string
     * 开票金额
     */
    public $totalMoney;
    public $providerId;

    public function initFromInvoiceApplyRequest(InvoiceApplyRequest $invoiceApplyRequest)
    {
        $this->orderIds = $invoiceApplyRequest->orderids;
        $this->accountId = $invoiceApplyRequest->accountid;
        $this->invoiceAmount = $invoiceApplyRequest->invoiceamount;
        $this->currencyCode = $invoiceApplyRequest->currencycode;
        $this->remarks = empty($invoiceApplyRequest->remarks) ? "" : $invoiceApplyRequest->remarks;
        $this->email = empty($invoiceApplyRequest->email) ? "" : $invoiceApplyRequest->email;
        $this->address = $invoiceApplyRequest->address;
        $this->consignee = $invoiceApplyRequest->consignee;
        $this->mobile = $invoiceApplyRequest->mobile;
        $this->company = $invoiceApplyRequest->company;
        $this->crmName = $invoiceApplyRequest->crmname;
        $this->crmMobile = $invoiceApplyRequest->crmmobile;
        $this->crmEmail = $invoiceApplyRequest->crmemail;
        $this->applyNote = empty($invoiceApplyRequest->applynote) ? "" : $invoiceApplyRequest->applynote;
        $this->vals = $invoiceApplyRequest->vals;
        $this->exchangeRate = $invoiceApplyRequest->exchangerate;
        $this->itemJson = $invoiceApplyRequest->itemjson;
        $this->crmAccountId = $invoiceApplyRequest->crmAccountId;
        $this->applyFrom = $invoiceApplyRequest->applyFrom;
        $this->totalMoney = $invoiceApplyRequest->totalmoney;
    }

    public function __construct()
    {

    }

    public function toArray()
    {
        return [
            'orderIds' => $this->orderIds,
            'accountId' => $this->accountId,
            'invoiceAmount' => $this->invoiceAmount,
            'currencyCode' => $this->currencyCode,
            'remarks' => $this->remarks,
            'email' => $this->email,
            'address' => $this->address,
            'consignee' => $this->consignee,
            'mobile' => $this->mobile,
            'company' => $this->company,
            'crmName' => $this->crmName,
            'crmMobile' => $this->crmMobile,
            'crmEmail' => $this->crmEmail,
            'applyNote' => $this->applyNote,
            'vals' => $this->vals,
            'exchangeRate' => $this->exchangeRate,
            'itemJson' => $this->itemJson,
            'crmAccountId' => $this->crmAccountId,
            'applyFrom' => $this->applyFrom,
            'totalMoney' => $this->totalMoney,
            'providerId' => $this->providerId,
        ];
    }

    public function setProviderId($providerId)
    {
        $this->providerId = $providerId;
    }

}