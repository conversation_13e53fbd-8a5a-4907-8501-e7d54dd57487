<?php

namespace App\Entities;

class AccountEntity
{
    private $accountid = 0;
    private $accountno = 0;
    private $qrcode = "";

    /**
     * @return int
     */
    public function getAccountid(): int
    {
        return $this->accountid;
    }

    /**
     * @param int $accountid
     * @return AccountEntity
     */
    public function setAccountid(int $accountid): AccountEntity
    {
        $this->accountid = $accountid;
        return $this;
    }

    /**
     * @return int
     */
    public function getAccountno(): int
    {
        return $this->accountno;
    }

    /**
     * @param int $accountno
     * @return AccountEntity
     */
    public function setAccountno(int $accountno): AccountEntity
    {
        $this->accountno = $accountno;
        return $this;
    }

    public function getQrcode()
    {
        return $this->qrcode;
    }

    public function setQrcode($qrcode): AccountEntity
    {
        $this->qrcode = $qrcode;
        return $this;
    }


}
