<?php

namespace App\Entities\Todo;

use App\Entities\EntityAccess;
use App\Models\WechatMessage;


/**
 * Class TodoDealDTO
 *
 * Data Transfer Object for handling To-Do tasks.
 * This class uses the EntityAccess trait to automatically generate getter and setter methods.
 *
 * @method int getMessageId()
 * @method void setMessageId(int $messageId)
 * @method int getOperatorId()
 * @method void setOperatorId(int $operatorId)
 * @method int getOperateType()
 * @method void setOperateType(int $operateType)
 * @method string getContent()
 * @method void setContent(string $content)
 * @method WechatMessage getMessage()
 * @method void setMessage(WechatMessage $message)
 * @method int getOtherId()
 * @method void setOtherId(int $otherId)
 */
class TodoDealDTO
{

    use EntityAccess;


    /**
     * @var int
     */
    private $messageId;

    /**
     * @var int
     */
    private $operatorId;

    /**
     * @var int
     */
    private $operateType;

    /**
     * @var string
     */
    private $content;

    /**
     * @var WechatMessage
     */
    private $message;

    /**
     * @var int $otherId
     */
    private $otherId;
}
