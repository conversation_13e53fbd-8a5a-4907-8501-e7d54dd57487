<?php

namespace App\Entities\Express;

class OrderUserExpressDTO
{
    /**
     * @var int $type
     */
    private $type;
    /**
     * @var int $oid
     */
    private $oid;
    /**
     * @var string $osn
     */
    private $osn;
    /**
     * @var string $expressNumber
     */
    private $expressNumber;
    /**
     * @var string $expressCompany
     */
    private $expressCompany;
    /**
     * @var string $sampleImage
     */
    private $sampleImage;
    /**
     * @var string $sampleForm
     */
    private $sampleForm;
    /**
     * @var string $sampleChineseName
     */
    private $sampleChineseName;
    /**
     * @var string $sampleClassification
     */
    private $sampleClassification;
    /**
     * @var string $sampleQuantity
     */
    private $sampleQuantity;
    /**
     * @var string $sampleWeight
     */
    private $sampleWeight;
    /**
     * @var string $sampleValue
     */
    private $sampleValue;
    /**
     * @var string $sampleDescription
     */
    private $sampleDescription;
    /**
     * @var string $source
     */
    private $source;
    /**
     * @var int $userExpressId
     */
    private $userExpressId;
    /**
     * @var int $operateUserAccount
     */
    private $operateUserAccount;
    /**
     * @var int $operateCrmAccount
     */
    private $operateCrmAccount;

    public function getType(): int
    {
        return $this->type;
    }

    public function setType(int $type): void
    {
        $this->type = $type;
    }

    public function getOid(): int
    {
        return $this->oid;
    }

    public function setOid(int $oid): void
    {
        $this->oid = $oid;
    }

    public function getOsn(): string
    {
        return $this->osn;
    }

    public function setOsn(string $osn): void
    {
        $this->osn = $osn;
    }

    public function getExpressNumber(): string
    {
        return $this->expressNumber;
    }

    public function setExpressNumber(string $expressNumber): void
    {
        $this->expressNumber = $expressNumber;
    }

    public function getExpressCompany(): string
    {
        return $this->expressCompany;
    }

    public function setExpressCompany(string $expressCompany): void
    {
        $this->expressCompany = $expressCompany;
    }

    public function getSampleImage(): string
    {
        return $this->sampleImage;
    }

    public function setSampleImage(string $sampleImage): void
    {
        $this->sampleImage = $sampleImage;
    }

    public function getSampleForm(): string
    {
        return $this->sampleForm;
    }

    public function setSampleForm(string $sampleForm): void
    {
        $this->sampleForm = $sampleForm;
    }

    public function getSampleChineseName(): string
    {
        return $this->sampleChineseName;
    }

    public function setSampleChineseName(string $sampleChineseName): void
    {
        $this->sampleChineseName = $sampleChineseName;
    }

    public function getSampleClassification(): string
    {
        return $this->sampleClassification;
    }

    public function setSampleClassification(string $sampleClassification): void
    {
        $this->sampleClassification = $sampleClassification;
    }

    public function getSampleQuantity(): string
    {
        return $this->sampleQuantity;
    }

    public function setSampleQuantity(string $sampleQuantity): void
    {
        $this->sampleQuantity = $sampleQuantity;
    }

    public function getSampleWeight(): string
    {
        return $this->sampleWeight;
    }

    public function setSampleWeight(string $sampleWeight): void
    {
        $this->sampleWeight = $sampleWeight;
    }

    public function getSampleValue(): string
    {
        return $this->sampleValue;
    }

    public function setSampleValue(string $sampleValue): void
    {
        $this->sampleValue = $sampleValue;
    }

    public function getSampleDescription(): string
    {
        return $this->sampleDescription;
    }

    public function setSampleDescription(string $sampleDescription): void
    {
        $this->sampleDescription = $sampleDescription;
    }

    public function getSource(): string
    {
        return $this->source;
    }

    public function setSource(string $source): void
    {
        $this->source = $source;
    }

    public function getUserExpressId(): int
    {
        return $this->userExpressId;
    }

    public function setUserExpressId(int $userExpressId): void
    {
        $this->userExpressId = $userExpressId;
    }

    public function getOperateUserAccount(): int
    {
        return $this->operateUserAccount;
    }

    public function setOperateUserAccount(int $operateUserAccount): void
    {
        $this->operateUserAccount = $operateUserAccount;
    }

    public function getOperateCrmAccount(): int
    {
        return $this->operateCrmAccount;
    }

    public function setOperateCrmAccount(int $operateCrmAccount): void
    {
        $this->operateCrmAccount = $operateCrmAccount;
    }

    /**
     * @return array 快递信息
     */
    public function toArray()
    {
        return [
            'type' => $this->getType(),
            'oid' => $this->getOid(),
            'osn' => $this->getOsn(),
            'express_number' => $this->getExpressNumber(),
            'express_company' => $this->getExpressCompany(),
            'sample_image' => $this->getSampleImage(),
            'sample_form' => $this->getSampleForm(),
            'sample_chinese_name' => $this->getSampleChineseName(),
            'sample_classification' => $this->getSampleClassification(),
            'sample_quantity' => $this->getSampleQuantity(),
            'sample_weight' => $this->getSampleWeight(),
            'sample_value' => $this->getSampleValue(),
            'sample_description' => $this->getSampleDescription(),
            'source' => $this->getSource(),
        ];
    }

    /**
     * @return array 操作日志
     */
    public function toHistoryArray()
    {
        return [
            'user_express_id' => $this->getUserExpressId(),
            'express_number' => $this->getExpressNumber(),
            'express_company' => $this->getExpressCompany(),
            'sample_image' => $this->getSampleImage(),
            'sample_form' => $this->getSampleForm(),
            'sample_chinese_name' => $this->getSampleChineseName(),
            'sample_classification' => $this->getSampleClassification(),
            'sample_quantity' => $this->getSampleQuantity(),
            'sample_weight' => $this->getSampleWeight(),
            'sample_value' => $this->getSampleValue(),
            'sample_description' => $this->getSampleDescription(),
            'source' => $this->getSource(),
            'operate_user_account' => $this->getOperateUserAccount(),
            'operate_crm_account' => $this->getOperateCrmAccount()
        ];
    }
}