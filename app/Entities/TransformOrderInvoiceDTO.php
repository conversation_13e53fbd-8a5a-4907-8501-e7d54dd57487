<?php

namespace App\Entities;

use App\Http\Requests\Invoice\TransformOrderInvoiceRequest;
use Illuminate\Support\Collection;

class TransformOrderInvoiceDTO
{
    /**
     * @var Collection|list<array{finance_ticket_applyid:int,cticketid:int}>
     *
     */
    public $applyInputValue;

    /**
     * @var int
     */
    public $adminId;

    /**
     * @var int
     */
    public $reasonId;

    /**
     * @var string
     */
    public $reasonAppend;

    /**
     * @var string
     */
    public $reasonDescription;

    public $adminName;

    public function initFromTransFormOrderInvoiceRequest(TransformOrderInvoiceRequest $request)
    {
        $this->applyInputValue = collect($request->apply_input_value);
        $this->adminId = $request->admin_id;
        $this->reasonId = $request->reason_id;
        $this->reasonAppend = $request->reason_append;
    }
}