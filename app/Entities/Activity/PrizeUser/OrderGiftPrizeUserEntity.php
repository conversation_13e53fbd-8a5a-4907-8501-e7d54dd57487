<?php

namespace App\Entities\Activity\PrizeUser;

use Yanqu\YanquPhplib\Openapi\Activity\Constants\ActivityPrize\ActivityTypeConstant;
use Yanqu\YanquPhplib\Openapi\Activity\Constants\OrderGift\PrizeTypeConstant;

class OrderGiftPrizeUserEntity extends BasePrizeUserEntity
{
    public function getAccountId(): int
    {
        return $this->prizeUser->accountid;
    }

    public function getGroupId(): int
    {
        return $this->prizeUser->groupid;
    }

    public function getRegionId(): int
    {
        return $this->prizeUser->region_id ?? 0;
    }

    public function getPrizeType(): int
    {
        return PrizeTypeConstant::ACTIVITY_PRIZE_TYPE_TRANSFER[$this->prizeUser->prize->type];
    }

    public function getPrizeName(): string
    {
        return $this->prizeUser->prize->name ?? '';
    }

    public function getPrizeDetailId(): int
    {
        return 0;
    }

    public function getActivityType(): int
    {
        return ActivityTypeConstant::ORDER_GIFT;
    }

    public function getActivityId(): int
    {
        return $this->prizeUser->activity_id;
    }

    public function getActivityName(): string
    {
        return $this->prizeUser->activity->name ?? '';
    }

    public function getActivityUserId(): int
    {
        return $this->prizeUser->id;
    }

    public function getDeliveryStatus(): int
    {
        return $this->prizeUser->delivery_status;
    }

    public function getMemo(): string
    {
        return $this->prizeUser->memo?? '';
    }

    public function getContactName(): string
    {
        return $this->prizeUser->contact_name ?? '';
    }

    public function getPhone(): string
    {
        return $this->prizeUser->phone ?? '';
    }

    public function getProvinceId(): int
    {
        return $this->prizeUser->province_id ?? 0;
    }

    public function getCityId(): int
    {
        return $this->prizeUser->city_id ?? 0;
    }

    public function getAreaId(): int
    {
        return $this->prizeUser->area_id ?? 0;
    }

    public function getAddress(): string
    {
        return $this->prizeUser->address ?? '';
    }

    public function getFullAddress(): string
    {
        return $this->prizeUser->province->name . $this->prizeUser->city->name
         . $this->prizeUser->area->name . $this->prizeUser->address;
    }

    public function getCouponId(): int
    {
        return 0;
    }

    public function getPoint(): int
    {
        return 0;
    }

    public function getPrizeUser()
    {
        return $this->prizeUser;
    }
}