<?php

namespace App\Entities\Activity\PrizeUser;

use Yanqu\YanquPhplib\Openapi\Activity\Constants\ActivityPrize\ActivityTypeConstant;

use Yanqu\YanquPhplib\Openapi\Activity\Constants\ActivityPrize\PrizeTypeConstant;

class ActivityPrizeUserEntity extends BasePrizeUserEntity
{
    public function getAccountId(): int
    {
        return $this->prizeUser->account_id ?? 0;
    }

    public function getGroupId(): int
    {
        return $this->prizeUser->group_id ?? 0;
    }

    public function getRegionId(): int
    {
        return $this->prizeUser->region_id ?? 0;
    }

    public function getPrizeType(): int
    {
        return $this->prizeUser->prize_type;
    }

    public function getPrizeName(): string
    {
        return $this->prizeUser->prize_name ?? '';
    }

    public function getPrizeDetailId(): int
    {
        return $this->prizeUser->prize_detail_id ?? 0;
    }

    public function getActivityType(): int
    {
        return $this->prizeUser->activity_type;
    }

    public function getActivityId(): int
    {
        return $this->prizeUser->activity_id;
    }

    public function getActivityName(): string
    {
        if ($this->prizeUser->activity_type == ActivityTypeConstant::ORDER_GIFT) {
            return $this->prizeUser->orderGiftActivity->name ?? '';
        }
        if ($this->prizeUser->activity_type == ActivityTypeConstant::LOTTERY) {
            return $this->prizeUser->lotteryActivity->title ?? '';
        }
        return '';
    }

    public function getActivityUserId(): int
    {
        return $this->prizeUser->activity_user_id ?? 0;
    }

    public function getDeliveryStatus(): int
    {
        return $this->prizeUser->delivery_status;
    }

    public function getMemo(): string
    {
        return $this->prizeUser->memo ?? '';
    }

    public function getContactName(): string
    {
        return $this->prizeUser->goods->contact_name ?? '';
    }

    public function getPhone(): string
    {
        if ($this->prizeUser->prize_type == PrizeTypeConstant::GOODS) {
            return $this->prizeUser->goods->phone ?? '';
        }
        if ($this->prizeUser->prize_type == PrizeTypeConstant::CARD) {
            return $this->prizeUser->card->phone ?? '';
        }
        return '';
    }

    public function getProvinceId(): int
    {
        return $this->prizeUser->goods->province_id ?? 0;
    }

    public function getCityId(): int
    {
        return $this->prizeUser->goods->city_id ?? 0;
    }

    public function getAreaId(): int
    {
        return $this->prizeUser->goods->area_id ?? 0;
    }

    public function getAddress(): string
    {
        return $this->prizeUser->goods->address ?? '';
    }

    public function getFullAddress(): string
    {
        return $this->prizeUser->goods->full_address ?? '';
    }

    public function getCouponId(): int
    {
        if ($this->prizeUser->activity_type == ActivityTypeConstant::LOTTERY) {
            return $this->prizeUser->lotteryPrizeUser->prize->coupon_id ?? 0;
        }
        return 0;
    }

    public function getPoint(): int
    {
        if ($this->prizeUser->activity_type == ActivityTypeConstant::LOTTERY) {
            return $this->prizeUser->lotteryPrizeUser->prize->amount ?? 0;
        }
        return 0;
    }

    public function getPrizeUser()
    {
        return $this->prizeUser;
    }
}