<?php

namespace App\Entities\Ekuaibao\Callback;

use App\Entities\Ekuaibao\MoneyDTO;

class FlowPaidCallbackDTO
{
    private $flowId;
    private $nodeId;
    private $state;
    /**
     * @var MoneyDTO
     */
    private $payMoney;
    /**
     * @var MoneyDTO
     */
    private $writtenOffMoney;
    /**
     * @var FormSpecificationDTO
     */
    private $formSpecification;
    public function __construct($data)
    {
        $this->flowId = $data['flowId'] ?? '';
        $this->nodeId = $data['nodeId'] ?? '';
        $this->state = $data['state'] ?? '';
        $this->payMoney = new MoneyDTO();
        $this->payMoney->setStandardStrCode($data['payMoney']['currency'] ?? '');
        $this->payMoney->setStandard($data['payMoney']['amount'] ?? '');
        $this->writtenOffMoney = new MoneyDTO();
        $this->writtenOffMoney->setStandardStrCode($data['writtenOffMoney']['currency'] ?? '');
        $this->writtenOffMoney->setStandard($data['writtenOffMoney']['amount'] ?? '');
        $this->formSpecification = new FormSpecificationDTO();
        $this->formSpecification->setSpecificationId($data['formSpecification']['specificationId'] ?? '');
    }

    public function toArray(): array
    {
        return [
            'flowId' => $this->flowId,
            'nodeId' => $this->nodeId,
            'state' => $this->state,
            'payMoney' => $this->payMoney->toArray(),
            'writtenOffMoney' => $this->writtenOffMoney->toArray(),
            'formSpecification' => $this->formSpecification->toArray(),
        ];
    }

    /**
     * @return mixed|string
     */
    public function getFlowId()
    {
        return $this->flowId;
    }

    /**
     * @param mixed|string $flowId
     */
    public function setFlowId($flowId): void
    {
        $this->flowId = $flowId;
    }

    /**
     * @return mixed|string
     */
    public function getNodeId()
    {
        return $this->nodeId;
    }

    /**
     * @param mixed|string $nodeId
     */
    public function setNodeId($nodeId): void
    {
        $this->nodeId = $nodeId;
    }

    /**
     * @return mixed|string
     */
    public function getState()
    {
        return $this->state;
    }

    /**
     * @param mixed|string $state
     */
    public function setState($state): void
    {
        $this->state = $state;
    }

    public function getPayMoney(): MoneyDTO
    {
        return $this->payMoney;
    }

    public function setPayMoney(MoneyDTO $payMoney): void
    {
        $this->payMoney = $payMoney;
    }

    public function getWrittenOffMoney(): MoneyDTO
    {
        return $this->writtenOffMoney;
    }

    public function setWrittenOffMoney(MoneyDTO $writtenOffMoney): void
    {
        $this->writtenOffMoney = $writtenOffMoney;
    }

    public function getFormSpecification(): FormSpecificationDTO
    {
        return $this->formSpecification;
    }

    public function setFormSpecification(FormSpecificationDTO $formSpecification): void
    {
        $this->formSpecification = $formSpecification;
    }
}