<?php

namespace App\Entities\Ekuaibao\FeeTypeForm;

use App\Entities\Ekuaibao\Attachment\AttachmentDTO;
use App\Entities\Ekuaibao\InvoiceForm\InvoiceFormDTO;
use App\Entities\Ekuaibao\MoneyDTO;

class FeeTypeFormDTO
{
    /**
     * @var MoneyDTO 费用金额
     */
    private $amount;
    /**
     * @var int 消费日期
     */
    private $feeDate;
    /**
     * @var string 明细ID
     */
    private $detailId;

    /**
     * @var MoneyDTO u_金额
     */
    private $backupAmount;
    /**
     * @var AttachmentDTO[] 附件
     */
    private $attachments;
    /**
     * @var InvoiceFormDTO 发票信息
     */
    private $invoiceForm;
    /**
     * @var MoneyDTO 发票金额
     */
    private $invoiceAmount;
    /**
     * @var string 消费原因
     */
    private $consumptionReasons;

    /**
     *转换成创建单据的数组
     * @return array
     */
    public function toCreateFlowArray()
    {
        return [
            'amount' => $this->amount ? $this->amount->toArray() : [],
            'feeDate' => $this->feeDate,
            'u_金额' =>  $this->backupAmount ? $this->backupAmount->toArray() : [],
            'invoiceForm' => $this->invoiceForm ? $this->invoiceForm->toArray() : [],
        ];
    }

    public function getAmount(): MoneyDTO
    {
        return $this->amount;
    }

    public function setAmount(MoneyDTO $amount): void
    {
        $this->amount = $amount;
    }

    public function getFeeDate(): int
    {
        return $this->feeDate;
    }

    public function setFeeDate(int $feeDate): void
    {
        $this->feeDate = $feeDate;
    }

    public function getDetailId(): string
    {
        return $this->detailId;
    }

    public function setDetailId(string $detailId): void
    {
        $this->detailId = $detailId;
    }

    public function getBackupAmount(): MoneyDTO
    {
        return $this->backupAmount;
    }

    public function setBackupAmount(MoneyDTO $backupAmount): void
    {
        $this->backupAmount = $backupAmount;
    }

    /**
     * @return AttachmentDTO[]
     */
    public function getAttachments(): array
    {
        return $this->attachments;
    }

    /**
     * @param mixed $attachments
     */
    public function setAttachments($attachments): void
    {
        $this->attachments = $attachments;
    }

    public function getInvoiceForm(): InvoiceFormDTO
    {
        return $this->invoiceForm;
    }

    public function setInvoiceForm(InvoiceFormDTO $invoiceForm): void
    {
        $this->invoiceForm = $invoiceForm;
    }

    public function getInvoiceAmount(): MoneyDTO
    {
        return $this->invoiceAmount;
    }

    public function setInvoiceAmount(MoneyDTO $invoiceAmount): void
    {
        $this->invoiceAmount = $invoiceAmount;
    }

    public function getConsumptionReasons(): string
    {
        return $this->consumptionReasons;
    }

    public function setConsumptionReasons(string $consumptionReasons): void
    {
        $this->consumptionReasons = $consumptionReasons;
    }
}