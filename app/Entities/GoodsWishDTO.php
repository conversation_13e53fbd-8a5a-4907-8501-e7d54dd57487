<?php

namespace App\Entities;

class GoodsWishDTO
{
    private $accountId;

    private $ip;

    private $wish;

    private $source;

    /**
     * @return mixed
     */
    public function getAccountId():int
    {
        return $this->accountId;
    }

    /**
     * @param mixed $accountId
     * @return GoodsWishDTO
     */
    public function setAccountId(int $accountId): GoodsWishDTO
    {
        $this->accountId = $accountId;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getIp():string
    {
        return $this->ip;
    }

    /**
     * @param mixed $ip
     * @return GoodsWishDTO
     */
    public function setIp(string $ip): GoodsWishDTO
    {
        $this->ip = $ip;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getWish():string
    {
        return $this->wish;
    }

    /**
     * @param mixed $wish
     * @return GoodsWishDTO
     */
    public function setWish(string $wish): GoodsWishDTO
    {
        $this->wish = $wish;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getSource():int
    {
        return $this->source;
    }

    /**
     * @param mixed $source
     * @return GoodsWishDTO
     */
    public function setSource(int $source): GoodsWishDTO
    {
        $this->source = $source;
        return $this;
    }


}
