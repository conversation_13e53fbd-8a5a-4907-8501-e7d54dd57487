<?php

namespace App\Entities\AfterSale;

use App\Entities\EntityAccess;

/**
 * @method int getOid()
 * @method TicketPmDTO setOid(int $oid)
 * @method int getTypeId()
 * @method TicketPmDTO setTypeId(int $typeId)
 * @method int getTicketType()
 * @method TicketPmDTO setTicketType(int $ticketType)
 * @method int getPickUp()
 * @method TicketPmDTO setPickUp(int $pickUp)
 * @method int getSource()
 * @method TicketPmDTO setSource(int $source)
 * @method int getCreator()
 * @method TicketPmDTO setCreator(int $creator)
 * @method string getCc()
 * @method TicketPmDTO setCc(string $cc)
 * @method int getIsVisibleToCustomer()
 * @method TicketPmDTO setIsVisibleToCustomer(int $isVisibleToCustomer)
 * @method string getFeedback()
 * @method TicketPmDTO setFeedback(string $feedback)
 * @method int getHandler()
 * @method TicketPmDTO setHandler(int $handler)
 * @method int getLevel()
 * @method TicketPmDTO setLevel(int $level)
 *
 * 工单参数
 */
class TicketPmDTO {
    use EntityAccess;

    private $oid;

    private $typeId;

    /**
     * @var int 工单类型 0异议单 1投诉单 2差评单
     */
    private $ticketType;

    /**
     * @var int 跟进人
     */
    private $pickUp;

    /**
     * @var int 来源 0客服反馈 1用户反馈 2 跟进发起 3邮件投诉 4满意度回访 5 400投诉 6免单 7NPS/CES
     */
    private $source;

    /**
     * @var int 创建人
     */
    private $creator;

    /**
     * @var string 抄送人
     */
    private $cc;

    /**
     * @var int 顾客是否可见
     */
    private $isVisibleToCustomer;

    /**
     * @var string 建议反馈
     */
    private $feedback;

    /**
     * @var int 处理人
     */
    private $handler;

    /**
     * @var int 紧急程度
     */
    private $level = null;
}