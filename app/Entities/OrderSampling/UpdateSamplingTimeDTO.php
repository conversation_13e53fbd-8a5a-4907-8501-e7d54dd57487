<?php

namespace App\Entities\OrderSampling;

use App\Entities\EntityAccess;

/**
 * @method UpdateSamplingTimeDTO setOid(int $oid)
 * @method UpdateSamplingTimeDTO setAccountId(int $accountId)
 * @method UpdateSamplingTimeDTO setDeliverDay(string $deliverDay)
 * @method UpdateSamplingTimeDTO setDeliverId(int $deliverId)
 * @method UpdateSamplingTimeDTO setRemindDay(string $notifyTime)
 * @method UpdateSamplingTimeDTO setOperateFrom(string $operateFrom)
 * @method UpdateSamplingTimeDTO setOperatorId(int $operatorId)
 * @method UpdateSamplingTimeDTO setSamplingId(int $samplingId)
 * @method int getOid()
 * @method int getAccountId()
 * @method string getDeliverDay()
 * @method int getDeliverId()
 * @method string getRemindDay()
 * @method string getOperateFrom()
 * @method int getOperatorId()
 * @method int getSamplingId()
 */
class UpdateSamplingTimeDTO {
    use EntityAccess;

    private $oid;

    private $accountId;

    private $deliverDay;

    private $deliverId;

    private $remindDay;

    private $operateFrom;

    private $operatorId;

    private $samplingId;
}