<?php

namespace App\Entities;

class FinanceBackTicketDTO
{
    /**
     * @var int
     * 发票id
     */
    public $applyId;

    /**
     * @var string
     * 退票备注
     */
    public $remark;

    /**
     * @var int
     * 退票人id
     */
    public $accountId;

    /**
     * @var int
     * 操作人adminId
     */
    public $adminId;

    /**
     * @var int
     * 退票原因
     */
    public $backReason;

    public function __construct($applyId, $remark, $accountId, $adminId, $backReason)
    {
        $this->applyId = $applyId;
        $this->remark = $remark;
        $this->accountId = $accountId;
        $this->adminId = $adminId;
        $this->backReason = $backReason;
    }
}