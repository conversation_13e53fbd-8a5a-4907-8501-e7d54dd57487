<?php

namespace App\Entities\NuoNuo;

class NuoNuoRequestDTO
{
    /**
     * 请求函数
     */
    protected $method;

    /**
     * 请求参数
     */
    protected $body;

    /**
     * 卖方税号
     */
    protected $salerTaxNum;

    /**
     * 诺诺请求token
     */
    protected $token;

    /**
     * 请求票类别（订单/预存）
     */
    protected $applyType;

    /**
     * 请求对应系统表id
     */
    protected $applyId;

    /**
     * @return mixed
     */
    public function getMethod()
    {
        return $this->method;
    }

    /**
     * @param  mixed  $method
     */
    public function setMethod($method): void
    {
        $this->method = $method;
    }

    /**
     * @return mixed
     */
    public function getBody()
    {
        return $this->body;
    }

    /**
     * @param  mixed  $body
     */
    public function setBody($body): void
    {
        $this->body = $body;
    }

    /**
     * @return mixed
     */
    public function getSalerTaxNum()
    {
        return $this->salerTaxNum;
    }

    /**
     * @param  mixed  $salerTaxNum
     */
    public function setSalerTaxNum($salerTaxNum): void
    {
        $this->salerTaxNum = $salerTaxNum;
    }

    /**
     * @return mixed
     */
    public function getToken()
    {
        return $this->token;
    }

    /**
     * @param  mixed  $token
     */
    public function setToken($token): void
    {
        $this->token = $token;
    }

    /**
     * @return mixed
     */
    public function getApplyType()
    {
        return $this->applyType;
    }

    /**
     * @param  mixed  $applyType
     */
    public function setApplyType($applyType): void
    {
        $this->applyType = $applyType;
    }

    /**
     * @return mixed
     */
    public function getApplyId()
    {
        return $this->applyId;
    }

    /**
     * @param  mixed  $applyId
     */
    public function setApplyId($applyId): void
    {
        $this->applyId = $applyId;
    }

    public function toString()
    {
        return json_encode([
           'method' => $this->getMethod(),
           'body' => $this->getBody(),
           'token' => $this->getToken(),
        ]);
    }
}