<?php

namespace App\Entities\NuoNuo;

class OpenReceiptNuoNuoDetailDTO
{
    /**
     * @var string $favouredPolicyFlag 优惠政策标识: 0:不使用; 1:使用;
     */
    public $favouredPolicyFlag;

    /**
     * @var string $favouredPolicyName 优惠政策名称（当favouredPolicyFlag为1时，此项必填）
     */
    public $favouredPolicyName;

    /**
     * @var string $goodsCode 商品编码
     */
    public $goodsCode;

    /**
     * @var string $goodsName 商品名称
     */
    public $goodsName;

    /**
     * @var int $num 数量
     */
    public $num;

    /**
     * @var int $price 单价
     */
    public $price;

    /**
     * @var string $specType 规格型号
     */
    public $specType;

    /**
     * @var int $taxIncludedAmount 含税金额（精确到小数点后两位）
     */
    public $taxIncludedAmount;

    /**
     * @var float $taxRate 税率（只支持0.xxx，需要生成二维码时必填）
     */
    public $taxRate;

    /**
     * @var string $unit 单位（成品油时，必须有且只能是升或者吨）
     */
    public $unit;

    /**
     * @var string $zeroRateFlag 零税率标识: 空:非零税率; 1:免税; 2:不征税; 3:普通零税率
     */
    public $zeroRateFlag;


    public function toMap(): array
    {
        //将对象转换为数组
        $map = [];
        foreach ($this as $key => $value) {
            if ($value !== null) {
                $map[$key] = $value;
            }
        }
        return $map;
    }
}