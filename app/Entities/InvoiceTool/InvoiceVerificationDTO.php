<?php

namespace App\Entities\InvoiceTool;

class InvoiceVerificationDTO
{

    private $invoiceType;
    private $invoiceNumber;
    private $invoiceCode;
    private $invoiceTime;
    private $taxInclusiveAmount;
    private $taxExclusiveAmount;
    private $invoiceValidationCode;
    private $verificationResult;
    private $verificationPassed;
    private $verificationResultMessage;

    public function __construct($data)
    {
        $this->invoiceType = $data['invoice_type'] ?? '';
        $this->invoiceNumber = $data['invoice_number'] ?? '';
        $this->invoiceCode = $data['invoice_code'] ?? '';
        $this->invoiceTime = $data['invoice_time'] ?? '';
        $this->taxInclusiveAmount = $data['tax_inclusive_amount'] ?? '';
        $this->taxExclusiveAmount = $data['tax_exclusive_amount'] ?? '';
        $this->invoiceValidationCode = $data['invoice_validation_code'] ?? '';
        $this->verificationResult = $data['verification_result'] ?? '';
        $this->verificationPassed = $data['verification_passed'] ?? '';
        $this->verificationResultMessage = $data['verification_result_message'] ?? '';
    }

    /**
     * @return mixed
     */
    public function getInvoiceType()
    {
        return $this->invoiceType;
    }

    /**
     * @param mixed $invoiceType
     */
    public function setInvoiceType($invoiceType): void
    {
        $this->invoiceType = $invoiceType;
    }

    /**
     * @return mixed
     */
    public function getInvoiceNumber()
    {
        return $this->invoiceNumber;
    }

    /**
     * @param mixed $invoiceNumber
     */
    public function setInvoiceNumber($invoiceNumber): void
    {
        $this->invoiceNumber = $invoiceNumber;
    }

    /**
     * @return mixed
     */
    public function getInvoiceCode()
    {
        return $this->invoiceCode;
    }

    /**
     * @param mixed $invoiceCode
     */
    public function setInvoiceCode($invoiceCode): void
    {
        $this->invoiceCode = $invoiceCode;
    }

    /**
     * @return mixed
     */
    public function getInvoiceTime()
    {
        return $this->invoiceTime;
    }

    /**
     * @param mixed $invoiceTime
     */
    public function setInvoiceTime($invoiceTime): void
    {
        $this->invoiceTime = $invoiceTime;
    }

    /**
     * @return mixed
     */
    public function getTaxInclusiveAmount()
    {
        return $this->taxInclusiveAmount;
    }

    /**
     * @param mixed $taxInclusiveAmount
     */
    public function setTaxInclusiveAmount($taxInclusiveAmount): void
    {
        $this->taxInclusiveAmount = $taxInclusiveAmount;
    }

    /**
     * @return mixed
     */
    public function getTaxExclusiveAmount()
    {
        return $this->taxExclusiveAmount;
    }

    /**
     * @param mixed $taxExclusiveAmount
     */
    public function setTaxExclusiveAmount($taxExclusiveAmount): void
    {
        $this->taxExclusiveAmount = $taxExclusiveAmount;
    }

    /**
     * @return mixed
     */
    public function getInvoiceValidationCode()
    {
        return $this->invoiceValidationCode;
    }

    /**
     * @param mixed $invoiceValidationCode
     */
    public function setInvoiceValidationCode($invoiceValidationCode): void
    {
        $this->invoiceValidationCode = $invoiceValidationCode;
    }

    /**
     * @return mixed
     */
    public function getVerificationResult()
    {
        return $this->verificationResult;
    }

    /**
     * @param mixed $verificationResult
     */
    public function setVerificationResult($verificationResult): void
    {
        $this->verificationResult = $verificationResult;
    }

    /**
     * @return mixed|string
     */
    public function getVerificationPassed()
    {
        return $this->verificationPassed;
    }

    /**
     * @param mixed|string $verificationPassed
     */
    public function setVerificationPassed($verificationPassed): void
    {
        $this->verificationPassed = $verificationPassed;
    }

    /**
     * @return mixed|string
     */
    public function getVerificationResultMessage()
    {
        return $this->verificationResultMessage;
    }

    /**
     * @param mixed|string $verificationResultMessage
     */
    public function setVerificationResultMessage($verificationResultMessage): void
    {
        $this->verificationResultMessage = $verificationResultMessage;
    }

}