<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

/*
Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});*/

Route::any("/echo", function () {
    echo "ok";
});

//Route::any("/send_sms", "Yuntongxun@sendSms");

//Route::any("/send_wechat_template_message", "WeChat@sendTemplateMessage");

Route::prefix('userportrait')->namespace('Portrait')->middleware(['checkip'])->group(function () {
    Route::any("/forget-tag-config", "UserPortraitController@forgetTeacherGroupTagConfig");
    Route::any("/get-user-basic", "UserPortraitController@getUserBasic");
    Route::any("/get-qiwei-info", "UserPortraitController@getQiweiInfo");
    Route::any("/get-leader", "UserPortraitController@getLeader");
    Route::any("/get-leader-detail", "UserPortraitController@getLeaderDetail");
    Route::any("/get-researchgroup-accountids", "UserPortraitController@getResearchgroupAccountId");
    Route::any("/get-pm-statistic", "UserPortraitController@getPmStatistic");
    Route::any("/get-cooperate-crm", "UserPortraitController@getCooperateCrm");
    Route::any("/get-demand-statistic", "UserPortraitController@getDemandStatistic");
    Route::any("/get-credit-balance", "UserPortraitController@getUserCreditBalance");
    Route::any("/get-order-preference", "UserPortraitController@getPreferenceForPortrait");
    Route::any("/get-unsatisfied-info", "UserPortraitController@getUserUnSatisfiedInfo");
    Route::any("/get-nps", "UserPortraitController@getUserNps");
    Route::any("/get-tips-data", "UserPortraitController@getTopTipsNeedData");
});

Route::prefix('supplier')->namespace('Supplier')->middleware(['checkip'])->group(function () {
    //生成质控报告
    Route::get('/create-order-merchant-product-cert',
        'SupplierMerchantProductCertController@createOrderMerchantProductCert');
    //获取供应商主体列表
    Route::get('/get-provider-merchant-subject-names',
        "ProviderMerchantController@getProviderMerchantSubjectNames");
    Route::post('/supplier-event-notification-receive',[\App\Http\Controllers\Supplier\EventNotificationReceiveController::class,'receive']);
});

Route::middleware(['checkip'])->group(function () {

    Route::any("/send_sms", "Yuntongxun@sendSms");
    Route::any("/send_wechat_template_message", "WeChat@sendTemplateMessage");


    //支付宝转账
    Route::any("/alipay/fundtransfer", 'AlipayController@Fundtransfer');

    //校验发放购物卡 -在生成兑换订单前调用
    Route::post("/shoppingcard/checksendcard", 'ShoppingCardController@checkSendCard');
    //购物卡自动发放 -生成订单后调用
    Route::post("/shoppingcard/sendcard", 'ShoppingCardController@SendCard');
    //购物卡发送短信 -发放完成后调用
    Route::post("/shoppingcard/sendcardsms", 'ShoppingCardController@sedShopCardSms');
    //解除锁定 - 生成订单失败调用
    Route::post("/shoppingcard/cancellock", 'ShoppingCardController@cancelLock');

    Route::post("/account/login", "AccountController@login");
    Route::get("/account/info", "AccountController@info");
    Route::post("/account/consume_jifen", "AccountController@consumeJifen");
    Route::get("/account/consume_log", "AccountController@consumeJifenLog");

    //领取优惠券
    Route::post("/coupon/receive_coupon", "CouponController@receiveCoupon");
    //领取优惠券(一张优惠券一次赠送多人)
    Route::post("/coupon/batch_receive_coupon", "CouponController@batchReceiveCoupon");
    //领取团体协议优惠券
    Route::post('/coupon/receive-group-agreement-coupon', 'CouponController@receiveGroupAgreementCoupon');
    //团体预存券发放
    Route::post('/group-prepayment-coupon/send', 'GroupPrepaymentCouponController@batchSend');

    Route::get("/region/get_region", "RegionController@getRegion");

    //绑定操作
    Route::post("/account/wxbind", "BindController@wxBind");


    //注册
    Route::post("/account/register", "UserController@Register");

    //识别小程序用户的安全风控等级
    Route::post("/account/getUserRiskRank", "UserController@getUserRiskRank");

    Route::post('/snapshot/save', 'TableSnapshotController@save');

    //补单成功的回调（可以加通知，短信等）
    Route::post('/replenish/callback', 'ReplenishController@callback');

    //获取业务线对应区域二维码
    Route::post("/business/get_area_user_info", "BusinessArea@getAreaUserInfo");
    Route::post("/business/create_demand_data", "BusinessArea@createDemandData"); //发布需求
    Route::post("/business/create-sensors-record", "BusinessArea@createSensorsRecord"); //新增后端神策埋点

    //获取办事处渠道码
    Route::get("/buffet_address/get_channel_code", "BuffetAddressController@getChannelCode");

    //校验用户的3要素 手机号、身份证、姓名是否一致
    Route::post("/account/checkIdCard", "AccountController@checkIdCard");

    //开卡校验
    Route::post("/card/realNameVer", "CardController@realNameVer");

    //开卡审核-通过
    Route::post("/card/approved", "CardController@cardApproved");

    //开卡审核-提交
    Route::post("/card/submit", "CardController@submitExamined");

    //假开卡
    Route::post("/card/open-fake-card", "CardController@openFakeCard");

    //企业协议审核-提交
    Route::post("/card/submitCompanyPact", "CardController@submitCompanyPact");

    //单独三要素校验
    Route::post("/card/threeAuth", "CardController@threeAuth");

    //单独企业二要素校验
    Route::post("/card/companyAuth", "CardController@companyAuth");

    //校验用户的证件认证情况-用作开卡团体认证新增修改的默认值显示
    Route::post("/card/checkUserAuth", "CardController@checkUserAuth");

    //模糊搜索企业名称
    Route::post("/card/companyFuzzySearch", "CardController@companyFuzzySearch");

    //设置认证状态-aut_state
    Route::post("/card/setAuthState", "CardController@setAuthState");

    //设置团体未认证
    Route::post("/group/set-group-not-auth", "GroupController@setGroupNotAuth");

    //用户修改资料触发团体认证
    Route::post("/group/save-user-info-trigger-group-auth", "GroupController@saveUserInfoTriggerGroupAuth");

    //获取个人用户认证状态-可公用
    Route::post("/card/getUserAuthState", "CardController@GetUserAuthState");

    //获取认证审核通知人
    Route::post("/card/getAuthExamineNotifier", "CardController@getAuthExamineNotifier");

    //积分兑换
    Route::get("/integral/get_integral_good_list", "IntegralController@getIntegralGoodList");
    Route::get("/integral/get_integral_confirm_data", "IntegralController@getIntegralConfirmData");
    Route::post("/integral/exchange_integral_good", "IntegralController@exchangeIntegralGood");
    //是否有积分商城的权限
    Route::get("/integral/has-mall-permission", "IntegralController@hasMallPermission");
    //获取用户积分信息
    Route::get("/integral/get_user_integral_info", "IntegralController@getUserIntegralInfo");
    //兑换润文字数
    Route::post("/integral/exchange_text_number", "IntegralController@exchangeTextNumber");

    //获取订单的收样状态
    Route::get("/sampling/get_sampling_state", "SamplingController@getSamplingState");
    Route::get("/sampling/get_sampling_state_old", "SamplingController@getSamplingStateOld");
    Route::get("/sampling/get_one_sampling_state", "SamplingController@getSamplingStateByOid");

    //团体信用金操作 (开通、修改、关闭)
    Route::post("/group/setGroup", "GroupController@setGroup");
    //团体校验负责人认证情况
    Route::post("/group/checkGroupAuth", "GroupController@checkGroupAuth");
    //团体 获取认证数据+提交认证
    Route::post("/group/groupAuthentication", "GroupController@groupAuthentication");
    //团体认证，开通信用金等状态查询(业务状态太多，暂时不能删除，需要可以用getGroupAuthStatus)
    Route::post("/group/groupStatus", "GroupController@groupStatus");
    Route::post("/group/groupMemberAut", "GroupController@groupMemberAut");
    //创建转移码
    Route::post("/group/createHandoverQRcode", "GroupController@createHandoverQRcode");
    //执行转移并开通信用金
    Route::post("/group/handoverSuccess", "GroupController@handoverSuccess");
    //负责人处理转移申请
    Route::post("/group/groupAccountExamine", "GroupController@groupAccountExamine");
    //团体处理通知
    Route::post("/group/groupAuthNotices", "GroupController@groupAuthNotices");
    //人脸识别后操作
    Route::post("/group/faceSuccess", "GroupController@faceSuccess");
    Route::post("/group/replenishFaceSuccess", "GroupController@replenishFaceSuccess");
    Route::post("/group/isCreateGroup", "GroupController@isCreateGroup");

    //加入团体
    Route::post("/group/joinGroup", "GroupController@joinGroup");

    Route::namespace('AfterSale')->group(function () {
        //新建工单接口
        Route::post("/ticket/create", "TicketPmController@create");
        //资料库问题获取
        Route::get("/ticket-bank/get-by-problem-type", "TicketBankController@getTicketBankByProblemType");
        //获取问题类型列表
        Route::get('/problem-type/get-list', 'ProblemTypeController@getProblemTypeList');
        //获取问题处理人
        Route::get('/problem-type/get-handler', 'ProblemTypeController@getProblemHandler');
        //创建异议
        Route::post('/dissent/create', 'DissentController@createDissent');
    });

    //发放团体券包
    Route::post("/group_coupon/insert", "GroupCouponController@insertGroupCoupon");

    //订单回收相关
    Route::post("/recovery/save_recovery_status", "RecoveryController@saveRecoveryStatus"); //更新回收状态
    Route::post("/recovery/save_recovery_address", "RecoveryController@saveRecoveryAddress"); //修改回收地址
    Route::post("/recovery/save_recovery_locked", "RecoveryController@saveRecoveryLocked"); //更新回收状态
    Route::post("/recovery/save_is_recovery_data", "RecoveryController@saveIsRecoveryData"); //更新是否回收状态及地址
    Route::post("/recovery/create-notice-email", "RecoveryController@createNoticeEmail"); //新增回收寄出通知内容

    //获取带星密文
    Route::post("/encrypt/getStarsContent", "EncrypController@getStarsContent");
    //设置明文密文对应关系
    Route::post("/encrypt/addRelation", "EncrypController@addEncryptRelation");
    //密文解密
    Route::post("/encrypt/getContentPlaintext", "EncrypController@getContentPlaintext");
    //批量证件号解密
    Route::post("/encrypt/batch-id-card-decryption", "EncrypController@batchIdCardDecryption");

    //开票相关
    Route::post("/invoice/orderOpenTicket", "InvoiceController@orderOpenTicket");
    Route::post("/invoice/couponOpenTicket", "InvoiceController@couponOpenTicket");
    Route::post("/invoice/getTicketResult", "InvoiceController@getTicketResult");
    Route::post('/invoice/addOnlineInvoice', 'InvoiceController@addOnlineInvoice');
    Route::post('/invoice/getOriginOnlineInvoice', 'InvoiceController@getOriginOnlineInvoice');
    Route::post('/invoice/cancel-digital-red-confirm', 'InvoiceController@cancelDigitalRedConfirm');
    Route::post('/invoice/refresh-digital-red-confirm', 'InvoiceController@refreshDigitalRedConfirm');
    Route::post("/invoice/default-ticket-type", "InvoiceController@getDefaultTicketType");
    Route::get("/invoice/currency","InvoiceController@getCurrency");
    Route::post("/invoice/order-rate","InvoiceController@getOrderRate");
    Route::get("/invoice/pre-invoice-data","InvoiceController@getPreInvoiceData");
    //发票申请
    Route::post('/invoice/apply',"InvoiceController@apply");
    Route::post('/finance/back-ticket', "FinanceController@backTicket");

    Route::prefix('invoice')->namespace('Invoice')->group(function () {
        Route::get('/get-invoice-items-by-order-ids', 'OrderInvoiceController@getInvoiceItemsByOrderIds');
    });
    //抬头推荐
    Route::post('/finance/tax-num-propose',"FinanceTfInfoController@taxNumberPropose");
    //开收据
    Route::post('/invoice/receipt/open','ReceiptController@open');
    //收据作废
    Route::post('/invoice/receipt/cancel','ReceiptController@cancel');

    Route::post('/invoice/order/transform', 'InvoiceTransFormController@transformOrderInvoice');

    //获得订单对接人
    Route::any("/order/pickup", "OrderController@pickup");
    //获取项目经理二维码
    Route::any("/order/get_pm_qrcode", "OrderController@getPmQrcode");
    //获取用户的绩效归属员工的crm_account_id
    Route::any("/account/getAchievementsHead", "AccountController@getAchievementsHead");
    Route::any("/account/batchGetAchievementsHead", "AccountController@batchGetAchievementsHead");
    //获取测试项目对应办事处负责人
    Route::any("/buffet-address/get-project-leader", "BuffetAddressController@getProjectLeader");

    Route::post("/order/check-order-can-cancel", "OrderController@checkOrderCanCancel"); //校验订单是否可以取消
    Route::post("/order/cancel-order", "OrderController@cancelOrder"); //取消订单
    Route::post("/order/submit-questionnaire", "OrderController@submitQuestionnaire"); //用户提交问卷-目前仅取消订单触发

    Route::get('/order/get-exploratory-order-quotation', "OrderController@getExploratoryOrderQuotation");
    Route::post('/order/save-exploratory-order-quotation', "OrderController@saveExploratoryOrderQuotation");

    // 获取订单标签信息
    Route::post("/order/get-tags", "OrderController@getOrderTags");

    //发送邮件
    Route::post("/email/send", "EmailController@sendEmailAsync");
    Route::post("/email/send-sync", "EmailController@sendEmail");
    Route::post("/email/sendasync", "EmailController@sendEmailAsync");

    //银行卡号ocr识别
    Route::post("/bank/ocr", "BankController@BankCardOcr");
    //银行卡校验 2要素或者3要素
    Route::post("/bank/check", "BankController@BankCardCheck");
    //获取校内员工收益金额
    Route::post("/staff/getStaffIncome", "UniStaffController@getStaffIncome");
    //获取可领取任务
    Route::post("/staff/getApplyTask", "UniStaffController@getApplyTask");
    //获取已领取的任务
    Route::post("/staff/getEnrollTask", "UniStaffController@getEnrollTask");
    //获取子任务
    Route::post("/staff/getSubTask", "UniStaffController@getSubTask");
    //获取结算任务
    Route::post("/staff/getSettlementTask", "UniStaffController@getSettlementTask");
    //判断是否展示兼职生及任务模块
    Route::post("/staff/isShow", "UniStaffController@isShow");

    //创建团体-公共
    Route::post("/group/create", "GroupController@newCreateGroup");
    //校验是否可以加入团体
    Route::post("/group/joinCheck", "GroupController@joinGroupCheck");
    //校验是否可以创建团体
    Route::post("/group/createCheck", "GroupController@createGroupCheck");
    //查询团体认证状态
    Route::post("/group/getGroupAuthStatus", "GroupController@getGroupAuthStatus");
    //查询企业团体认证情况
    Route::post("/group/get-company-group-auth-status", "GroupController@getCompanyGroupAuthStatus");
    //企业团体与负责人认证信息对比
    Route::post("/group/company-group-responsible-mate", "GroupController@companyGroupResponsibleMate");
    //团体加入相关
    Route::get("/group/group-list", "GroupJoinController@getGroupList");
    Route::get("/group/qr-code", "GroupJoinController@getGroupJoinQrCode");
    //申请或邀请成员加入团体后，对团体管理员进行通知
    Route::get("/group/apply-notice", "GroupJoinController@applyNotice");
    //获得团体的信用金，预存等信息
    Route::get("/group/finance-info", "GroupFinanceController@getGroupFinanceInfo");
    Route::get("/group/finance-info-batch", "GroupFinanceController@getGroupFinanceInfoBatch");

    Route::post('/group/payment-aggregation-details', "Order\GroupOrderController@paymentAggregationDetails");

    //根据导师姓名，学校，学院 获取导师id
    Route::post("/group/getTeacherId", "GroupController@getTeacherId");
    //根据导师id推荐团体-导师自己的团体和导师下面的学生加入的团体
    Route::post("/group/getRecommendGroup", "GroupController@getRecommendGroup");
    //根据账号ID获得团体名称和团体认证状态
    Route::get("/group/get-info-by-account-ids", "GroupController@getGroupInfoByAccountIds");

    //根据课题组负责人id或姓名获取此课题组下所有客户id
    Route::get("/map-plan/get-account-ids-by-leader", "MapPlanSearchGroupController@getAccountIdsByLeader");

    //获取是不是内部员工账号
    Route::get("/user/getWhiteList", "UserController@getWhiteList");


    //加入团体时把个人信用金欠款的订单加入到团体
    Route::get("/group/join-group-order", "GroupJoinController@joinGroupOrder");
    //退出团体
    Route::get("/group/leave-group", "GroupJoinController@leaveGroup");
    //设置团体成员角色
    Route::get("/group/set-level", "GroupAdminController@setGroupLevel");

    //退出团体时把进入团体时带入的并还欠款订单去掉
    Route::get("/group/leave-group-order", "GroupJoinController@leaveGroupOrder");
    //导出所有团体订单
    Route::get("/group/group-order-export", "GroupDataController@groupOrder");
    //操作信用金的冻结状态
    Route::post("/group/change-freeze-status", "GroupFinanceController@changeFreezeStatus");

    //填写愿望
    Route::post("/goods-wish", "WishController@addGoodsWish");

    //根据IP获取省份城市信息
    Route::get("/account/based-ip-get-province-city", "AccountController@basedIpGetProvinceCity");

    //证件校验(大陆、港澳、台、中国绿卡)
    Route::post("/card/id-card-verification", "CardController@idCardVerification");

    //搜索标准企业
    Route::post("/card/search-standard-enterprise", "CardController@searchStandardEnterprise");
    //判断标准企业接口
    Route::get('/card/judgment-standard-enterprise', 'CardController@judgmentStandardEnterprise');

    //维护企业和用户的企业信息
    Route::post("/company/save-company-info", "CompanyController@saveCompanyInfo");

    //转发实验家的接口
    Route::post("/shiyanjia/request-json", "ProxyController@shiyanjiaJson");

    //获得用户虚拟号
    Route::get("/user/get-mock-mobile", "MockMobileController@getMockMobile");

    Route::post('/account/add-tag', "AccountController@addTag");
    //是否有未拆分的预存金额
    Route::post('/prepayment/is-has-un-split-prepayment', "PrepaymentController@isHasUnSplitPrepayment");

    // 获取预存拆分金额
    Route::post('/prepayment/get_prepayment_recharge_type', "PrepaymentController@getPrepaymentRechargeType");

    // 预存账号预存金额拆分
    Route::post('/prepayment/get-split-prepayment-amount', "PrepaymentController@getSplitPrepaymentAmount");
    // 个人预存账号余额
    Route::post('/prepayment/get-coupon-prepayment-amount', "PrepaymentController@getCouponPrepaymentAmount");
    Route::post('/prepayment/get-yan-su-prepayment-amount', "PrepaymentController@getYanSuPrepaymentAmount");
    // 个人欠款金额
    Route::post('/credit/get-credit-amount', "CreditController@getCreditAmount");
    Route::post('/credit/get-account-credit-Amount-by-account-ids', "CreditController@getAccountCreditAmountByAccountIds");
    Route::post('/credit/get-order-credit-amount-by-account-ids', "CreditController@getOrderCreditAmountByAccountIds");
    Route::post('/credit/get-order-credit-amount', "CreditController@getOrderCreditAmount");
    Route::post('/credit/get-ticket-credit-amount', "CreditController@getTicketCreditAmount");
    Route::post('/credit/get-order-match-serial-no-amount', "CreditController@getOrderMatchSerialNoAmount");
    Route::post('/credit/get-pending-and-repaid-order-credit-amount-by-account-ids',
        "CreditController@getPendingAndRepaidOrderCreditAmountByAccountIds");

    //更新用户等级
    Route::post("/user/update-member-grade", "UserController@updateMemberGrade");
    //获取用户积分系数
    Route::post("/user/get-user-integral-coefficient", "UserController@getUserIntegralCoefficient");
    //更新用户等级-redis入列
    Route::post("/user/update-member-grade-insert-queue", "UserController@updateMemberGradeInsertQueue");

    //企业打标签
    Route::post('/company/tag-company', "CompanyController@tagCompany");

    //主体
    Route::post("/provider/default-provider-list-for-orders", "FinanceProviderController@getDefaultProviderListForOrders");
    Route::post("/provider/provider-list-for-coupon-applies", "FinanceProviderController@getProviderListForCouponApplies");

    //生成科研工作台邀请码
    Route::post("/workbench/generate-invite-code", "ScienceWorkbenchController@generateInviteCode");
    //激活邀请码
    Route::post("/workbench/active-invite-code", "ScienceWorkbenchController@activeInviteCode");
    //获得PDF的页数
    Route::post("/workbench/get-number-of-pages", "ScienceWorkbenchController@getNumberOfPages");
    //敏感词检测
    Route::post("/sensitive-check/check-text", "SensitiveCheckController@checkText");

    //用户同意协议记录
    Route::post("/account/user-agree-agreement-log", "UserController@userAgreeAgreementLog");

    //退单更新邀请奖励
    Route::post("/invite/refund-update-reward", "InviteRewardController@refundUpdateReward");
    //下单新增订单返利
    Route::post("/invite/add-invite-rebate-order", "InviteRewardController@addInviteRebateOrder");
    //邀请注册风控校验
    Route::post("/invite/risk-check", "InviteRewardController@riskCheck");
    //获取分词的接口
    Route::post("/nlp/get-text-segments","NlpController@getTextSegments");
    //通过论文致谢后发送邮件
    Route::post("/paper-award/send-success-email","PaperAwardController@sendSuccessEmail");

    //获取仪器详细信息
    Route::get("/lab-assets/get-asset-detail", "LabAssetsController@getAssetDetail");
    //获取仪器档案的审核状态
    Route::get("/lab-assets/batch-get-asset-audit-status", "LabAssetsController@batchGetAssetAuditStatus");
    //未安装列表
    Route::get("/lab-assets/unuse-lab-assets-list", "LabAssetsController@getUnUseLabAssetsList");
    //获取仪器类型
    Route::get("/lab-assets/get-asset-type", "LabAssetsController@getAssetType");

    //仪器调拨
    Route::prefix("/lab-assets/allocation")->group(function () {
        Route::post("/create", "LabAssetsController@createAllocation");
        Route::post("/delete", "LabAssetsController@deleteAllocation");
        Route::get("/list", "LabAssetsController@getAllocationList");
        Route::post("/edit", "LabAssetsController@editAllocation");
    });

    //赠送加急券
    Route::post("/vas-coupon/send-coupon","VasCouponController@sendCoupon");
    //查询用户的加急券
    Route::get("/vas-coupon/query-coupon","VasCouponController@queryCoupon");
    //使用加急券
    Route::post("/vas-coupon/use-coupon","VasCouponController@useCoupon");
    //取消加急券的使用
    Route::post("/vas-coupon/cancel-coupon","VasCouponController@cancelCoupon");

    // 纳米商城明细优惠金额计算
    Route::post("/nanometer/update-price-discount-amount","NanometerController@updatePriceDiscountAmount");
    Route::post("/nanometer/judge-nano-order-is-have-hazardous","NanometerController@judgeNanoOrderIsHaveHazardous");

    //获取是不是新能源用户
    Route::get("/account/check-new-energy-company-account", "UserController@checkNewEnergyCompanyAccount");
    /**
     * 三方服务
     */
    Route::get("/sf-token", "SFController@getToken");
    Route::post("/sf-download", "SFController@downloadFile");
    //通过证件照片获取证件姓名
    Route::post("/id-card/ocr", "IdCardController@IdCardOcr");

    //获取乐橙直播的accessToken
    Route::get("/le-cheng/access-token", "LiveController@accessToken");
    //获取乐橙直播的minToken
    Route::post("/le-cheng/min-token", "LiveController@getMinToken");
    //查询设备直播地址
    Route::post("/le-cheng/get-live-stream-info", "LiveController@getLiveStreamInfo");
    //清楚设备的水印
    Route::get("/le-cheng/remove-device-watermark", "LiveController@removeDeviceWatermark");

    //获取订单的快递时长
    Route::get("/order/get-delivery-time", "OrderController@getOrderDeliveryTime");

    //获取订单复测状态
    Route::post("/order/get-retest-status","OrderController@getOrderRetestStatus");


    /**
     * 测试内容（问卷）
     */
    Route::post("/test-content/preview", "TestContentController@preview");

    //根据流水号和附言查询疑似身份
    Route::post('auto-invoice/search-suspected-identity-by-postscript',
        'AutoInvoiceController@searchSuspectedIdentityByPostscript');

    //获取实验员名字
    Route::get('project-log/get-tester-name', 'ProjectLogController@getTesterName');
    Route::get('project-log/get-tester', 'ProjectLogController@getTester');

    //校验是否可以预约
    Route::get("/merchant-buffet/check-can-book-segment-with-provider-product", "MerchantBuffetController@checkCanBookSegmentWithProviderProduct");

    //云视频测试项目的时间段
    Route::get("/merchant-buffet/get-segment-with-provider-product", "MerchantBuffetController@getSegmentWithProviderProduct");

    //云视频获取时间段
    Route::get("/merchant-buffet/get-segment", "MerchantBuffetController@getSegment");
    //云视频批量设置时间段
    Route::post("/merchant-buffet/set-product-segment-batch", "MerchantBuffetController@setProductSegmentBatch");
    //云视频获取机时列表
    Route::get("/merchant-buffet/get-segment-with-book-info", "MerchantBuffetController@getSegmentWithBookInfo");

    //订单成本相关
    Route::post('/order-cost/get-order-costs', 'OrderController@getOrderCosts');
    Route::post('/get-order-overdue_days', 'OrderController@getOrderOverdueDays');
    Route::post('/order-cost/get-order-settle-costs', 'OrderController@getOrderSettleCosts');
    Route::prefix('order-cost')->namespace('Order')->group(function () {
        Route::post('/update-order-tilt-mechanism-overdue-discount',
            'OrderCostController@updateOrderTiltMechanismOverdueDiscount');
    });
});

Route::prefix('order')
    ->namespace('Order')
    ->middleware(['checkip'])
    ->group(function () {
    // 是否为 测试+分析一体化 订单
    Route::get('/is-test-with-analysis-order', 'OrderTestWithAnalysisController@isTestWithAnalysisOrder');
    // 检测是否满足测试+分析一体化
    Route::post('/check-test-with-analysis', 'OrderTestWithAnalysisController@checkTestWithAnalysis');
    // 获取订单报告的可见性配置
    Route::get('/get-report-visibility', 'OrderReportController@getReportVisibility');
    // 批量获取订单报告可见性配置
    Route::post('/get-orders-report-visibility', 'OrderReportController@getOrdersReportVisibility');

    // 寄样订单预排班
    Route::prefix('schedule-order')->group(function () {
        // 三方恢复暂停的订单
        Route::post('/vendor-resume-test', "ScheduleOrderController@vendorResumeTest");
    });
});

Route::prefix('order-vas')
    ->namespace('Order')
    ->middleware(['checkip'])
    ->group(function () {
        // 获取订单的样品宝服务状态
        Route::post('/get-sample-insurance-service-status', 'OrderVasController@getSampleInsuranceServiceStatus');
        // 获取已启用的订单增值服务信息
        Route::post('/get-enabled-orders-vas', 'OrderVasController@getEnabledOrdersVas');
        // 闪测服务是否受限
        Route::post('/is-flash-test-restricted', 'OrderVasController@isFlashTestRestricted');
    });

Route::prefix('order-sampling')
    ->namespace('Sampling')
    ->middleware(['checkip'])
    ->group(function () {
        // 更新取样时间
        Route::post('/update-sampling-time', 'OrderSamplingController@updateSamplingTime');
        //更新取样信息
        Route::post('/update-sampling-info', 'OrderSamplingController@updateSamplingInfo');


        // 取样地址管理
        Route::prefix('address')->group(function () {
            Route::get('list', 'SamplingAddressManageController@getAddressList');
            Route::post('save', 'SamplingAddressManageController@saveAddress');
            Route::post('delete', 'SamplingAddressManageController@deleteAddress');
            Route::post('set-default', 'SamplingAddressManageController@setDefaultAddress');
        });
    });


Route::prefix('standard-report')->middleware(['checkip'])->group(function () {
    Route::post('/get-report-list', "StandardReportController@getReportList");
    Route::post('/generate-async', 'StandardReportController@generateAsync');
    Route::post('/generate-sync', 'StandardReportController@generateSync');
    Route::post('/convert-to-pdf','StandardReportController@convertToPDF');
    Route::get('/get-status-by-id-list','StandardReportController@getStatusByIdList');
    Route::get('/get-complete-report','StandardReportController@getCompleteReport');
    Route::get('/get-download-url','StandardReportController@getDownloadUrl');
    Route::post('/generate-result','StandardReportController@generateResult');
    Route::post('/copy-object','StandardReportController@copyObject');
    Route::get('/get-web-office-token','StandardReportController@getWebOfficeToken');
    Route::get('/get-order-result-image','StandardReportController@getOrderResultImage');
});

// 活动奖品
Route::prefix('activity-prize')->namespace('Activity')->middleware(['checkip'])->group(function () {
    // 创建领奖记录
    Route::post('create-prize-user', 'ActivityPrizeController@createPrizeUser');
    // 发货
    Route::post('send-prize', 'ActivityPrizeController@sendPrize');
});


Route::prefix('document')->group(function () {
    Route::post('preview', "DocumentPreviewController@preview");
});

Route::prefix('allot')->namespace('Order')->group(function () {
    Route::get('/get-allot-info', "AllotController@getAllotInfo");
});

Route::prefix('order-questionnaire')->namespace('Order')->group(function () {
    Route::get('/get-sample-group-info', "OrderQuestionnaireController@getDetailByGroup");
});
Route::prefix('retest')->namespace('Retest')->group(function () {
    Route::get('/get-list-by-osn', "RetestController@getRestListByOsn");
    Route::get("/order-has-aftersale", "RetestController@hasAfterSale");
});

Route::prefix('typical-case-library')->group(function () {
    Route::get('/test-template/index', "TypicalCaseTestTemplateController@index");
    Route::get('/test-template/refer-list', "TypicalCaseTestTemplateController@referList");
    Route::post('/test-template/save', "TypicalCaseTestTemplateController@save");
    Route::get('/test-template/show', "TypicalCaseTestTemplateController@show");
    Route::post('/test-template/delete', "TypicalCaseTestTemplateController@delete");
    Route::post('/test-template/submit-review', "TypicalCaseTestTemplateController@submitReview");
    Route::post('/test-template/review', "TypicalCaseTestTemplateController@review");
    Route::post('/test-template/switch-reviewer', "TypicalCaseTestTemplateController@switchReviewer");
    Route::post('/case/set-excellent', "TypicalCaseController@setExcellent");
    Route::post('/case/save', "TypicalCaseController@save");
    Route::post('/case/create-from-process', "TypicalCaseController@createFromProcess");
    Route::post('/case/delete', "TypicalCaseController@delete");
    Route::get('/case/show', "TypicalCaseController@show");
    Route::post('/case/index', "TypicalCaseController@index");        // 有问卷数据，所以用post
    Route::get('/case/unique', "TypicalCaseController@unique");        // 判断case是否存在
    Route::get('/case/sample-category', "TypicalCaseController@getSampleCategory");
    Route::get('/case/order-buffet', "TypicalCaseController@getOrderBuffet");
    Route::get('/case/order-info', "TypicalCaseController@getOrderInfo");
    Route::get('/case/order-sample-ingredient', "TypicalCaseController@getOrderSampleIngredient");
    Route::get('/case/guess-sample-category', "TypicalCaseController@guessSampleCategory");
    Route::post('/case/invite-co-worker', "TypicalCaseController@inviteCoWorker");
    Route::get('/case/abnormal', "TypicalCaseController@getAbnormal");
    Route::post('/review/submit', "TypicalCaseReviewController@submitForReview");
    Route::post('/review/pass', "TypicalCaseReviewController@pass");
    Route::post('/review/reject', "TypicalCaseReviewController@reject");
    Route::post('/review/switch-owner', "TypicalCaseReviewController@switchOwner");
    Route::get('/abnormal-reason/get-list', "TypicalCaseAbnormalReasonController@getList");
    Route::post('/abnormal-reason/save', "TypicalCaseAbnormalReasonController@save");
});

Route::prefix('order-test-process')->group(function () {
    Route::post('/save', "OrderTestProcessController@save");
    Route::get('/index', "OrderTestProcessController@index");
});





Route::prefix('infra')->namespace('Infra')->group(function () {
    Route::prefix('oss')->group(function () {
        Route::post('/restore-by-url','OSSController@restoreByUrl');
        Route::post('/get-object-info-by-url','OSSController@getObjectInfoByUrl');
        Route::get('/get-sign-url','OSSController@getSignUrl');
    });
});

Route::prefix('order-rating')->namespace('Order')->group(function () {
    Route::post('/get-rating', "OrderRatingController@getRating");
});

Route::namespace('AfterSale')->group(function () {
    Route::post('/ticket/get-order-ticket-status', "TicketPmController@getOrderTicketStatus");
});

Route::namespace('VideoTest')->prefix('video-test')->group(function () {
    Route::post('/start-timer', "VideoTestOperateController@startTimer");
    Route::post('/stop-timer', "VideoTestOperateController@stopTimer");
    Route::post('/pre-stop-timer', "VideoTestOperateController@preStopTimer");
    Route::get('/get-record-by-oid', "VideoTestOperateController@getRecordListByOid");
    Route::get('/get-timer-detail', "VideoTestOperateController@getTimerDetail");
});

Route::prefix('nanometer')->group(function () {
    Route::get('/get-available-coupon-list', "NanoCouponController@getAvailableCouponList");
    Route::get('/get-coupon-scope', "NanoCouponController@getCouponScope");
    Route::get('/get-final-discount-amount', "NanoCouponController@getFinalDiscountAmount");
    Route::get('/check-coupon-item-can-use', "NanoCouponController@checkCouponItemCanUse");
});

/**
 * 待办相关统一接口
 */
Route::prefix('todo')
    ->namespace('Todo')
    ->middleware(['checkip'])
    ->group(function () {
        // 创建待办
        Route::post('/create-todo', 'TodoController@createTodo');
        // 批量创建待办
        Route::post('/create-todos', 'TodoController@createTodos');
        // 更新待办
        Route::post('/update-todo', 'TodoController@updateTodo');
        // 待办完成
        Route::post('/finish-todo', 'TodoController@finishTodo');
        // 删除待办
        Route::post('/del-todo', 'TodoController@delTodo');
        // 工作台待办手动完成
        Route::post('/deal-manual', 'TodoController@dealTodoManual');
        // 转交待办
        Route::post('/hand-over-todo','TodoController@todoHandOver');
        // 获取其他信息
        Route::get('/get-other-info', 'TodoController@getOtherInfo');
    });

// 分析数据模板
Route::prefix("analysis-data-template")->middleware(['checkip'])->group(function () {
    // 项目模板统计
    Route::get("/get-product-list", "AnalysisDataTemplateController@getProductList");
    // 项目模板列表
    Route::get("/get-template-list", "AnalysisDataTemplateController@getTemplateList");
    // 从模板新建文件
    Route::get("/get-template-copy", "AnalysisDataTemplateController@getTemplateCopy");
    // 新增模板
    Route::post('/add', "AnalysisDataTemplateController@add");
    // 编辑模板
    Route::post('/edit', "AnalysisDataTemplateController@edit");
    // 删除模板
    Route::post('/delete', "AnalysisDataTemplateController@delete");
});
/* 供应商支付 */
Route::prefix('provider-merchant-payment')->namespace('ProviderMerchantPayment')->middleware(['checkip'])
    ->group(function () {
        //供应商发票核销申请列表
        Route::get('/get-reconcile-status-enums',
            "ProviderMerchantInvoiceReconciliationController@getReconcileStatusEnums");
        Route::get('/get-reconcile-type-enums',
            "ProviderMerchantInvoiceReconciliationController@getReconcileTypeEnums");
        Route::get('/get-reconciliation-applications',
            "ProviderMerchantInvoiceReconciliationController@getReconciliationApplications");
        Route::get('/get-reconciliation-application-detail',
            "ProviderMerchantInvoiceReconciliationController@getReconciliationApplicationDetail");
        //操作供应商发票核销申请
        Route::post('/reject-reconciliation-application',
            "ProviderMerchantInvoiceReconciliationController@rejectReconciliationApplication");
        Route::post('/confirm-reconcile',
            "ProviderMerchantInvoiceReconciliationController@confirmReconcile");
        Route::post('/audit-reconciliation',
            "ProviderMerchantInvoiceReconciliationController@auditReconciliationBySpecialist");

        //供应商发票
        Route::post('/check-before-save-prepayment-invoices',
            "ProviderMerchantPrepaymentController@checkBeforeSavePrepaymentInvoices");
        Route::post('/save-prepayment-invoices',
            "ProviderMerchantPrepaymentController@savePrepaymentInvoices");
        Route::get('/get-prepayment-invoices',
            "ProviderMerchantInvoiceController@getPaymentInvoices");
        Route::get('/get-payment-invoices',
            "ProviderMerchantInvoiceController@getPaymentInvoices");

        //供应商预存申请
        Route::get('/get-prepayments', 'ProviderMerchantPrepaymentController@getPrepayments');
        Route::get('/get-export-prepayments', 'ProviderMerchantPrepaymentController@getExportPrepayments');
        Route::get('/get-prepayments-for-upload-invoice',
            'ProviderMerchantPrepaymentController@getPrepaymentsForUploadInvoice');
        Route::post('/create-ekuaibao-corporate-payment-flow-for-prepayment',
            'ProviderMerchantPrepaymentController@createEkuaibaoCorporatePaymentFlow');
        Route::post('/create-ekuaibao-prepayment-flow-for-prepayment',
            'ProviderMerchantPrepaymentController@createEkuaibaoPrepaymentFlow');
        Route::get('/get-prepayment-detail', 'ProviderMerchantPrepaymentController@getPrepaymentDetail');
        Route::post('/check-invoices-before-add-prepayment',
            "ProviderMerchantPrepaymentController@checkInvoicesBeforeAddPrepayment");

        //供应商支付公共接口
        Route::get('/get-provider-merchant-payment-error-logs',
            "ProviderMerchantPrepaymentController@getProviderMerchantPaymentErrorLogs");
        Route::post('/save-ekuaibao-flow-code', "ProviderMerchantPaymentController@saveEkuaibaoFlowCode");

        //供应商结算
        Route::prefix('settle')->group(function () {
            Route::get('/get-list-for-upload-invoice',
                "OrderSettleController@getOrderSettlesForUploadInvoice");
            Route::post('/save-invoices', "OrderSettleController@saveOrderSettleInvoices");
            Route::post('/check-invoices-before-settle',"OrderSettleController@checkInvoicesBeforeSettle");
        });
    });

/* 发票工具 */
Route::prefix('invoice-tool')->namespace('InvoiceTool')->middleware(['checkip'])->group(function () {
    Route::post('/invoice-ocr', "InvoiceToolController@invoiceOcrAndSave");
    Route::post('/verify-invoice', "InvoiceToolController@verifyInvoiceAndSave");
});

/* crm账号 */
Route::prefix('crm-account')->namespace('CrmAccount')->middleware(['checkip'])->group(function () {
    Route::get('/get-accounts', "CrmAccountController@getCrmAccounts");
});


/* 回调 */
Route::prefix('callback')->group(function () {
    Route::prefix('ekuaibao')->namespace('Ekuaibao')->group(function () {
        Route::post('/flow-paid-callback', "EkuaibaoCallBackController@FlowPaidCallback");
    });
});

Route::prefix('buffet')
    ->namespace('Buffet')
    ->middleware(['checkip'])
    ->group(function () {
        Route::prefix('sample-category')->group(function() {
            // 获取样品分类列表
            Route::get('/category-id-path-list', 'BuffetSampleCategoryController@getBuffetCategoryIdPathList');
            // 获取分类关联的商品列表
            Route::get('/category-buffet-list', 'BuffetSampleCategoryController@getCategoryBuffetList');
            // 获取商品关联的分类ids
            Route::get('/buffet-category-ids', 'BuffetSampleCategoryController@getBuffetCategoryIds');

            // 检查分类关联的各商品案例信息
            Route::get('/check-category-buffet-case', 'BuffetSampleCategoryController@checkCategoryBuffetCase');
            // 新增分类
            Route::post('/add-category', 'BuffetSampleCategoryController@addCategory');
            // 修改分类
            Route::post('/update-category', 'BuffetSampleCategoryController@updateCategory');
            // 检查分类树下关联的商品
            Route::get('/check-category-tree-buffets', 'BuffetSampleCategoryController@checkCategoryTreeBuffets');
            // 删除分类
            Route::post('/delete-category', 'BuffetSampleCategoryController@deleteCategory');
            // 检查是否存在指定商品和分类的案例
            Route::get('/check-case-relation', 'BuffetSampleCategoryController@checkCaseRelation');
            // 修改商品和分类的绑定关系
            Route::post('/update-bind', 'BuffetSampleCategoryController@updateBind');
            // 分类排序
            Route::post('/update-category-sort', 'BuffetSampleCategoryController@updateCategorySort');
        });
    });

/* 订单快递 */
Route::prefix('order-express')->namespace('Order')->group(function () {
    //用户快递
    Route::get('/get-user-express-info', 'OrderExpressController@getUserExpressInfo');
    Route::post('/save-user-express-data', 'OrderExpressController@saveUserExpressData');
});

// 客户邮箱相关接口
Route::prefix('account-email')
    ->namespace("Account")
    ->middleware(['checkip'])
    ->group(function () {
        Route::get('/get-emails', 'AccountEmailController@getEmails');
        Route::post('/add-email', 'AccountEmailController@addEmail');
    });

// 智能外呼系统相关接口（公网访问）
Route::prefix('smart-call')
    ->middleware(['checkip'])
    ->group(function () {
        // 客户管理
        Route::post('/customers/import', 'SmartCallController@importCustomers');

        // 任务管理
        Route::post('/task/save', 'SmartCallController@saveTask');
        Route::get('/task/list', 'SmartCallController@getTaskList');
        Route::get('/task/detail', 'SmartCallController@getTaskDetail');
        Route::post('/task/start', 'SmartCallController@startTask');
        Route::post('/task/stop', 'SmartCallController@stopTask');
        Route::post('/task/delete', 'SmartCallController@deleteTask');

        // 资源管理
        Route::get('/bot/list', 'SmartCallController@getBotList');
        Route::get('/did/list', 'SmartCallController@getDidList');

        // 工具接口
        Route::get('/environment/info', 'SmartCallController@getEnvironmentInfo');
    });