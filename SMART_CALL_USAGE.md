# 智能外呼系统使用说明

## 概述

本文档描述了智能外呼系统的核心API接口，包括任务创建和客户导入功能。

## 环境配置

### 环境判断逻辑

系统根据 `.config.ini` 文件中的 `env` 参数判断当前环境：

- **生产环境** (`env=prod`): 直接通过，无需额外校验
- **测试环境** (`env=test`): 需要校验用户是否在白名单中，并过滤客户手机号
- **开发环境** (`env=dev`): 需要校验用户是否在白名单中

### 白名单机制

- **IP白名单**: 所有请求都需要通过IP白名单校验
- **用户白名单**: 测试环境下需要校验用户ID是否在白名单中
- **手机号白名单**: 测试环境下导入客户时，只保留白名单中的手机号

## API接口

### 1. 创建/更新外呼任务

```http
POST /api/smart-call/task/save
```

根据网易七鱼官方接口文档实现，支持完整的任务创建和更新功能。

**必填参数**:
- `taskName`: 任务名称（最多64个字符）
- `botId`: 话术ID
- `didNumbers`: 线路号码列表
- `robotSeat`: 运行任务需要的坐席数（最小1）
- `recall`: 自动重播开关（0-关闭，1-开启）

**可选参数**:
- `account_id`: 用户ID（测试环境必填）
- `taskId`: 任务ID（更新任务时必填）
- `startType`: 任务启动类型（0-手动开启，1-定时开启）
- `lineType`: 线路策略类型（0-普通线路，1-智能外显，3-号码池）
- `hangupSms`: 挂机短信开关（0-关闭，1-开启）
- `flashSms`: 闪信开关（0-关闭，1-开启）
- 其他参数请参考网易七鱼官方文档

**请求示例**:
```json
{
    "taskName": "测试外呼任务",
    "botId": 123,
    "didNumbers": ["************"],
    "robotSeat": 1,
    "recall": 0,
    "startType": 0,
    "account_id": 12345
}
```

### 2. 导入客户信息

```http
POST /api/smart-call/customers/import
```

**请求参数**:
- `customers`: 客户列表（必填）
  - `name`: 客户姓名（必填，最多100个字符）
  - `phone`: 手机号（必填，最多20个字符）
  - `task_id`: 任务ID（必填）
- `account_id`: 用户ID（测试环境必填）
- `encrypt_key`: 加密密钥（可选，16位）

**请求示例**:
```json
{
    "customers": [
        {
            "name": "张三",
            "phone": "***********",
            "task_id": "123"
        }
    ],
    "account_id": 12345
}
```

**注意**: 测试环境下，系统会自动过滤非白名单手机号。

### 3. 获取环境信息

```http
GET /api/smart-call/environment/info
```

用于调试和确认当前环境配置。

## 配置要求

### 环境变量

```env
# 智能外呼配置
SMART_CALL_URL=https://b.163.com/open/api/wecall
SMART_CALL_APP_KEY=your_app_key_here
SMART_CALL_APP_SECRET=your_app_secret_here
SMART_CALL_APP_TYPE=your_channel_tag
```

### .config.ini 配置

```ini
;env: 当前环境，dev-开发环境，test-测试环境，prod-生产环境
env = test

;智能外呼
smart_call_app_key = "your_app_key_here"
smart_call_app_secret = "your_app_secret_here"
smart_call_base_url = "https://b.163.com/open/api/wecall"
```

## 核心特性

1. **环境区分**: 根据配置自动判断环境并应用相应的校验规则
2. **白名单过滤**: 测试环境自动过滤非白名单用户和手机号
3. **IP校验**: 所有接口都需要通过IP白名单校验
4. **网易七鱼兼容**: 完全按照官方接口文档实现

## 注意事项

1. 只实现了你明确需要的功能：任务创建和客户导入
2. 测试环境需要提供 `account_id` 参数进行白名单校验
3. 所有代码符合 PHP 7.1.3 语法要求
4. 优先从 `.config.ini` 读取配置，回退到环境变量
