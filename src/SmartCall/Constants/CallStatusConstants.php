<?php

namespace Yanqu\YanquPhplib\SmartCall\Constants;

/**
 * 智能外呼状态常量
 */
class CallStatusConstants
{
    // ==================== 任务状态 TaskStatus ====================

    /**
     * 任务状态：未开始
     */
    const TASK_NOT_START = 'NOT_START';

    /**
     * 任务状态：进行中
     */
    const TASK_RUNNING = 'RUNNING';

    /**
     * 任务状态：已完成
     */
    const TASK_COMPLETE = 'COMPLETE';

    /**
     * 任务状态：生效中(计时中,定时任务专有)
     */
    const TASK_COUNT_DOWNING = 'COUNT_DOWNING';

    /**
     * 任务状态：已过期
     */
    const TASK_EXPIRED = 'EXPIRED';

    // ==================== 通话状态 ReplyStatus ====================

    /**
     * 通话状态：已接听
     */
    const REPLY_ANSWER = 'ANSWER';

    /**
     * 通话状态：无应答
     */
    const REPLY_NO_REPLY = 'NO_REPLY';

    /**
     * 通话状态：忙线中
     */
    const REPLY_BUSY = 'BUSY';

    /**
     * 通话状态：关机
     */
    const REPLY_CLOSE = 'CLOSE';

    /**
     * 通话状态：停机
     */
    const REPLY_SHUT_DOWN = 'SHUT_DOWN';

    /**
     * 通话状态：拒接
     */
    const REPLY_REJECT = 'REJECT';

    /**
     * 通话状态：空号
     */
    const REPLY_NOT_EXIST = 'NOT_EXIST';

    /**
     * 通话状态：无法接通
     */
    const REPLY_UN_CONNECTED = 'UN_CONNECTED';

    /**
     * 通话状态：欠费
     */
    const REPLY_OWE_MONEY = 'OWE_MONEY';

    /**
     * 通话状态：外呼失败
     */
    const REPLY_OUT_CALL_FAIL = 'OUT_CALL_FAIL';

    /**
     * 通话状态：呼叫中
     */
    const REPLY_CALL_ING = 'CALL_ING';

    /**
     * 通话状态：外呼失败(无挂断回调)
     */
    const REPLY_NO_CALLBACK = 'NO_CALLBACK';

    // ==================== 挂断状态 HangUpStatus ====================

    /**
     * 挂断状态：默认
     */
    const HANGUP_DEFAULT = 'DEFAULT';

    /**
     * 挂断状态：客户挂断
     */
    const HANGUP_CUSTOMER_HANGUP = 'CUSTOMER_HANGUP';

    /**
     * 挂断状态：AI挂断
     */
    const HANGUP_AI_HANGUP = 'AI_HANGUP';

    /**
     * 挂断状态：已转人工
     */
    const HANGUP_MANUAL = 'MANUAL';

    // ==================== 状态映射 ====================

    /**
     * 任务状态映射
     */
    const TASK_STATUS_MAP = [
        self::TASK_NOT_START => '未开始',
        self::TASK_RUNNING => '进行中',
        self::TASK_COMPLETE => '已完成',
        self::TASK_COUNT_DOWNING => '生效中(计时中)',
        self::TASK_EXPIRED => '已过期',
    ];

    /**
     * 通话状态映射
     */
    const REPLY_STATUS_MAP = [
        self::REPLY_ANSWER => '已接听',
        self::REPLY_NO_REPLY => '无应答',
        self::REPLY_BUSY => '忙线中',
        self::REPLY_CLOSE => '关机',
        self::REPLY_SHUT_DOWN => '停机',
        self::REPLY_REJECT => '拒接',
        self::REPLY_NOT_EXIST => '空号',
        self::REPLY_UN_CONNECTED => '无法接通',
        self::REPLY_OWE_MONEY => '欠费',
        self::REPLY_OUT_CALL_FAIL => '外呼失败',
        self::REPLY_CALL_ING => '呼叫中',
        self::REPLY_NO_CALLBACK => '外呼失败(无挂断回调)',
    ];

    /**
     * 挂断状态映射
     */
    const HANGUP_STATUS_MAP = [
        self::HANGUP_DEFAULT => '默认',
        self::HANGUP_CUSTOMER_HANGUP => '客户挂断',
        self::HANGUP_AI_HANGUP => 'AI挂断',
        self::HANGUP_MANUAL => '已转人工',
    ];

    // ==================== 工具方法 ====================

    /**
     * 获取任务状态名称
     * @param string $status
     * @return string
     */
    public static function getTaskStatusName($status)
    {
        return isset(self::TASK_STATUS_MAP[$status]) ? self::TASK_STATUS_MAP[$status] : '未知状态';
    }

    /**
     * 获取通话状态名称
     * @param string $status
     * @return string
     */
    public static function getReplyStatusName($status)
    {
        return isset(self::REPLY_STATUS_MAP[$status]) ? self::REPLY_STATUS_MAP[$status] : '未知状态';
    }

    /**
     * 获取挂断状态名称
     * @param string $status
     * @return string
     */
    public static function getHangUpStatusName($status)
    {
        return isset(self::HANGUP_STATUS_MAP[$status]) ? self::HANGUP_STATUS_MAP[$status] : '未知状态';
    }

    /**
     * 判断任务是否为终态
     * @param string $status
     * @return bool
     */
    public static function isTaskFinalStatus($status)
    {
        return in_array($status, [
            self::TASK_COMPLETE,
            self::TASK_EXPIRED
        ]);
    }

    /**
     * 判断通话是否为终态
     * @param string $status
     * @return bool
     */
    public static function isReplyFinalStatus($status)
    {
        return !in_array($status, [
            self::REPLY_CALL_ING
        ]);
    }

    /**
     * 判断通话是否成功接通
     * @param string $status
     * @return bool
     */
    public static function isCallSuccess($status)
    {
        return $status === self::REPLY_ANSWER;
    }
}
