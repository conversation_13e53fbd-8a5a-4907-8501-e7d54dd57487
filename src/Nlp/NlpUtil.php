<?php

namespace Yanqu\YanquPhplib\Nlp;

class NlpUtil
{
    /**
     * 分词
     * @param string $text 需要分的词
     * @param string $outType 0：大粒度，1：中粒度（默认），2：小粒度
     * @return array<array{
     *      id: string,
     *      word: string,
     *      tags: string[]
     *  }>
     */
    public static function getTextSegments($text,$outType = "0") {
        $secret = "******************************";
        $endPoint = "https://alinlp.cn-hangzhou.aliyuncs.com";
        $params = [
            'SignatureVersion' => '1.0',
            'SignatureMethod' => 'HMAC-SHA1',
            'SignatureNonce' => uniqid(),
            'Timestamp' => date(\DateTime::ATOM, time() - 8 * 3600),
            'Version' => '2020-06-29',
            'AccessKeyId' => 'LTAI5tFrEhhu7ZxLhGaxzRmA',
            'Action' => 'GetWsChGeneral',
            'ServiceCode' => 'alinlp',
            'Text' => $text,
            'TokenizerId' => 'GENERAL_CHN',
            'Format' => 'JSON',
            'OutType' => $outType
        ];
        ksort($params);
        $str = "GET&%2F&" . urlencode(http_build_query($params));
        $sign = base64_encode(hash_hmac("sha1", $str, $secret . "&", true));
        $params["Signature"] = $sign;
        $result = file_get_contents($endPoint . "?" . http_build_query($params));
        $segmentRes = json_decode(json_decode($result, 1)['Data'], 1)['result'];
        return empty($segmentRes) ? [] : $segmentRes;
    }
}
