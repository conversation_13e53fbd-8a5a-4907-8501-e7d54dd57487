<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2020 - 2020 (http://www.shiyanjia.com)
 * <AUTHOR> 2023/9/6 10:07
 */

namespace Yanqu\YanquPhplib\YqLog;

use Yanqu\YanquPhplib\Configuration;
use Yanqu\YanquPhplib\Utils\HttpUtils;
use Yanqu\YanquPhplib\Utils\StringUtils;
use Yanqu\YanquPhplib\YqLog\driver\LogDriver;
use Yanqu\YanquPhplib\YqLog\driver\YqLogDriver;
use Yanqu\YanquPhplib\YqLog\formatter\LogFormatter;
use Yanqu\YanquPhplib\YqLog\formatter\YqLogFormatter;

class YqLog
{
    /**
     * 日志实例
     * @var array
     */
    private static $instances = [];

    /**
     * 日志模块名称
     * @var $module
     */
    private $module;

    /**
     * @var YqLogDriver $driver
     */
    private $driver;

    /**
     * @var YqLogFormatter $formatter
     */
    private $formatter;

    /**
     * 跟踪记录id
     * @var $traceId;
     */
    private static $traceId;

    private function __construct($moduleName)
    {
        $this->module = $moduleName;

        $this->driver    = LogDriver::create(Configuration::getInstance()->getDriver());
        $this->formatter = LogFormatter::create(Configuration::getInstance()->getFormatter());
    }

    private function __clone()
    {
    }

    public static function logger($moduleName = YqLogModules::DEFAULT_MODULE)
    {
        $instances = self::$instances;
        if (isset($instances[$moduleName])) {
            return $instances[$moduleName];
        }
        self::$instances[$moduleName] = new self($moduleName);

        return self::$instances[$moduleName];
    }

    /**
     * ERROR 日志记录
     * @param $message
     * @param array $context
     */
    public function error($message, array $context = [])
    {
        $this->log(__FUNCTION__, $message, $context);
    }

    /**
     * Warning 日志记录
     * @param $message
     * @param array $context
     */
    public function warning($message, array $context = [])
    {
        $this->log(__FUNCTION__, $message, $context);
    }

    /**
     * Info 日志记录
     * @param $message
     * @param array $context
     */
    public function info($message, array $context = [])
    {
        $this->log(__FUNCTION__, $message, $context);
    }

    /**
     * Debug 日志记录
     * @param $message
     * @param array $context
     */
    public function debug($message, array $context = [])
    {
        // 生产环境不记录 debug 信息
        if (Configuration::getInstance()->getEnv() == Configuration::ENV_PROD) return;
        $this->log(__FUNCTION__, $message, $context);
    }

    private function log($level = "info", $message, array $context = [])
    {
        // 获取日志 trace 信息
        $trace         = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
        $backtraceData = $trace[1];
        $traceInfo     = [
            "file" => $backtraceData["file"],
            "line" => $backtraceData["line"]
        ];
        // 日志信息的统一处理
        $logMessage = new YqLogMessage();
        if ($message instanceof \Exception) {
            // 异常信息的 message 处理
            $logMessage->setException(get_class($message));
            $logMessage->setFile($message->getFile());
            $logMessage->setLine($message->getLine());
            $logMessage->setMessage($message->getMessage());
            $logMessage->setTrace($message->getTraceAsString());
        } else {
            $logMessage->setMessage($message);
            $logMessage->setFile($traceInfo["file"]);
            $logMessage->setLine($traceInfo["line"]);
        }
        //context中添加ua 和 remote addr 和xff ，url 从$_SERVER取，需要兼容php5
        $ua = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
        $remoteAddr = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '';
        $xff = isset($_SERVER['HTTP_X_FORWARDED_FOR']) ? $_SERVER['HTTP_X_FORWARDED_FOR'] : '';
        $context['_ua'] = $ua;
        $context['_remote_addr'] = $remoteAddr;
        $context['_xff'] = $xff;
        $context['_url'] = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';

        $attts = new YqLogAttrs();
        $attts->setDate(date("Y-m-d H:i:s"));
        $attts->setLevel($level);
        $attts->setModule($this->module);
        $attts->setMessage($logMessage);
        $attts->setContext($context);
        $attts->setTrace($traceInfo);
        $attts->setTraceId(self::getTraceId());
        $this->formatter->setAttrs($attts);

        return $this->driver->save($this->formatter);
    }

    public static function getTraceId()
    {
        if (!self::$traceId) {
            self::$traceId = HttpUtils::getHeader(HttpUtils::TraceIdHeader);
            if (!self::$traceId) {
                self::$traceId = StringUtils::generateUuid();
            }
        }
        return self::$traceId;
    }
}