<?php

namespace Yanqu\YanquPhplib\Openapi\Activity\Constants\OrderGift;

use Yanqu\YanquPhplib\Openapi\Activity\Constants\ActivityPrize\PrizeTypeConstant as ActivityPrizeTypeConstant;

class PrizeTypeConstant
{
    /**
     * 实物奖品
     */
    const GOODS = 1;

    /**
     * 卡密类奖品
     */
    const CARD = 2;

    /**
     * 订单等额积分
     */
    const ORDER_POINTS = 3;

    const ACTIVITY_PRIZE_TYPE_TRANSFER = [
        self::GOODS => ActivityPrizeTypeConstant::GOODS,
        self::CARD => ActivityPrizeTypeConstant::CARD,
        self::ORDER_POINTS => ActivityPrizeTypeConstant::ORDER_POINTS,
    ];
} 