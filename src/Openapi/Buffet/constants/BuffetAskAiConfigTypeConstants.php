<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2024 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/4/25 15:01
 */
namespace Yanqu\YanquPhplib\Openapi\Buffet\constants;

class BuffetAskAiConfigTypeConstants
{
    // 配置类型：测试目的
    const TYPE_TEST_PURPOSE = 1;

    // 配置类型：样品信息
    const TYPE_SAMPLE_INFO = 2;

    // 配置类型：实验参数
    const TYPE_TEST_PARAMS = 3;

    // 配置类型：结果服务
    const TYPE_RESULT_SERVICE = 4;
}