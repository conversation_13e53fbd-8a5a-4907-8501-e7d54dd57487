<?php

namespace Yanqu\YanquPhplib\Openapi\Account;

use Yanqu\YanquPhplib\CheckIp\CheckIpUtil;
use Yanqu\YanquPhplib\Curl\CurlUtil;
use Yanqu\YanquPhplib\Configuration;

class AchievementClient
{
    /**
     * 查用户绩效对应的品牌经理
     * @param GetAchievementHeadParam $param
     * @return int
     * @throws \Yanqu\YanquPhplib\Exception\CurlException
     */
    public static function getAchievementHead(GetAchievementHeadParam $param) {
        $url = Configuration::getInstance()->get('openapi_url') . "/account/getAchievementsHead";
        $requestArray = $param->generateRequestArray();
        //如果没有传ip则加一个
        if(empty($requestArray["postip"])) {
            $requestArray["postip"] = CheckIpUtil::getIP();
        }
        $resJson = CurlUtil::postForm($url, $requestArray);
        $res = json_decode($resJson, true);
        if (is_numeric($res["data"]["saccountid"])) {
            return $res["data"]["saccountid"];
        } else {
            throw new \Exception($res['status_msg']);
        }
    }

    public static function batchGetAchievementHead(GetAchievementHeadParam $param)
    {
        $requestArray = $param->generateRequestArray();
        $url = Configuration::getInstance()->get('openapi_url') . "/account/batchGetAchievementsHead";
        //如果没有传ip则加一个
        if (empty($requestArray["postip"])) {
            $requestArray["postip"] = CheckIpUtil::getIP();
        }
        $resJson = CurlUtil::postForm($url, $requestArray);
        $res = json_decode($resJson, true);
        if (!empty($res["data"]["crm_account_ids"])) {
            return $res["data"]["crm_account_ids"];
        } else {
            throw new \Exception($res['status_msg']);
        }
    }
}
