<?php

namespace Yanqu\YanquPhplib\Openapi\Account;

use Yanqu\YanquPhplib\Curl\CurlUtil;
use Yanqu\YanquPhplib\Configuration;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\Openapi\Account\CheckNewEnergyCompanyAccountParam;

class AccountClient
{
    public static function checkNewEnergyCompanyAccount(CheckNewEnergyCompanyAccountParam $param) {
        $url = Configuration::getInstance()->get('openapi_url') . '/account/check-new-energy-company-account';
        $resJson = CurlUtil::get($url, $param->generateRequestArray());

        $res = json_decode($resJson, true);
        if ($res['status_code'] == 0) {
            return $res['data'];
        } else {
            throw new \Exception($res['status_msg']);
        }
    }


    /**
     * @return bool
     */
    public static function openFakeCard(OpenFakeCardParam $param)
    {
        $url = Configuration::getInstance()->get('openapi_url') . '/card/open-fake-card';
        try {
            $resJson = CurlUtil::postForm($url, $param->generateRequestArray());
            $res = json_decode($resJson, true);
            if ($res['status_code'] == 0) {
                return true;
            } else {
                return false;
            }
        } catch (CurlException $e) {
            return false;
        }
    }
}
