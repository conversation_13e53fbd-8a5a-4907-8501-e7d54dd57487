<?php

namespace Yanqu\YanquPhplib\Openapi\TicketPm\Constants;

class TicketPmTypeConstants {

    /**
     * 工单类型 争议单(异议单)
     */
    const TYPE_DISSENT = 0;

    /**
     * 工单类型 投诉单
     */
    const TYPE_COMPLAINT = 1;

    /**
     * 工单类型 差评单
     */
    const TYPE_BAD = 2;

    const MAP = [
        self::TYPE_DISSENT   => '争议单',
        self::TYPE_COMPLAINT => '投诉单',
        self::TYPE_BAD       => '差评单',
    ];

    const TYPE_DESC_MAP = [
        self::TYPE_DISSENT   => '争议工单',
        self::TYPE_COMPLAINT => '投诉工单',
        self::TYPE_BAD       => '差评工单',
    ];
}