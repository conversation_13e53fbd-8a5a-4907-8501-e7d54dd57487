<?php

namespace Yanqu\YanquPhplib\Openapi\OrderSampling\Param;

class UpdateSamplingInfoParam {


    private $oid;

    private $remindDay;

    private $accountId;

    private $deliverDay;

    private $deliverId;

    private $samplingId = 0;

    private $province;

    private $city;

    private $deliverConsignee;

    private $deliverMobile;

    private $universityCode;

    private $buildingId;

    private $campusId;

    private $louzhuang;

    private $louceng;

    private $loushi;

    private $operateFrom;

    private $operatorId;

    private $linkId;

    private $number;

    private $reason;

    public function setOid($oid) {
        $this->oid = $oid;
        return $this;
    }

    public function setRemindDay($remindDay) {
        $this->remindDay = $remindDay;
        return $this;
    }

    public function setAccountId($accountId) {
        $this->accountId = $accountId;
        return $this;
    }

    public function setDeliverDay($deliverDay) {
        $this->deliverDay = $deliverDay;
        return $this;
    }

    public function setDeliverId($deliverId) {
        $this->deliverId = $deliverId;
        return $this;
    }

    public function setSamplingId($samplingId) {
        $this->samplingId = $samplingId;
        return $this;
    }

    public function setProvince($province) {
        $this->province = $province;
        return $this;
    }

    public function setCity($city) {
        $this->city = $city;
        return $this;
    }

    public function setDeliverConsignee($deliverConsignee) {
        $this->deliverConsignee = $deliverConsignee;
        return $this;
    }

    public function setDeliverMobile($deliverMobile) {
        $this->deliverMobile = $deliverMobile;
        return $this;
    }

    public function setUniversityCode($universityCode) {
        $this->universityCode = $universityCode;
        return $this;
    }

    public function setBuildingId($buildingId) {
        $this->buildingId = $buildingId;
        return $this;
    }

    public function setCampusId($campusId) {
        $this->campusId = $campusId;
        return $this;
    }

    public function setLouzhuang($louzhuang) {
        $this->louzhuang = $louzhuang;
        return $this;
    }

    public function setLouceng($louceng) {
        $this->louceng = $louceng;
        return $this;
    }

    public function setLoushi($loushi) {
        $this->loushi = $loushi;
        return $this;
    }

    public function setOperateFrom($operateFrom) {
        $this->operateFrom = $operateFrom;
        return $this;
    }

    public function setOperatorId($operatorId) {
        $this->operatorId = $operatorId;
        return $this;
    }

    public function getOid() {
        return $this->oid;
    }

    public function getRemindDay() {
        return $this->remindDay;
    }

    public function getAccountId() {
        return $this->accountId;
    }

    public function getDeliverDay() {
        return $this->deliverDay;
    }

    public function getDeliverId() {
        return $this->deliverId;
    }

    public function getSamplingId() {
        return $this->samplingId;
    }

    public function getProvince() {
        return $this->province;
    }

    public function getCity() {
        return $this->city;
    }

    public function getDeliverConsignee() {
        return $this->deliverConsignee;
    }

    public function getDeliverMobile() {
        return $this->deliverMobile;
    }

    public function getUniversityCode() {
        return $this->universityCode;
    }

    public function getBuildingId() {
        return $this->buildingId;
    }

    public function getCampusId() {
        return $this->campusId;
    }

    public function getLouzhuang() {
        return $this->louzhuang;
    }

    public function getLouceng() {
        return $this->louceng;
    }

    public function getLoushi() {
        return $this->loushi;
    }

    public function getOperateFrom() {
        return $this->operateFrom;
    }

    public function getOperatorId() {
        return $this->operatorId;
    }

    public function getLinkId() {
        return $this->linkId;
    }

    public function setLinkId($linkId) {
        $this->linkId = $linkId;
        return $this;
    }

    public function getNumber() {
        return $this->number;
    }

    public function setNumber($number) {
        $this->number = $number;
        return $this;
    }
    public function getReason() {
        return $this->reason;
    }

    public function setReason($reason) {
        $this->reason = $reason;
        return $this;
    }
}