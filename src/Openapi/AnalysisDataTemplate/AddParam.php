<?php

namespace Yanqu\YanquPhplib\Openapi\AnalysisDataTemplate;

use Yanqu\YanquPhplib\Openapi\BaseParam;

class AddParam extends BaseParam
{
    public function rules()
    {
        return [
            "title" => "required|string",
            "provider_product_id"=>"required|integer|min:1",
            "template_type" => "required|integer|in:1,2",
            "provider_merchant_id" => "integer|min:1",
            "operator_type" => "required|integer|in:1,2,3",
            "operator_id" => "required|integer|min:1",
        ];
    }
}
