<?php

namespace Yanqu\YanquPhplib\Openapi\TypicalCase;

use Yanqu\YanquPhplib\Openapi\OpenapiRequestTrait;

class TypicalCaseClient
{
    use OpenapiRequestTrait;

    public static function getSampleCategory($buffetId)
    {
        $method = "GET";
        $url = "/typical-case-library/case/sample-category";
        $param = ['buffet_id' => $buffetId];
        return (new self())->requestOpenApi($method, $url, $param);
    }

    public static function getOrderBuffet($keyword = '')
    {
        $method = "GET";
        $url = "/typical-case-library/case/order-buffet";
        $param = ['keyword' => $keyword];
        return (new self())->requestOpenApi($method, $url, $param);
    }

    public static function getOrderInfo($osn, $providerMerchantId = 0)
    {
        $method = "GET";
        $url = "/typical-case-library/case/order-info";
        $param = ['osn' => $osn, 'provider_merchant_id' => $providerMerchantId];
        return (new self())->requestOpenApi($method, $url, $param);
    }

    public static function getOrderSampleIngredient($oid, $sampleGroup)
    {
        $method = "GET";
        $url = "/typical-case-library/case/order-sample-ingredient";
        $param = ['oid' => $oid, 'sample_group' => $sampleGroup];
        return (new self())->requestOpenApi($method, $url, $param);
    }

    public static function guessSampleCategory($sampleIngredient, $buffetId)
    {
        $method = "GET";
        $url = "/typical-case-library/case/guess-sample-category";
        $param = ['sample_ingredient' => $sampleIngredient, 'buffet_id' => $buffetId];
        return (new self())->requestOpenApi($method, $url, $param, [], [CURLOPT_TIMEOUT => 10]);
    }

    public static function inviteCoWorker($param)
    {
        $method = "POST";
        $url = "/typical-case-library/case/invite-co-worker";
        return (new self())->requestOpenApi($method, $url, $param);
    }

    public static function getAbnormal($param)
    {
        $method = "GET";
        $url = "/typical-case-library/case/abnormal";
        return (new self())->requestOpenApi($method, $url, $param);
    }
}