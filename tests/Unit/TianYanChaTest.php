<?php

use App\Constants\FinanceTfnInfoConstants;
use App\Utils\TianYanCha\TianYanChaUtil;

class TianYanChaTest extends \Tests\TestCase
{
    public function testQueryPrefix()
    {
        $tianYanChaUtil = app(TianYanChaUtil::class);
        $res = $tianYanChaUtil->queryPrefix('浙江大学');

        $this->assertArrayHasKey('error_code',$res);
    }

    public function testQueryNormalCorporation()
    {
        $tianYanChaUtil = app(TianYanChaUtil::class);
        $res = $tianYanChaUtil->queryNormalCorporation('2353059381');

        $this->assertArrayHasKey('error_code',$res);
    }

    public function testQuerySpecialCorporation()
    {
        $tianYanChaUtil = app(TianYanChaUtil::class);
        $res = $tianYanChaUtil->querySpecialCorporation('3225457563');

        $this->assertArrayHasKey('error_code',$res);
    }

    public function testTitleResultPo()
    {
        $vo = new \App\Entities\TitleTaxResultVo();
        $vo->title = '12';

        $this->assertEquals($vo['title'],'12');
        $this->assertEmpty($vo['name']);
        $vo['address'] = '123';
        $this->assertEquals($vo->address,'123');
    }


    public function testSearchNormalCompany()
    {
        $res = app(\App\Services\TaxNum\TaxNumService::class)->queryInfo('杭州研趣信息技术有限公司',
            FinanceTfnInfoConstants::SEARCH_LEVEL_HARD);
        $this->assertIsObject($res);
        $this->assertEquals($res->taxNumber,'91330106321921521U');
    }

    public function testSearchSchool()
    {
        $res = app(\App\Services\TaxNum\TaxNumService::class)->queryInfo('浙江大学',
            FinanceTfnInfoConstants::SEARCH_LEVEL_HARD);
        $this->assertIsObject($res);
        $this->assertEquals($res->taxNumber,'12100000470095016Q');
    }
}