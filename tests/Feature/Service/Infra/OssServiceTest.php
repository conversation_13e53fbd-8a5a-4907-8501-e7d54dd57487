<?php

namespace Tests\Feature\Service\Infra;

use App\Services\infra\OssService;
use Tests\TestCase;

class OssServiceTest extends TestCase {

    public function testGetSignUrl() {
        $url = app(OssService::class)->getSignUrl(
            'pao-ding-report',
            'standard_report/test/20240827/1828384209933303808/SCCYQ20240827184900RPT.pdf',
            3600,
            true
        );
        $this->assertIsString($url);
    }

    public function testGetObjectByUrl() {
        $bucketAndObject = app(OssService::class)->getBucketAndObjectByUrl(
            'https://shiyanjia-reports.oss-accelerate.aliyuncs.com/test.jpg'
        );
        $this->assertEquals([
            'shiyanjia-reports',
            'test.jpg'
        ],$bucketAndObject);
    }

    public function testGetObjectInfoByUrl() {
        $objectInfo = app(OssService::class)->getObjectInfoByUrl(
            'https://supplierreports.oss-cn-hangzhou.aliyuncs.com/2218/2410234387-241104+TEM.rar'
        );
        dump($objectInfo);
        $this->assertIsArray($objectInfo);
    }

    public function testRestore() {
        $objectInfo = app(OssService::class)->restoreByUrl(
            'https://supplierreports.oss-cn-hangzhou.aliyuncs.com/2218/2410234387-241104+TEM.rar'
        );
        dump($objectInfo);
        $this->assertIsArray($objectInfo);
    }
}