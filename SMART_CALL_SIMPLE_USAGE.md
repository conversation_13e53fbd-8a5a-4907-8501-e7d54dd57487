# 智能外呼系统使用说明（简化版）

## 概述

本项目集成了智能外呼系统功能，主要包含：
1. **IP校验和白名单限制**
2. **发布任务功能**（根据接口文档实现）
3. **客户导入功能**

## 环境要求

- PHP 7.1.3+
- Laravel 5.x+
- yanqu-phplib（包含智能外呼通用组件）

## 配置

### 环境变量配置

在 `.env` 文件中添加：

```env
# 智能外呼系统配置
SMART_CALL_URL=https://b.163.com/open/api
SMART_CALL_APP_KEY=your_app_key_here
SMART_CALL_APP_SECRET=your_app_secret_here
SMART_CALL_APP_TYPE=your_channel_tag
```

## 权限控制

### IP校验
- 所有请求都需要通过IP白名单校验
- 使用 `checkip` 中间件自动处理

### 环境和用户白名单
- **生产环境**：只需要IP校验即可访问
- **测试环境**：需要额外校验用户是否在白名单中（需要传递 `account_id`）
- **其他环境**：不允许访问

## API接口

### 1. 创建/更新外呼任务

**接口地址：** `POST /api/smart-call/task/save`

**请求参数：**

| 参数名称 | 类型 | 必填 | 描述 |
|---------|------|------|------|
| taskId | Long | 否 | 任务id，更新任务时必填，创建不填 |
| taskName | String | 是 | 任务名称，最大长度64个字符 |
| botId | Long | 是 | 任务关联的话术id |
| didNumbers | Array | 是 | 线路号码列表 |
| robotSeat | Integer | 是 | 运行任务需要的坐席数 |
| recall | Integer | 是 | 自动重播开关。0-关闭，1-开启 |
| account_id | Integer | 否 | 用户ID（测试环境必填） |

**请求示例：**

```json
{
    "taskName": "测试外呼任务",
    "botId": 123,
    "didNumbers": ["************"],
    "robotSeat": 1,
    "recall": 0,
    "account_id": 12345
}
```

**定时任务示例：**

```json
{
    "taskName": "定时外呼任务",
    "botId": 1195,
    "didNumbers": ["************"],
    "robotSeat": 1,
    "recall": 0,
    "startType": 1,
    "executeBeginTime": *************,
    "executeEndTime": *************,
    "cycle": [1,2,3,4,5],
    "executeTimeInterval": [
        {
            "startTime": "09:00:00",
            "endTime": "20:00:00"
        }
    ],
    "account_id": 12345
}
```

### 2. 导入客户信息

**接口地址：** `POST /api/smart-call/customers/import`

**请求参数：**

```json
{
    "customers": [
        {
            "name": "张三",
            "phone": "***********",
            "task_id": "task_123"
        }
    ],
    "account_id": 12345,
    "encrypt_key": "****************"
}
```

### 3. 获取环境信息

**接口地址：** `GET /api/smart-call/environment/info`

用于调试和确认当前环境状态。

## 使用示例

### PHP代码示例

```php
use App\Services\SmartCall\SmartCallService;

$service = new SmartCallService();

// 创建外呼任务
$taskData = [
    'taskName' => '测试外呼任务',
    'botId' => 123,
    'didNumbers' => ['************'],
    'robotSeat' => 1,
    'recall' => 0
];

try {
    $result = $service->saveTask($taskData, 12345); // 测试环境需要account_id
    echo "任务创建成功，ID: " . $result['taskId'];
} catch (\Exception $e) {
    echo "任务创建失败: " . $e->getMessage();
}

// 导入客户
$customers = [
    [
        'name' => '张三',
        'phone' => '***********',
        'task_id' => 'task_123'
    ]
];

try {
    $result = $service->importCustomers($customers, 12345);
    echo "客户导入成功";
} catch (\Exception $e) {
    echo "客户导入失败: " . $e->getMessage();
}
```

### cURL示例

```bash
# 创建外呼任务
curl -X POST "https://your-domain.com/api/smart-call/task/save" \
  -H "Content-Type: application/json" \
  -d '{
    "taskName": "测试外呼任务",
    "botId": 123,
    "didNumbers": ["************"],
    "robotSeat": 1,
    "recall": 0,
    "account_id": 12345
  }'

# 导入客户
curl -X POST "https://your-domain.com/api/smart-call/customers/import" \
  -H "Content-Type: application/json" \
  -d '{
    "customers": [
      {
        "name": "张三",
        "phone": "***********",
        "task_id": "task_123"
      }
    ],
    "account_id": 12345
  }'

# 获取环境信息
curl -X GET "https://your-domain.com/api/smart-call/environment/info"
```

## 错误处理

### 常见错误

1. **IP校验失败**
   ```json
   {
       "status_code": 1001,
       "status_msg": "IP地址不在白名单中",
       "data": []
   }
   ```

2. **用户白名单校验失败**（测试环境）
   ```json
   {
       "status_code": 1001,
       "status_msg": "用户不在测试环境白名单中",
       "data": []
   }
   ```

3. **参数验证失败**
   ```json
   {
       "status_code": 1001,
       "status_msg": "taskName字段是必填的",
       "data": []
   }
   ```

## 注意事项

1. **环境差异**：
   - 生产环境：只需要IP校验
   - 测试环境：需要传递 `account_id` 并在白名单中

2. **参数要求**：
   - 测试环境的所有接口都建议传递 `account_id` 参数
   - 生产环境可选传递 `account_id`

3. **任务限制**：
   - 单个apiKey可创建的任务上限为50000
   - 已删除的任务不参与统计

4. **定时任务**：
   - 当 `startType=1` 时，必须填写执行时间相关参数
   - 时间戳使用毫秒格式

## 文件结构

```
app/Services/SmartCall/
└── SmartCallService.php              # 业务服务类

app/Http/Controllers/
└── SmartCallController.php           # 控制器

routes/api.php                        # 路由配置
config/constants.php                  # 配置文件
```

## 依赖

确保 `yanqu-phplib` 包含以下组件：
- `SmartCall/Auth/SmartCallAuth` - 认证和签名
- `SmartCall/Crypto/SmartCallAESUtil` - AES加密
- `SmartCall/Client/SmartCallClient` - HTTP客户端

## 版本信息

- 版本：v1.0.0
- PHP版本：7.1.3+
- 创建日期：2025-07-10
- 功能：IP校验、白名单限制、发布任务
