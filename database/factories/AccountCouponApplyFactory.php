<?php

use App\Models\AccountCouponApply;
use Faker\Generator as Faker;


/** @var \Illuminate\Database\Eloquent\Factory $factory */
$factory->define(
    AccountCouponApply::class,
    function () {
        //faker设置中文
        $faker = \Faker\Factory::create('zh_CN');
        return [
            'applyno' => $faker->unique()->regexify('[A-Za-z0-9]{30}'),
            'applyway' => $faker->randomElement(['online', 'offline']),
            'preaccount' => $faker->numberBetween(0, 1),
            'ticketinfoid' => $faker->numberBetween(1, 100),
            'accountid' => $faker->numberBetween(1, 100),
            'addressid' => $faker->numberBetween(1, 100),
            'applyamount' => $faker->randomFloat(2, 0, 10000),
            'ticketamount' => $faker->randomFloat(2, 0, 10000),
            'ticketamount_cash' => $faker->randomFloat(2, 0, 10000),
            'consignee' => $faker->name,
            'mobile' => $faker->phoneNumber,
            'email' => $faker->email,
            'ticketype' => $faker->numberBetween(0, 3),
            'needreport' => $faker->randomElement(['1,2,3', '1,2', '1,3', '2,3', '1', '2', '3']),
            'title' => $faker->name,
            'registrationo' => $faker->name,
            'vals' => $faker->text(255),
            'province' => $faker->numberBetween(1, 100),
            'city' => $faker->numberBetween(1, 100),
            'area' => $faker->numberBetween(1, 100),
            'address' => $faker->address,
            'submitnote' => $faker->text(2000),
            'ticketjson' => $faker->text(1000),
            'financenote' => $faker->text(255),
            'paytime' => $faker->unixTime,
            'payno' => $faker->text(255),
            'payway' => $faker->randomElement(['alipay', 'weixin', 'chinapay']),
            'paystatus' => $faker->numberBetween(1, 100),
            'saccountid' => $faker->numberBetween(1, 100),
            'status' => $faker->numberBetween(0, 100),
            'payment' => $faker->numberBetween(0, 1),
            'ticketstatus' => $faker->numberBetween(0, 1),
            'ticketnote' => $faker->text(255),
            'adtime' => $faker->unixTime,
            'postime' => $faker->unixTime,
            'crmsubaccountid' => $faker->numberBetween(1, 100),
            'receivemobile' => $faker->phoneNumber,
            'isvoid' => $faker->numberBetween(0, 1),
            'autostatus' => $faker->numberBetween(1, 2),
            'is_need_detail' => $faker->numberBetween(0, 1),
            'givetype' => $faker->numberBetween(1, 3),
            'numlabcoat' => $faker->numberBetween(1, 100),
            'sendcoat' => $faker->numberBetween(0, 1),
            'real_user' => $faker->name,
            'real_mobile' => $faker->phoneNumber,
            'invoiceamount' => $faker->randomFloat(2, 0, 10000),
            'currencycode' => $faker->currencyCode,
            'exchangerate' => '0',
            'invalid_time' => $faker->unixTime,
            'coupon_type' => $faker->numberBetween(0, 1),
            'next_follow_up_time' => $faker->dateTime,
            'next_follow_up_account' => $faker->numberBetween(1, 100),
            'groupid' => $faker->numberBetween(1, 100),
            'without_achievement' => $faker->numberBetween(0, 1),
        ];
    }
);
