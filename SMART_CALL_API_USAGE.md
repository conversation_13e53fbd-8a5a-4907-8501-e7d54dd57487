# 智能外呼系统API使用说明

## 概述

本文档描述了智能外呼系统的API接口使用方法，包括任务管理、客户管理、资源管理等功能。

## 环境配置

### 1. 环境判断逻辑

系统根据 `.config.ini` 文件中的 `env` 参数判断当前环境：

- **生产环境** (`env=prod`): 直接通过，无需额外校验
- **测试环境** (`env=test`): 需要校验用户是否在白名单中，并过滤客户手机号
- **开发环境** (`env=dev`): 需要校验用户是否在白名单中

### 2. 白名单机制

- **IP白名单**: 所有请求都需要通过IP白名单校验
- **用户白名单**: 测试环境下需要校验用户ID是否在白名单中
- **手机号白名单**: 测试环境下导入客户时，只保留白名单中的手机号

## API接口

### 1. 任务管理

#### 创建/更新外呼任务

```http
POST /api/smart-call/task/save
```

**请求参数**:

必填参数：
- `taskName`: 任务名称（最多64个字符）
- `botId`: 话术ID
- `didNumbers`: 线路号码列表
- `robotSeat`: 运行任务需要的坐席数（最小1）
- `recall`: 自动重播开关（0-关闭，1-开启）

可选参数：
- `account_id`: 用户ID（测试环境必填）
- `taskId`: 任务ID（更新任务时必填）
- `folderId`: 文件ID
- `lineType`: 线路策略类型（0-普通线路，1-智能外显，3-号码池）
- `linePolicyId`: 智能外显策略ID（lineType=1时必填）
- `startType`: 任务启动类型（0-手动开启，1-定时开启）
- `executeBeginTime`: 执行开始时间（定时任务必填）
- `executeEndTime`: 执行结束时间（定时任务必填）
- `cycle`: 循环周期（定时任务必填）
- `executeTimeInterval`: 执行时间间隔（定时任务必填）
- `blacklistGroups`: 黑名单分组
- `hangupSms`: 挂机短信开关（0-关闭，1-开启）
- `hangupSmsMod`: 挂机短信模式（0-基础模式，1-高级模式）
- `hangupSmsConf`: 挂机短信基础配置
- `hangupSmsAdvancedConf`: 挂机短信高级配置
- `axb`: 智能小号开关（0-关闭，1-开启）
- `flashSms`: 闪信开关（0-关闭，1-开启）
- `flashSmsConf`: 闪信配置
- `filterConf`: 拦截规则配置
- `recallConf`: 自动重播配置

**请求示例**:

```json
{
    "taskName": "测试外呼任务",
    "botId": 123,
    "didNumbers": ["************"],
    "robotSeat": 1,
    "recall": 0,
    "startType": 0,
    "account_id": 12345
}
```

**响应示例**:

```json
{
    "status_code": 200,
    "data": {
        "taskId": 123
    },
    "msg": "外呼任务保存成功"
}
```

#### 获取任务列表

```http
GET /api/smart-call/task/list
```

**查询参数**:
- `account_id`: 用户ID（测试环境必填）
- 其他查询参数...

#### 获取任务详情

```http
GET /api/smart-call/task/detail?taskId=123&account_id=12345
```

#### 启动任务

```http
POST /api/smart-call/task/start
```

**请求参数**:
- `taskId`: 任务ID（必填）
- `account_id`: 用户ID（测试环境必填）

#### 停止任务

```http
POST /api/smart-call/task/stop
```

**请求参数**:
- `taskId`: 任务ID（必填）
- `account_id`: 用户ID（测试环境必填）

#### 删除任务

```http
POST /api/smart-call/task/delete
```

**请求参数**:
- `taskId`: 任务ID（必填）
- `account_id`: 用户ID（测试环境必填）

### 2. 客户管理

#### 导入客户信息

```http
POST /api/smart-call/customers/import
```

**请求参数**:
- `customers`: 客户列表（必填）
  - `name`: 客户姓名（必填，最多100个字符）
  - `phone`: 手机号（必填，最多20个字符）
  - `task_id`: 任务ID（必填）
- `account_id`: 用户ID（测试环境必填）
- `encrypt_key`: 加密密钥（可选，16位）

**请求示例**:

```json
{
    "customers": [
        {
            "name": "张三",
            "phone": "***********",
            "task_id": "123"
        },
        {
            "name": "李四",
            "phone": "***********",
            "task_id": "123"
        }
    ],
    "account_id": 12345
}
```

**注意**: 测试环境下，系统会自动过滤非白名单手机号，只保留白名单中的客户。

### 3. 资源管理

#### 获取话术列表

```http
GET /api/smart-call/bot/list?account_id=12345
```

#### 获取线路列表

```http
GET /api/smart-call/did/list?account_id=12345
```

### 4. 工具接口

#### 获取环境信息

```http
GET /api/smart-call/environment/info
```

**响应示例**:

```json
{
    "status_code": 200,
    "data": {
        "environment": "test",
        "laravel_env": "test",
        "is_production": false,
        "is_test": true,
        "is_dev": false,
        "ip": "*************",
        "timestamp": **********,
        "config_source": "config.ini + constants.php"
    },
    "msg": "获取环境信息成功"
}
```

## 错误处理

### 常见错误码

- `1001`: IP地址不在白名单中
- `1037`: 接口调用频率超限：每秒不能超过100次
- `1038`: 接口调用频率超限：每十分钟不能超过30000次

### 错误响应格式

```json
{
    "status_code": 500,
    "data": [],
    "msg": "错误描述信息"
}
```

## 使用示例

### PHP示例

```php
<?php

// 创建外呼任务
$taskData = [
    'taskName' => '测试外呼任务',
    'botId' => 123,
    'didNumbers' => ['************'],
    'robotSeat' => 1,
    'recall' => 0,
    'startType' => 0,
    'account_id' => 12345
];

$response = file_get_contents('http://your-domain.com/api/smart-call/task/save', false, stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => json_encode($taskData)
    ]
]));

$result = json_decode($response, true);
if ($result['status_code'] == 200) {
    echo "任务创建成功，任务ID: " . $result['data']['taskId'];
} else {
    echo "任务创建失败: " . $result['msg'];
}
```

### JavaScript示例

```javascript
// 创建外呼任务
const taskData = {
    taskName: '测试外呼任务',
    botId: 123,
    didNumbers: ['************'],
    robotSeat: 1,
    recall: 0,
    startType: 0,
    account_id: 12345
};

fetch('/api/smart-call/task/save', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(taskData)
})
.then(response => response.json())
.then(result => {
    if (result.status_code === 200) {
        console.log('任务创建成功，任务ID:', result.data.taskId);
    } else {
        console.error('任务创建失败:', result.msg);
    }
});
```

## 注意事项

1. **环境区分**: 测试环境需要提供 `account_id` 参数进行白名单校验
2. **手机号过滤**: 测试环境下导入客户时，系统会自动过滤非白名单手机号
3. **频率限制**: 注意接口调用频率限制，避免触发限流
4. **参数验证**: 确保必填参数完整，参数格式正确
5. **错误处理**: 建议在客户端实现完善的错误处理机制

## 配置要求

### 环境变量

```env
# 智能外呼配置
SMART_CALL_URL=https://b.163.com/open/api/wecall
SMART_CALL_APP_KEY=your_app_key_here
SMART_CALL_APP_SECRET=your_app_secret_here
SMART_CALL_APP_TYPE=your_channel_tag
```

### .config.ini 配置

```ini
;env: 当前环境，dev-开发环境，test-测试环境，prod-生产环境
env = test

;智能外呼
smart_call_app_key = "your_app_key_here"
smart_call_app_secret = "your_app_secret_here"
smart_call_base_url = "https://b.163.com/open/api/wecall"
```

## 版本信息

- 版本：v2.0.0
- PHP版本：7.1.3+
- 更新日期：2025-07-10
- 功能：完整的网易七鱼智能外呼API支持
